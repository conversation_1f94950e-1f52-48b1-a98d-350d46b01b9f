<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:rippleColor="@color/primary_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- PreSeed Avatar -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_pre_seed_avatar"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_person"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            tools:src="@drawable/ic_person" />

        <!-- PreSeed Name -->
        <TextView
            android:id="@+id/tv_pre_seed_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tv_pre_seed_quantity"
            app:layout_constraintStart_toEndOf="@+id/iv_pre_seed_avatar"
            app:layout_constraintTop_toTopOf="@+id/iv_pre_seed_avatar"
            tools:text="张三" />

        <!-- PreSeed Quantity -->
        <TextView
            android:id="@+id/tv_pre_seed_quantity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_quantity_badge"
            android:paddingHorizontal="12dp"
            android:paddingVertical="4dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_pre_seed_avatar"
            tools:text="100株" />

        <!-- PreSeed Create Time -->
        <TextView
            android:id="@+id/tv_pre_seed_create_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_pre_seed_avatar"
            app:layout_constraintTop_toBottomOf="@+id/tv_pre_seed_name"
            tools:text="2025-01-16 10:30" />

        <!-- Status Indicator (Optional) -->
        <View
            android:id="@+id/view_status_indicator"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_status_indicator_active"
            app:layout_constraintStart_toStartOf="@+id/iv_pre_seed_avatar"
            app:layout_constraintTop_toBottomOf="@+id/iv_pre_seed_avatar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
