package com.zhmiaobang.easydianapp.databases

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.zhmiaobang.easydianapp.databases.converter.OnnxModelJsonConverter
import com.zhmiaobang.easydianapp.databases.converter.FloatArrayConverter
import com.zhmiaobang.easydianapp.databases.dao.MeasureLogDao
import com.zhmiaobang.easydianapp.databases.entity.MeasureLog

/**
 * 应用程序主数据库
 *
 * 使用 Room 数据库框架，提供本地数据存储功能
 *
 * 当前包含的表：
 * - MeasureLog: 测量日志记录表
 *
 * <AUTHOR> 4.0 sonnet
 */
@Database(
    entities = [
        MeasureLog::class
    ],
    version = 3,
    exportSchema = false
)
@TypeConverters(OnnxModelJsonConverter::class, FloatArrayConverter::class)
abstract class MyDb : RoomDatabase() {

    /**
     * 获取测量日志 DAO
     */
    abstract fun measureLogDao(): MeasureLogDao

    companion object {
        private const val DATABASE_NAME = "easy_dian_app_database"

        @Volatile
        private var INSTANCE: MyDb? = null

        /**
         * 数据库迁移：从版本1到版本2
         * 添加 conf 和 nms 字段到 measure_log 表
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 添加置信度阈值字段
                database.execSQL("ALTER TABLE measure_log ADD COLUMN conf REAL")
                // 添加NMS阈值字段
                database.execSQL("ALTER TABLE measure_log ADD COLUMN nms REAL")
                android.util.Log.d("MyDb", "数据库迁移完成：版本1 -> 版本2，添加了 conf 和 nms 字段")
            }
        }

        /**
         * 数据库迁移：从版本2到版本3
         * 添加 dimension_lengths 和 diagonal_lengths 字段到 measure_log 表
         */
        private val MIGRATION_2_3 = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 添加检测框长宽尺寸数据字段
                database.execSQL("ALTER TABLE measure_log ADD COLUMN dimension_lengths TEXT")
                // 添加检测框对角线长度数据字段
                database.execSQL("ALTER TABLE measure_log ADD COLUMN diagonal_lengths TEXT")
                android.util.Log.d("MyDb", "数据库迁移完成：版本2 -> 版本3，添加了 dimension_lengths 和 diagonal_lengths 字段")
            }
        }

        /**
         * 获取数据库实例（单例模式）
         *
         * @param context 应用程序上下文
         * @return 数据库实例
         */
        fun getDatabase(context: Context): MyDb {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    MyDb::class.java,
                    DATABASE_NAME
                )
                    .addMigrations(MIGRATION_1_2, MIGRATION_2_3)
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }

        /**
         * 数据库回调，用于初始化和升级
         */
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // 数据库创建时的初始化操作
                android.util.Log.d("MyDb", "数据库创建完成")
            }

            override fun onOpen(db: SupportSQLiteDatabase) {
                super.onOpen(db)
                // 数据库打开时的操作
                android.util.Log.d("MyDb", "数据库打开")
            }
        }

        /**
         * 清除数据库实例（用于测试或重置）
         */
        fun clearInstance() {
            INSTANCE = null
        }
    }
}
