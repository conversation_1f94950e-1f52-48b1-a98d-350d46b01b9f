<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp">

    <ImageView
        android:id="@+id/iv_error_icon"
        android:layout_width="96dp"
        android:layout_height="96dp"
        android:layout_marginBottom="16dp"
        android:alpha="0.6"
        android:src="@drawable/ic_image_error"
        app:tint="@color/error_color" />

    <TextView
        android:id="@+id/tv_error_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="加载失败"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_error_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="请检查网络连接后重试"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        android:gravity="center" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_retry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="重试"
        style="@style/Widget.Material3.Button.OutlinedButton" />

</LinearLayout>
