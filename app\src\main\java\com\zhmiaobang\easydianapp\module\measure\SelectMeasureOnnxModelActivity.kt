package com.zhmiaobang.easydianapp.module.measure

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.adapter.OnnxModelAdapter
import com.zhmiaobang.easydianapp.databinding.ActivitySelectMeasureOnnxModelBinding
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.utils.PaginationScrollListener
import com.zhmiaobang.easydianapp.viewmodel.measure.MeasureViewModel

/**
 * ONNX模型选择Activity
 *
 * 功能特性：
 * - 展示ONNX模型列表数据
 * - 支持单项选择功能
 * - 支持分页加载
 * - 选择后通过Toolbar菜单确认
 *
 * <AUTHOR> 4.0 sonnet
 */
class SelectMeasureOnnxModelActivity : BaseActivity() {

    companion object {
        private const val TAG = "SelectOnnxModelActivity"
        const val EXTRA_SELECTED_MODEL = "selected_onnx_model"
    }

    // ViewBinding
    private val binding: ActivitySelectMeasureOnnxModelBinding by lazy {
        ActivitySelectMeasureOnnxModelBinding.inflate(layoutInflater)
    }

    // ViewModel
    private val viewModel: MeasureViewModel by viewModels()

    // Adapter
    private lateinit var onnxModelAdapter: OnnxModelAdapter

    // 选中的模型
    private var selectedModel: OnnxModelJson? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initializeUI()
        setupObservers()

        Log.d(TAG, "SelectMeasureOnnxModelActivity创建完成，观察者已设置")
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()
        setupToolbar()
        setupRecyclerView()
        setupSwipeRefresh()

        loadOnnxModelData()
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()
        Log.d(TAG, "开始设置观察者")

        // 观察分页数据
        viewModel.paginatedDataObserver.observe(this) { paginatedData ->
            Log.d(TAG, "收到分页数据更新")

            try {
                val items = paginatedData.items
                val state = paginatedData.paginationState

                // 停止下拉刷新动画
                binding.swipeRefreshLayout.isRefreshing = false

                // 根据加载状态更新UI
                when (state.loadingState) {
                    LoadingState.LOADING_FIRST -> {
                        // 首次加载，显示主加载指示器
                        showLoading()
                    }
                    LoadingState.LOADING_MORE -> {
                        // 加载更多，在adapter中显示
                        hideLoading()
                        onnxModelAdapter.showLoadingMore()
                    }
                    LoadingState.ERROR -> {
                        // 加载错误
                        hideLoading()
                        if (items.isEmpty()) {
                            showError(state.errorMessage ?: "加载失败")
                        } else {
                            onnxModelAdapter.showLoadError(state.errorMessage ?: "加载失败")
                        }
                    }
                    LoadingState.NO_MORE -> {
                        // 没有更多数据
                        hideLoading()
                        onnxModelAdapter.showNoMoreData()
                    }
                    LoadingState.IDLE -> {
                        // 加载完成
                        hideLoading()
                        onnxModelAdapter.hideLoadingState()
                    }
                }

                // 更新数据显示
                if (items.isNotEmpty()) {
                    showOnnxModelList(items)
                    Log.d(TAG, "显示ONNX模型列表，总数量: ${items.size}")
                } else if (state.loadingState != LoadingState.LOADING_FIRST) {
                    showEmptyState()
                }

            } catch (e: Exception) {
                Log.e(TAG, "处理分页数据失败: ${e.message}", e)
                hideLoading()
                binding.swipeRefreshLayout.isRefreshing = false
                showError("数据处理失败，请重试")
            }
        }

        // 观察错误信息
        viewModel.errorObserver.observe(this) { errorMessage ->
            Log.e(TAG, "ViewModel错误: $errorMessage")
            hideLoading()
            showError(errorMessage)
        }

        Log.d(TAG, "观察者设置完成")
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        initToolbar(
            toolbar = binding.selectOnnxToolbar,
            title = "选择测量模型",
            showBack = true
        )
    }

    /**
     * 创建选项菜单
     */
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_select_onnx_model, menu)
        return true
    }

    /**
     * 准备选项菜单
     */
    override fun onPrepareOptionsMenu(menu: Menu): Boolean {
        // 根据是否有选中项来显示/隐藏确认按钮
        menu.findItem(R.id.action_confirm_selection)?.isVisible = (selectedModel != null)
        return super.onPrepareOptionsMenu(menu)
    }

    /**
     * 处理菜单项点击
     */
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_confirm_selection -> {
                confirmSelection()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        onnxModelAdapter = OnnxModelAdapter()
        val layoutManager = LinearLayoutManager(this)

        binding.recyclerView.apply {
            this.layoutManager = layoutManager
            adapter = onnxModelAdapter
            setHasFixedSize(true)
        }

        // 设置点击事件监听器
        onnxModelAdapter.setOnItemClickListener(object : OnnxModelAdapter.OnItemClickListener {
            override fun onItemClick(onnxModel: OnnxModelJson, position: Int) {
                Log.d(TAG, "点击ONNX模型: ${onnxModel.name}")
                // 点击处理由Adapter内部的setSelectedPosition处理
            }

            override fun onSelectionChanged(selectedModel: OnnxModelJson?) {
                Log.d(TAG, "选择状态变化: ${selectedModel?.name ?: "无选择"}")
                <EMAIL> = selectedModel
                invalidateOptionsMenu() // 刷新菜单状态
            }

            override fun onRetryClick() {
                Log.d(TAG, "点击重试按钮")
                viewModel.retryLoad()
            }
        })

        // 设置分页滚动监听器
        val scrollListener = object : PaginationScrollListener(layoutManager) {
            override fun isLoading(): Boolean {
                return viewModel.getCurrentPaginationState().isLoading
            }

            override fun isLastPage(): Boolean {
                return !viewModel.getCurrentPaginationState().hasMoreData
            }

            override fun loadMoreItems() {
                Log.d(TAG, "滚动触发加载更多")
                viewModel.loadNextPage()
            }
        }

        binding.recyclerView.addOnScrollListener(scrollListener)
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setOnRefreshListener {
            Log.d(TAG, "下拉刷新触发")
            viewModel.refresh()
        }

        // 设置刷新指示器颜色
        binding.swipeRefreshLayout.setColorSchemeResources(
            R.color.hIndex_blue,
            R.color.hIndex_green
        )
    }

    /**
     * 加载ONNX模型数据
     */
    private fun loadOnnxModelData() {
        Log.d(TAG, "开始加载ONNX模型数据")

        // 检查JWT Token
        val token = com.zhmiaobang.easydianapp.init.ConfigTools.getJwtToken()
        if (token == null) {
            Log.e(TAG, "JWT Token为空，无法发送请求")
            showError("用户未登录，请重新登录")
            return
        }

        // 检查用户信息
        val user = com.zhmiaobang.easydianapp.init.ConfigTools.getUser()
        if (user == null) {
            Log.e(TAG, "用户信息为空")
            showError("用户信息异常，请重新登录")
            return
        }

        try {
            Log.d(TAG, "开始加载第一页ONNX模型数据...")
            viewModel.loadFirstPage()
            Log.d(TAG, "ONNX模型数据请求已发送")
        } catch (e: Exception) {
            hideLoading()
            binding.swipeRefreshLayout.isRefreshing = false
            val errorMessage = ExceptionUtil.catchException(e)
            showError(errorMessage)
            Log.e(TAG, "加载ONNX模型数据失败: ${e.message}", e)
        }
    }

    /**
     * 显示ONNX模型列表
     */
    private fun showOnnxModelList(onnxModels: List<OnnxModelJson>) {
        Log.d(TAG, "显示ONNX模型列表，数量: ${onnxModels.size}")

        binding.recyclerView.visibility = View.VISIBLE
        binding.emptyStateLayout.visibility = View.GONE

        onnxModelAdapter.updateData(onnxModels)
    }

    /**
     * 显示空状态
     */
    private fun showEmptyState() {
        Log.d(TAG, "显示空状态")

        binding.recyclerView.visibility = View.GONE
        binding.emptyStateLayout.visibility = View.VISIBLE
    }

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.progressIndicator.visibility = View.VISIBLE
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.progressIndicator.visibility = View.GONE
    }

    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        showToast(message)
        showEmptyState()
    }

    /**
     * 确认选择
     */
    private fun confirmSelection() {
        val model = onnxModelAdapter.getSelectedModel()
        if (model != null) {
            Log.d(TAG, "确认选择模型: ${model.name}")

            // 创建返回结果
            val resultIntent = Intent().apply {
                putExtra(EXTRA_SELECTED_MODEL, model)
            }

            // 设置结果并关闭Activity
            setResult(Activity.RESULT_OK, resultIntent)
            finish()
        } else {
            Log.e(TAG, "未选择任何模型")
            showToast("请先选择一个模型")
        }
    }
}