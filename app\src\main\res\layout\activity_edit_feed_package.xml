<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.miacochang.feed.EditFeedPackageActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/edFeedpackage_toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_color"
            app:title="编辑饲料包"
            app:titleTextColor="@color/white"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Cover Image Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="封面图片"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:visibility="invisible" />

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/edFeedpackage_iv_cover"
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="12dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_image_placeholder"
                        app:shapeAppearanceOverlay="@style/RoundedImageView"
                        tools:src="@drawable/ic_image_placeholder" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/edFeedpackage_btn_select_cover"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="选择图片"
                        app:icon="@drawable/ic_image_placeholder" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Basic Info Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="visible"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="基本信息"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Feed Package Number (Read Only) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edFeedpackage_til_no"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="饲料包编号"
                        app:endIconMode="none">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edFeedpackage_et_no"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:enabled="false"
                            android:inputType="none"
                            tools:text="FPK001" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Feed Package Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edFeedpackage_til_name"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="饲料包名称"
                        app:endIconMode="clear_text">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edFeedpackage_et_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1"
                            tools:text="优质饲料包A" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Status Dropdown -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edFeedpackage_til_status"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="状态">

                        <AutoCompleteTextView
                            android:id="@+id/edFeedpackage_act_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            tools:text="有效" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Read Only Info Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="系统信息（只读）"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Miaochang (Read Only) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edFeedpackage_til_miaochang"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="所属苗场"
                        app:endIconMode="none">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edFeedpackage_et_miaochang"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:enabled="false"
                            android:inputType="none"
                            tools:text="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Create Time (Read Only) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edFeedpackage_til_create_time"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="创建时间"
                        app:endIconMode="none">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edFeedpackage_et_create_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:enabled="false"
                            android:inputType="none"
                            tools:text="2024-01-01 10:00:00" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Update Time (Read Only) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edFeedpackage_til_update_time"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="更新时间"
                        app:endIconMode="none">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edFeedpackage_et_update_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:enabled="false"
                            android:inputType="none"
                            tools:text="2024-01-01 10:00:00" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Save Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/edFeedpackage_btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                android:padding="16dp"
                android:text="保存"
                android:textSize="16sp"
                app:cornerRadius="8dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Loading Progress -->
    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/edFeedpackage_progress_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminate="true"
        android:visibility="gone"
        app:indicatorColor="@color/primary_color"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>