package com.zhmiaobang.easydianapp.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.RoundedCornersTransformation
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ItemLoadErrorBinding
import com.zhmiaobang.easydianapp.databinding.ItemLoadingMoreBinding
import com.zhmiaobang.easydianapp.databinding.ItemNoMoreDataBinding
import com.zhmiaobang.easydianapp.databinding.ItemOnnxModelBinding
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson
import com.zhmiaobang.easydianapp.utils.LoadingState

/**
 * ONNX模型选择适配器 - 支持单项选择和分页
 * 
 * <AUTHOR> 4.0 sonnet
 */
class OnnxModelAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_ONNX_MODEL = 0
        private const val TYPE_LOADING = 1
        private const val TYPE_ERROR = 2
        private const val TYPE_NO_MORE = 3
    }

    // 数据集合
    private val onnxModels = mutableListOf<OnnxModelJson>()
    
    // 选中状态管理
    private var selectedPosition: Int = -1 // -1 表示未选择
    
    // 加载状态
    private var loadingState: LoadingState = LoadingState.IDLE
    private var errorMessage: String? = null

    // 点击事件监听器接口
    interface OnItemClickListener {
        fun onItemClick(onnxModel: OnnxModelJson, position: Int)
        fun onSelectionChanged(selectedModel: OnnxModelJson?)
        fun onRetryClick() // 重试点击事件
    }

    private var itemClickListener: OnItemClickListener? = null

    /**
     * 设置点击事件监听器
     */
    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.itemClickListener = listener
    }

    /**
     * 更新所有数据（刷新）
     */
    fun updateData(newOnnxModels: List<OnnxModelJson>) {
        onnxModels.clear()
        onnxModels.addAll(newOnnxModels)
        loadingState = LoadingState.IDLE
        
        // 检查选中项是否仍然存在
        if (selectedPosition >= 0 && selectedPosition < onnxModels.size) {
            // 选中项仍然存在，保持选中状态
        } else {
            // 选中项不存在，清除选中状态
            selectedPosition = -1
            itemClickListener?.onSelectionChanged(null)
        }
        
        notifyDataSetChanged()
    }

    /**
     * 添加更多数据（分页加载）
     */
    fun addData(newOnnxModels: List<OnnxModelJson>) {
        val startPosition = onnxModels.size
        onnxModels.addAll(newOnnxModels)
        loadingState = LoadingState.IDLE
        notifyItemRangeInserted(startPosition, newOnnxModels.size)
    }

    /**
     * 设置选中项
     */
    fun setSelectedPosition(position: Int) {
        val oldPosition = selectedPosition
        selectedPosition = position
        
        // 刷新旧的选中项
        if (oldPosition >= 0 && oldPosition < onnxModels.size) {
            notifyItemChanged(oldPosition)
        }
        
        // 刷新新的选中项
        if (selectedPosition >= 0 && selectedPosition < onnxModels.size) {
            notifyItemChanged(selectedPosition)
            itemClickListener?.onSelectionChanged(onnxModels[selectedPosition])
        } else {
            itemClickListener?.onSelectionChanged(null)
        }
    }

    /**
     * 获取当前选中的模型
     */
    fun getSelectedModel(): OnnxModelJson? {
        return if (selectedPosition >= 0 && selectedPosition < onnxModels.size) {
            onnxModels[selectedPosition]
        } else {
            null
        }
    }

    /**
     * 显示加载更多状态
     */
    fun showLoadingMore() {
        loadingState = LoadingState.LOADING_MORE
        notifyItemChanged(itemCount - 1)
    }

    /**
     * 显示加载错误状态
     */
    fun showLoadError(message: String) {
        errorMessage = message
        loadingState = LoadingState.ERROR
        notifyItemChanged(itemCount - 1)
    }

    /**
     * 显示无更多数据状态
     */
    fun showNoMoreData() {
        loadingState = LoadingState.NO_MORE
        notifyItemChanged(itemCount - 1)
    }

    /**
     * 隐藏加载状态
     */
    fun hideLoadingState() {
        loadingState = LoadingState.IDLE
        notifyItemChanged(itemCount - 1)
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < onnxModels.size) {
            TYPE_ONNX_MODEL
        } else {
            when (loadingState) {
                LoadingState.LOADING_MORE -> TYPE_LOADING
                LoadingState.ERROR -> TYPE_ERROR
                LoadingState.NO_MORE -> TYPE_NO_MORE
                else -> TYPE_ONNX_MODEL
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_ONNX_MODEL -> {
                val binding = ItemOnnxModelBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                OnnxModelViewHolder(binding)
            }
            TYPE_LOADING -> {
                val binding = ItemLoadingMoreBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                LoadingViewHolder(binding)
            }
            TYPE_ERROR -> {
                val binding = ItemLoadErrorBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ErrorViewHolder(binding)
            }
            TYPE_NO_MORE -> {
                val binding = ItemNoMoreDataBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                NoMoreViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is OnnxModelViewHolder -> {
                if (position < onnxModels.size) {
                    holder.bind(onnxModels[position], position)
                }
            }
            is ErrorViewHolder -> {
                holder.bind(errorMessage ?: "加载失败")
            }
        }
    }

    override fun getItemCount(): Int {
        return onnxModels.size + if (shouldShowLoadingItem()) 1 else 0
    }

    /**
     * 是否应该显示加载项
     */
    private fun shouldShowLoadingItem(): Boolean {
        return loadingState != LoadingState.IDLE && onnxModels.isNotEmpty()
    }

    /**
     * ONNX模型ViewHolder
     */
    inner class OnnxModelViewHolder(
        private val binding: ItemOnnxModelBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(onnxModel: OnnxModelJson, position: Int) {
            with(binding) {
                // 绑定基本信息
                tvOnnxName.text = onnxModel.name
                tvOnnxCategory.text = onnxModel.shrimpCate.name

                // 加载图标
                loadIcon(onnxModel.icon)

                // 设置选中状态
                rbSelected.isChecked = (position == selectedPosition)

                // 设置卡片选中状态背景
                setCardSelectedState(position == selectedPosition)

                // 设置点击事件
                setupClickListeners(onnxModel, position)
            }
        }

        /**
         * 加载图标图片
         */
        private fun loadIcon(iconUrl: String?) {
            binding.ivOnnxIcon.load(iconUrl) {
                crossfade(300)
                placeholder(R.drawable.ic_image_placeholder)
                error(R.drawable.ic_image_placeholder)
                transformations(RoundedCornersTransformation(8f))
            }
        }

        /**,ch
         * 设置卡片选中状态
         */
        private fun setCardSelectedState(isSelected: Boolean) {
            val context = binding.root.context
            if (isSelected) {
                // 选中状态：改变背景色和透明度
                binding.root.setCardBackgroundColor(
                    context.getColor(R.color.hIndex_blue_light)
                )
                binding.root.alpha = 1.0f
            } else {
                // 未选中状态：默认背景
                binding.root.setCardBackgroundColor(
                    context.getColor(android.R.color.white)
                )
                binding.root.alpha = 0.9f
            }
        }

        /**
         * 设置点击事件监听器
         */
        private fun setupClickListeners(onnxModel: OnnxModelJson, position: Int) {
            with(binding) {
                // 点击整个卡片区域进行选择
                clickableArea.setOnClickListener {
                    itemClickListener?.onItemClick(onnxModel, position)
                    setSelectedPosition(position)
                }
            }
        }
    }

    /**
     * 加载更多ViewHolder
     */
    inner class LoadingViewHolder(
        private val binding: ItemLoadingMoreBinding
    ) : RecyclerView.ViewHolder(binding.root)

    /**
     * 错误ViewHolder
     */
    inner class ErrorViewHolder(
        private val binding: ItemLoadErrorBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(message: String) {
            binding.tvErrorMessage.text = message
            binding.btnRetry.setOnClickListener {
                itemClickListener?.onRetryClick()
            }
        }
    }

    /**
     * 无更多数据ViewHolder
     */
    inner class NoMoreViewHolder(
        private val binding: ItemNoMoreDataBinding
    ) : RecyclerView.ViewHolder(binding.root)
}
