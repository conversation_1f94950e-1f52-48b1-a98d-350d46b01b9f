<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/aqua_accent"
                android:endColor="@color/aqua_accent_dark"
                android:angle="90" />
            <corners android:radius="8dp" />
            <stroke
                android:width="2dp"
                android:color="@color/aqua_accent_dark" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/aqua_accent_light"
                android:endColor="@color/aqua_accent"
                android:angle="90" />
            <corners android:radius="8dp" />
            <stroke
                android:width="1dp"
                android:color="@color/aqua_accent" />
        </shape>
    </item>
    
</selector>
