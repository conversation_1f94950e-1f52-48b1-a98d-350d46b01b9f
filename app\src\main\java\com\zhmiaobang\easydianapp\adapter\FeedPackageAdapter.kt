package com.zhmiaobang.easydianapp.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.RoundedCornersTransformation
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ItemFeedPackageBinding
import com.zhmiaobang.easydianapp.databinding.ItemLoadErrorBinding
import com.zhmiaobang.easydianapp.databinding.ItemLoadingMoreBinding
import com.zhmiaobang.easydianapp.databinding.ItemNoMoreDataBinding
import com.zhmiaobang.easydianapp.json.feed.FeedPackageJson
import com.zhmiaobang.easydianapp.utils.LoadingState

/**
 * 饲料包列表适配器 - 支持分页加载
 * 用于在 FeedPackageListActivity 中展示饲料包列表
 *
 * 功能特性：
 * - 展示饲料包基本信息（编号、名称、封面、状态）
 * - 点击logo/名称跳转到编辑页面
 * - 使用Coil加载封面图片
 * - 状态指示器显示
 * - 支持分页加载和加载状态显示
 *
 * <AUTHOR> 4.0 sonnet
 */
class FeedPackageAdapter(
    private var feedPackages: MutableList<FeedPackageJson> = mutableListOf()
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "FeedPackageAdapter"

        // ViewType常量
        private const val TYPE_FEED_PACKAGE = 0
        private const val TYPE_LOADING = 1
        private const val TYPE_ERROR = 2
        private const val TYPE_NO_MORE = 3
    }

    // 加载状态
    private var loadingState: LoadingState = LoadingState.IDLE
    private var errorMessage: String? = null

    // 点击事件监听器接口
    interface OnItemClickListener {
        fun onItemClick(feedPackage: FeedPackageJson)
        fun onRetryClick() // 重试点击事件
    }

    private var itemClickListener: OnItemClickListener? = null

    /**
     * 设置点击事件监听器
     */
    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.itemClickListener = listener
    }

    /**
     * 更新所有数据（刷新）
     */
    fun updateData(newFeedPackages: List<FeedPackageJson>) {
        feedPackages.clear()
        feedPackages.addAll(newFeedPackages)
        loadingState = LoadingState.IDLE
        notifyDataSetChanged()
    }

    /**
     * 追加数据（分页加载）
     */
    fun addData(newFeedPackages: List<FeedPackageJson>) {
        val startPosition = feedPackages.size
        feedPackages.addAll(newFeedPackages)
        loadingState = LoadingState.IDLE
        notifyItemRangeInserted(startPosition, newFeedPackages.size)
    }

    /**
     * 显示加载更多状态
     */
    fun showLoadingMore() {
        if (loadingState != LoadingState.LOADING_MORE) {
            loadingState = LoadingState.LOADING_MORE
            notifyItemChanged(itemCount - 1)
        }
    }

    /**
     * 显示加载错误状态
     */
    fun showLoadError(message: String) {
        errorMessage = message
        loadingState = LoadingState.ERROR
        notifyItemChanged(itemCount - 1)
    }

    /**
     * 显示没有更多数据状态
     */
    fun showNoMoreData() {
        if (loadingState != LoadingState.NO_MORE) {
            loadingState = LoadingState.NO_MORE
            notifyItemChanged(itemCount - 1)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < feedPackages.size) {
            TYPE_FEED_PACKAGE
        } else {
            when (loadingState) {
                LoadingState.LOADING_MORE -> TYPE_LOADING
                LoadingState.ERROR -> TYPE_ERROR
                LoadingState.NO_MORE -> TYPE_NO_MORE
                else -> TYPE_FEED_PACKAGE
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_FEED_PACKAGE -> {
                val binding = ItemFeedPackageBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                FeedPackageViewHolder(binding)
            }
            TYPE_LOADING -> {
                val binding = ItemLoadingMoreBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                LoadingViewHolder(binding)
            }
            TYPE_ERROR -> {
                val binding = ItemLoadErrorBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ErrorViewHolder(binding)
            }
            TYPE_NO_MORE -> {
                val binding = ItemNoMoreDataBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                NoMoreViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is FeedPackageViewHolder -> {
                if (position < feedPackages.size) {
                    holder.bind(feedPackages[position])
                }
            }
            is ErrorViewHolder -> {
                holder.bind(errorMessage ?: "加载失败")
            }
        }
    }

    override fun getItemCount(): Int {
        return feedPackages.size + if (shouldShowLoadingItem()) 1 else 0
    }

    /**
     * 是否应该显示加载项
     */
    private fun shouldShowLoadingItem(): Boolean {
        return loadingState != LoadingState.IDLE && feedPackages.isNotEmpty()
    }

    /**
     * ViewHolder类
     */
    inner class FeedPackageViewHolder(
        private val binding: ItemFeedPackageBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(feedPackage: FeedPackageJson) {
            with(binding) {
                // 绑定基本信息
                tvNo.text = feedPackage.no
                tvName.text = feedPackage.name

                // 加载封面图片
                loadCoverImage(feedPackage.cover)

                // 设置状态指示器
                setStatusIndicator(feedPackage.status)

                // 设置点击事件
                setupClickListeners(feedPackage)
            }
        }

        /**
         * 加载封面图片
         */
        private fun loadCoverImage(coverUrl: String?) {
            binding.ivCover.load(coverUrl) {
                crossfade(300)
                placeholder(R.drawable.ic_image_placeholder)
                error(R.drawable.ic_image_placeholder)
                transformations(RoundedCornersTransformation(8f))
            }
        }

        /**
         * 设置状态指示器
         */
        private fun setStatusIndicator(status: Int) {
            val context = binding.root.context
            when (status) {
                1 -> {
                    binding.statusDot.setBackgroundResource(R.drawable.circle_shape)
                    binding.statusDot.backgroundTintList = context.getColorStateList(R.color.hIndex_green)
                    binding.tvStatus.text = "有效"
                    binding.tvStatus.setTextColor(context.getColor(R.color.hIndex_green))
                }
                0 -> {
                    binding.statusDot.setBackgroundResource(R.drawable.circle_shape)
                    binding.statusDot.backgroundTintList = context.getColorStateList(R.color.hIndex_red)
                    binding.tvStatus.text = "无效"
                    binding.tvStatus.setTextColor(context.getColor(R.color.hIndex_red))
                }
                else -> {
                    binding.statusDot.setBackgroundResource(R.drawable.circle_shape)
                    binding.statusDot.backgroundTintList = context.getColorStateList(R.color.text_hint)
                    binding.tvStatus.text = "未知"
                    binding.tvStatus.setTextColor(context.getColor(R.color.text_hint))
                }
            }
        }

        /**
         * 设置点击事件监听器
         */
        private fun setupClickListeners(feedPackage: FeedPackageJson) {
            with(binding) {
                // 点击logo/名称区域跳转到编辑页面
                clickableArea.setOnClickListener {
                    itemClickListener?.onItemClick(feedPackage)
                }
            }
        }
    }

    /**
     * 加载更多ViewHolder
     */
    inner class LoadingViewHolder(
        private val binding: ItemLoadingMoreBinding
    ) : RecyclerView.ViewHolder(binding.root)

    /**
     * 错误ViewHolder
     */
    inner class ErrorViewHolder(
        private val binding: ItemLoadErrorBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(message: String) {
            binding.tvErrorMessage.text = message
            binding.btnRetry.setOnClickListener {
                itemClickListener?.onRetryClick()
            }
        }
    }

    /**
     * 没有更多数据ViewHolder
     */
    inner class NoMoreViewHolder(
        private val binding: ItemNoMoreDataBinding
    ) : RecyclerView.ViewHolder(binding.root)
}
