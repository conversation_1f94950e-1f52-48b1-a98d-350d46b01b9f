package com.zhmiaobang.easydianapp.viewmodel.preseed

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.zhmiaobang.easydianapp.json.CommonResponseJson
import com.zhmiaobang.easydianapp.json.CommonRestfulJson
import com.zhmiaobang.easydianapp.json.preseed.PreSeedJson
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.utils.PaginatedData
import com.zhmiaobang.easydianapp.utils.PaginationState
import com.zhmiaobang.easydianapp.viewmodel.BaseViewModel

/**
 * 预播种管理相关的ViewModel
 *
 * 功能特性：
 * - 获取预播种列表（支持分页）
 * - 数据状态管理
 * - 完整的错误处理和加载状态
 *
 * <AUTHOR> 4.0 sonnet
 */
class PreSeedViewModel: BaseViewModel() {

    companion object {
        private const val TAG = "PreSeedViewModel"
    }

    // 分页数据观察者
    val paginatedDataObserver: MutableLiveData<PaginatedData<PreSeedJson>> by lazy {
        MutableLiveData<PaginatedData<PreSeedJson>>()
    }

    // 兼容旧接口的观察者
    val listObserver: MutableLiveData<CommonRestfulJson<PreSeedJson>> by lazy {
        MutableLiveData<CommonRestfulJson<PreSeedJson>>()
    }

    // 分页状态
    private var paginationState = PaginationState()

    // 所有已加载的预播种数据
    private val allPreSeeds = LinkedHashSet<PreSeedJson>()

    /**
     * 加载第一页数据（正常加载，使用缓存）
     */
    fun loadFirstPage() = launch({
        if (paginationState.isLoading) {
            Log.d(TAG, "正在加载中，忽略重复请求")
            return@launch
        }

        Log.d(TAG, "开始加载第一页数据（使用缓存）...")
        paginationState = paginationState.startFirstLoading()
        allPreSeeds.clear()

        try {
            // 正常加载使用缓存
            RetrofitClient.setUseCache(true)

            val response = RetrofitClient.apiService.get_preseed_list(page = 1)
            Log.d(TAG, "第一页加载成功: count=${response.count}, results数量=${response.results.size}")

            // 添加数据到集合（自动去重）
            allPreSeeds.addAll(response.results)

            // 更新分页状态
            val hasNext = !response.next.isNullOrEmpty()
            paginationState = paginationState.loadSuccess(hasNext, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allPreSeeds.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "第一页加载失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "加载失败")

            val paginatedData = PaginatedData(
                items = allPreSeeds.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        }
    })

    /**
     * 加载下一页数据（使用缓存）
     */
    fun loadNextPage() = launch({
        if (!paginationState.canLoadMore) {
            Log.d(TAG, "无法加载更多: isLoading=${paginationState.isLoading}, hasMore=${paginationState.hasMoreData}")
            return@launch
        }

        Log.d(TAG, "开始加载第${paginationState.currentPage}页数据（使用缓存）...")
        paginationState = paginationState.startLoadingMore()

        // 先发送加载状态
        val loadingData = PaginatedData(
            items = allPreSeeds.toList(),
            paginationState = paginationState
        )
        paginatedDataObserver.postValue(loadingData)

        try {
            // 加载更多使用缓存
            RetrofitClient.setUseCache(true)

            val response = RetrofitClient.apiService.get_preseed_list(page = paginationState.currentPage)
            Log.d(TAG, "第${paginationState.currentPage}页加载成功: count=${response.count}, results数量=${response.results.size}")

            // 添加新数据到集合（自动去重）
            allPreSeeds.addAll(response.results)

            // 更新分页状态
            val hasNext = !response.next.isNullOrEmpty()
            paginationState = paginationState.loadSuccess(hasNext, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allPreSeeds.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "第${paginationState.currentPage}页加载失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "加载失败")

            val paginatedData = PaginatedData(
                items = allPreSeeds.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        }
    })

    /**
     * 刷新数据（禁用缓存，获取最新数据）
     */
    fun refresh() = launch({
        Log.d(TAG, "刷新数据（禁用缓存）")

        try {
            // 刷新时禁用缓存，确保获取最新数据
            RetrofitClient.setUseCache(false)

            // 重置状态并重新加载
            paginationState = paginationState.reset()
            loadFirstPage()

        } catch (e: Exception) {
            Log.e(TAG, "刷新失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "刷新失败")

            val paginatedData = PaginatedData(
                items = allPreSeeds.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        } finally {
            // 恢复缓存设置
            RetrofitClient.setUseCache(true)
        }
    })

    /**
     * 重试加载（禁用缓存，获取最新数据）
     */
    fun retryLoad() {
        Log.d(TAG, "重试加载（禁用缓存）")

        try {
            // 重试时禁用缓存，确保获取最新数据
            RetrofitClient.setUseCache(false)
            loadFirstPage()
        } finally {
            // 恢复缓存设置
            RetrofitClient.setUseCache(true)
        }
    }

    /**
     * 获取当前分页状态
     */
    fun getCurrentPaginationState(): PaginationState = paginationState

    /**
     * 兼容旧接口的方法
     */
    fun get_list(page: Int = 1) = launch({
        listObserver.postValue(RetrofitClient.apiService.get_preseed_list(page))
    })


    val qrCodeObserver: MutableLiveData<CommonResponseJson<PreSeedJson>> by lazy { MutableLiveData<CommonResponseJson<PreSeedJson>>() }
    suspend fun get_preseed_qrcode()=launch({
        qrCodeObserver.postValue(RetrofitClient.apiService.get_preseed_qrcode())
    })
}