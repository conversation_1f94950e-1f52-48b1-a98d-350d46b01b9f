package com.zhmiaobang.easydianapp.json.employee

import android.os.Parcelable
import kotlinx.parcelize.Parcelize


/*
'id', 'employee', 'code', 'qrcodeString', 'qrcodeUrl', 'uuid', 'expire_time', 'createTime'
 */
@Parcelize
data class EmployeeLoginCodeJson(
    val id:Int,
    val employee:Int?=0,
    val code: String,
    val qrcodeString: String,
    val qrcodeUrl: String,
    val uuid: String?=null,
    val expire_time: String,
    val createTime: String
): Parcelable
