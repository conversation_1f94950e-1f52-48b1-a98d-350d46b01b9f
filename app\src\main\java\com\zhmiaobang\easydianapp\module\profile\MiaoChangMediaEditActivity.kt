package com.zhmiaobang.easydianapp.module.profile

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Base64
import android.util.Log
import android.util.Patterns
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityMiaoChangMediaEditBinding
import com.zhmiaobang.easydianapp.init.ConfigTools
import com.zhmiaobang.easydianapp.json.user.MiaoChangMedia
import com.zhmiaobang.easydianapp.json.user.MiaoChangMediaUpdateRequest
import com.zhmiaobang.easydianapp.json.user.Miaochang
import com.zhmiaobang.easydianapp.json.user.UserJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.viewmodel.user.UserViewModel
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream

/**
 * 苗场媒体信息编辑Activity
 *
 * 功能特性：
 * - 编辑联系人信息（姓名、电话）
 * - 管理三种图片（Logo、抖音二维码、微信二维码）
 * - 支持拍照和相册选择
 * - 图片base64编码和URL格式支持
 * - 表单验证和错误处理
 *
 * <AUTHOR> 4.0 sonnet
 */
class MiaoChangMediaEditActivity : BaseActivity() {

    companion object {
        private const val TAG = "MiaoChangMediaEdit"
        private const val MAX_IMAGE_SIZE = 2 * 1024 * 1024 // 2MB (备用限制)
        private const val MAX_BASE64_SIZE = 512 * 1024 // 512KB base64限制
        private const val IMAGE_QUALITY = 80
        private const val MIN_IMAGE_QUALITY = 30 // 最低压缩质量
        private val SUPPORTED_IMAGE_TYPES = arrayOf("image/jpeg", "image/png")
    }

    // ViewBinding
    private val binding: ActivityMiaoChangMediaEditBinding by lazy {
        ActivityMiaoChangMediaEditBinding.inflate(layoutInflater)
    }

    // ViewModels
    private val userViewModel: UserViewModel by viewModels()

    // 数据
    private var user: UserJson? = null
    private var miaochang: Miaochang? = null
    private var media: MiaoChangMedia? = null

    // 图片选择器
    private lateinit var galleryLauncher: ActivityResultLauncher<Intent>
    private var currentImageType: ImageType = ImageType.LOGO

    // 图片数据存储
    private var logoData: String? = null
    private var douyinQrData: String? = null
    private var wechatQrData: String? = null

    // 图片类型枚举
    enum class ImageType {
        LOGO, DOUYIN_QR, WECHAT_QR
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        setupUI()
        setupImagePickers()
        setupClickListeners()
        setupObservers()
    }

    /**
     * 设置UI
     */
    private fun setupUI() {
        // 设置工具栏
        binding.mediaEditToolbar.setNavigationOnClickListener {
            onBackPressed()
        }

        // 设置图片选择器标题
        setupImageSelectorTitles()
    }

    /**
     * 设置图片选择器标题
     */
    private fun setupImageSelectorTitles() {
        binding.mediaEditLogoSection.root.findViewById<TextView>(R.id.image_selector_title).text = "Logo图片"
        binding.mediaEditDouyinSection.root.findViewById<TextView>(R.id.image_selector_title).text = "抖音二维码"
        binding.mediaEditWechatSection.root.findViewById<TextView>(R.id.image_selector_title).text = "微信二维码"
    }

    /**
     * 设置图片选择器
     */
    private fun setupImagePickers() {
        // 相册启动器
        galleryLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.data?.let { uri ->
                    handleImageUri(uri)
                }
            }
        }
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // Logo图片选择器
        setupImageSelectorListeners(binding.mediaEditLogoSection.root, ImageType.LOGO)

        // 抖音二维码选择器
        setupImageSelectorListeners(binding.mediaEditDouyinSection.root, ImageType.DOUYIN_QR)

        // 微信二维码选择器
        setupImageSelectorListeners(binding.mediaEditWechatSection.root, ImageType.WECHAT_QR)

        // 保存按钮
        binding.mediaEditSaveButton.setOnClickListener {
            saveMediaInfo()
        }
    }

    /**
     * 为图片选择器设置监听器
     */
    private fun setupImageSelectorListeners(sectionView: View, imageType: ImageType) {
        val deleteBtn = sectionView.findViewById<ImageView>(R.id.image_selector_delete)
        val previewContainer = sectionView.findViewById<View>(R.id.image_selector_preview_container)

        // 删除按钮
        deleteBtn.setOnClickListener {
            showDeleteConfirmDialog(imageType)
        }

        // 预览区域点击选择图片
        previewContainer.setOnClickListener {
            currentImageType = imageType
            openGallery()
        }
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()
        userViewModel.userObserver.observe(this) { userJson ->
            user = userJson
            miaochang = user?.miaochang
            media = miaochang?.media
            bindDataToUI()
        }
    }

    /**
     * 绑定数据到UI
     */
    private fun bindDataToUI() {
        media?.let { mediaData ->
            // 绑定基本信息
            binding.mediaEditContactPerson.setText(mediaData.contactPerson ?: "")
            binding.mediaEditContactPhone.setText(mediaData.contactPhone ?: "")

            // 绑定图片数据
            loadImageData(mediaData.logo, ImageType.LOGO)
            loadImageData(mediaData.douyinQrCode, ImageType.DOUYIN_QR)
            loadImageData(mediaData.wechatQrCode, ImageType.WECHAT_QR)
        }
    }



    /**
     * 加载图片数据
     */
    private fun loadImageData(imageData: String?, imageType: ImageType) {
        if (imageData.isNullOrBlank()) return

        val sectionView = when (imageType) {
            ImageType.LOGO -> binding.mediaEditLogoSection.root
            ImageType.DOUYIN_QR -> binding.mediaEditDouyinSection.root
            ImageType.WECHAT_QR -> binding.mediaEditWechatSection.root
        }

        val previewImage = sectionView.findViewById<ImageView>(R.id.image_selector_preview)
        val emptyState = sectionView.findViewById<View>(R.id.image_selector_empty_state)
        val deleteBtn = sectionView.findViewById<ImageView>(R.id.image_selector_delete)

        // 判断是URL还是base64
        if (imageData.startsWith("http")) {
            // URL格式，使用Coil加载
            previewImage.load(imageData) {
                crossfade(true)
                placeholder(R.drawable.ic_image_placeholder)
                error(R.drawable.ic_image_placeholder)
            }
        } else {
            // base64格式，解码后显示
            try {
                val base64Data = extractBase64Data(imageData)
                val decodedBytes = Base64.decode(base64Data, Base64.NO_WRAP)
                val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
                previewImage.setImageBitmap(bitmap)
            } catch (e: Exception) {
                Log.e(TAG, "解码base64图片失败: ${e.message}")
                previewImage.setImageResource(R.drawable.ic_image_placeholder)
            }
        }

        // 显示预览，隐藏空状态
        previewImage.visibility = View.VISIBLE
        emptyState.visibility = View.GONE
        deleteBtn.visibility = View.VISIBLE

        // 保存数据
        when (imageType) {
            ImageType.LOGO -> logoData = imageData
            ImageType.DOUYIN_QR -> douyinQrData = imageData
            ImageType.WECHAT_QR -> wechatQrData = imageData
        }
    }

    /**
     * 打开相册
     */
    private fun openGallery() {
        val intent = Intent(Intent.ACTION_PICK).apply {
            type = "image/*"
            data = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            putExtra(Intent.EXTRA_MIME_TYPES, SUPPORTED_IMAGE_TYPES)
        }
        galleryLauncher.launch(intent)
    }

    /**
     * 处理图片URI
     */
    private fun handleImageUri(uri: Uri) {
        try {
            val inputStream = contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()

            if (bitmap != null) {
                handleImageResult(bitmap)
            } else {
                showToast("图片加载失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理图片URI失败: ${e.message}")
            showToast("图片处理失败")
        }
    }

    /**
     * 处理图片结果
     */
    private fun handleImageResult(bitmap: Bitmap) {
        try {
            // 显示加载状态
            showImageLoading(currentImageType, true)

            // 先进行基础尺寸压缩
            val compressedBitmap = compressImage(bitmap)

            // 转换为base64（内部会进行智能压缩）
            val base64String = bitmapToBase64(compressedBitmap)

            if (base64String != null) {
                // 更新UI
                updateImagePreview(compressedBitmap, currentImageType)

                // 保存数据
                when (currentImageType) {
                    ImageType.LOGO -> logoData = base64String
                    ImageType.DOUYIN_QR -> douyinQrData = base64String
                    ImageType.WECHAT_QR -> wechatQrData = base64String
                }

                Log.d(TAG, "图片处理成功，Base64大小: ${base64String.length}字节，格式: ${if (base64String.startsWith("data:image/")) "Data URI" else "纯Base64"}")
            } else {
                // 清除当前图片
                clearImage(currentImageType)
                Log.w(TAG, "图片处理失败，无法满足大小要求")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理图片结果失败: ${e.message}")
            showToast("图片处理失败: ${e.message}")
            // 清除当前图片
            clearImage(currentImageType)
        } finally {
            // 隐藏加载状态
            showImageLoading(currentImageType, false)
        }
    }

    /**
     * 显示/隐藏图片加载状态
     */
    private fun showImageLoading(imageType: ImageType, show: Boolean) {
        val sectionView = when (imageType) {
            ImageType.LOGO -> binding.mediaEditLogoSection.root
            ImageType.DOUYIN_QR -> binding.mediaEditDouyinSection.root
            ImageType.WECHAT_QR -> binding.mediaEditWechatSection.root
        }

        val loadingView = sectionView.findViewById<View>(R.id.image_selector_loading)
        loadingView.visibility = if (show) View.VISIBLE else View.GONE
    }

    /**
     * 压缩图片到指定尺寸
     */
    private fun compressImage(bitmap: Bitmap): Bitmap {
        val maxWidth = 800
        val maxHeight = 800

        val width = bitmap.width
        val height = bitmap.height

        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }

        val ratio = minOf(maxWidth.toFloat() / width, maxHeight.toFloat() / height)
        val newWidth = (width * ratio).toInt()
        val newHeight = (height * ratio).toInt()

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * 智能压缩图片到目标大小
     * 动态调整质量和尺寸，确保base64字符串小于512KB
     */
    private fun compressImageToTargetSize(bitmap: Bitmap): Bitmap? {
        var currentBitmap = bitmap
        var quality = IMAGE_QUALITY
        var maxSize = 800

        // 首先进行尺寸压缩
        currentBitmap = compressImageWithSize(currentBitmap, maxSize)

        // 循环压缩直到满足大小要求
        while (quality >= MIN_IMAGE_QUALITY) {
            val base64String = bitmapToBase64Internal(currentBitmap, quality)
            if (base64String != null && base64String.length <= MAX_BASE64_SIZE) {
                Log.d(TAG, "压缩成功: 质量=$quality, Base64大小=${base64String.length}字节")
                return currentBitmap
            }

            // 降低质量
            quality -= 10

            // 如果质量已经很低，尝试进一步缩小尺寸
            if (quality < MIN_IMAGE_QUALITY && maxSize > 400) {
                quality = IMAGE_QUALITY
                maxSize -= 100
                currentBitmap = compressImageWithSize(bitmap, maxSize)
                Log.d(TAG, "缩小尺寸到: ${maxSize}x${maxSize}")
            }
        }

        Log.w(TAG, "无法将图片压缩到512KB以下")
        return null
    }

    /**
     * 按指定尺寸压缩图片
     */
    private fun compressImageWithSize(bitmap: Bitmap, maxSize: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height

        if (width <= maxSize && height <= maxSize) {
            return bitmap
        }

        val ratio = minOf(maxSize.toFloat() / width, maxSize.toFloat() / height)
        val newWidth = (width * ratio).toInt()
        val newHeight = (height * ratio).toInt()

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * 从base64字符串中提取纯净的base64数据
     * 支持带MIME前缀的格式：data:image/jpeg;base64,{base64_data}
     * 也支持纯净的base64字符串
     */
    private fun extractBase64Data(base64String: String): String {
        return if (base64String.startsWith("data:image/")) {
            // 提取逗号后面的base64数据
            val commaIndex = base64String.indexOf(",")
            if (commaIndex != -1 && commaIndex < base64String.length - 1) {
                base64String.substring(commaIndex + 1)
            } else {
                base64String
            }
        } else {
            // 已经是纯净的base64字符串
            base64String
        }
    }

    /**
     * Bitmap转Base64（内部使用，指定质量）
     * 返回完整的Data URI格式：data:image/jpeg;base64,{base64_data}
     */
    private fun bitmapToBase64Internal(bitmap: Bitmap, quality: Int): String? {
        return try {
            val byteArrayOutputStream = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, byteArrayOutputStream)
            val byteArray = byteArrayOutputStream.toByteArray()

            // 使用NO_WRAP避免换行符，并添加MIME类型前缀
            val base64String = Base64.encodeToString(byteArray, Base64.NO_WRAP)
            "data:image/jpeg;base64,$base64String"
        } catch (e: Exception) {
            Log.e(TAG, "Bitmap转Base64失败: ${e.message}")
            null
        }
    }

    /**
     * Bitmap转Base64（公共接口）
     * 确保base64字符串小于512KB
     */
    private fun bitmapToBase64(bitmap: Bitmap): String? {
        return try {
            // 首先尝试标准压缩
            val base64String = bitmapToBase64Internal(bitmap, IMAGE_QUALITY)

            if (base64String != null && base64String.length <= MAX_BASE64_SIZE) {
                Log.d(TAG, "标准压缩成功: Base64大小=${base64String.length}字节")
                return base64String
            }

            // 如果超过512KB，使用智能压缩
            Log.d(TAG, "图片过大(${base64String?.length ?: 0}字节)，开始智能压缩...")
            showToast("图片较大，正在智能压缩...")

            val compressedBitmap = compressImageToTargetSize(bitmap)
            if (compressedBitmap != null) {
                val finalBase64 = bitmapToBase64Internal(compressedBitmap, IMAGE_QUALITY)
                if (finalBase64 != null && finalBase64.length <= MAX_BASE64_SIZE) {
                    showToast("图片压缩成功")
                    return finalBase64
                }
            }

            showToast("图片过大，无法压缩到512KB以下，请选择其他图片")
            return null

        } catch (e: Exception) {
            Log.e(TAG, "Bitmap转Base64失败: ${e.message}")
            showToast("图片处理失败")
            null
        }
    }

    /**
     * 更新图片预览
     */
    private fun updateImagePreview(bitmap: Bitmap, imageType: ImageType) {
        val sectionView = when (imageType) {
            ImageType.LOGO -> binding.mediaEditLogoSection.root
            ImageType.DOUYIN_QR -> binding.mediaEditDouyinSection.root
            ImageType.WECHAT_QR -> binding.mediaEditWechatSection.root
        }

        val previewImage = sectionView.findViewById<ImageView>(R.id.image_selector_preview)
        val emptyState = sectionView.findViewById<View>(R.id.image_selector_empty_state)
        val deleteBtn = sectionView.findViewById<ImageView>(R.id.image_selector_delete)

        previewImage.setImageBitmap(bitmap)
        previewImage.visibility = View.VISIBLE
        emptyState.visibility = View.GONE
        deleteBtn.visibility = View.VISIBLE
    }

    /**
     * 显示删除确认对话框
     */
    private fun showDeleteConfirmDialog(imageType: ImageType) {
        val imageTypeName = when (imageType) {
            ImageType.LOGO -> "Logo图片"
            ImageType.DOUYIN_QR -> "抖音二维码"
            ImageType.WECHAT_QR -> "微信二维码"
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("确认删除")
            .setMessage("确定要删除${imageTypeName}吗？")
            .setPositiveButton("删除") { _, _ ->
                clearImage(imageType)
                showToast("${imageTypeName}已删除")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 清除图片
     */
    private fun clearImage(imageType: ImageType) {
        val sectionView = when (imageType) {
            ImageType.LOGO -> binding.mediaEditLogoSection.root
            ImageType.DOUYIN_QR -> binding.mediaEditDouyinSection.root
            ImageType.WECHAT_QR -> binding.mediaEditWechatSection.root
        }

        val previewImage = sectionView.findViewById<ImageView>(R.id.image_selector_preview)
        val emptyState = sectionView.findViewById<View>(R.id.image_selector_empty_state)
        val deleteBtn = sectionView.findViewById<ImageView>(R.id.image_selector_delete)
        val loadingView = sectionView.findViewById<View>(R.id.image_selector_loading)

        // 更新UI状态
        previewImage.visibility = View.GONE
        emptyState.visibility = View.VISIBLE
        deleteBtn.visibility = View.GONE
        loadingView.visibility = View.GONE

        // 清除图片资源
        previewImage.setImageDrawable(null)

        // 清除数据
        when (imageType) {
            ImageType.LOGO -> logoData = null
            ImageType.DOUYIN_QR -> douyinQrData = null
            ImageType.WECHAT_QR -> wechatQrData = null
        }

        Log.d(TAG, "已清除图片: $imageType")
    }

    /**
     * 保存媒体信息
     */
    private fun saveMediaInfo() {
        if (!validateForm()) {
            return
        }

        val contactPerson = binding.mediaEditContactPerson.text.toString().trim()
        val contactPhone = binding.mediaEditContactPhone.text.toString().trim()

        val request = MiaoChangMediaUpdateRequest(
            id = media?.id,
            contactPerson = contactPerson.ifBlank { null },
            contactPhone = contactPhone.ifBlank { null },
            logo = logoData,
            douyinQrCode = douyinQrData,
            wechatQrCode = wechatQrData
        )

        updateMediaInfo(request)
    }

    /**
     * 表单验证
     */
    private fun validateForm(): Boolean {
        val contactPerson = binding.mediaEditContactPerson.text.toString().trim()
        val contactPhone = binding.mediaEditContactPhone.text.toString().trim()

        // 清除之前的错误
        binding.mediaEditContactPersonLayout.error = null
        binding.mediaEditContactPhoneLayout.error = null

        var isValid = true

        // 验证联系人姓名（必填）
        if (contactPerson.isBlank()) {
            binding.mediaEditContactPersonLayout.error = "请输入联系人姓名"
            isValid = false
        }

        // 验证电话号码格式
        if (contactPhone.isNotBlank() && !Patterns.PHONE.matcher(contactPhone).matches()) {
            binding.mediaEditContactPhoneLayout.error = "请输入正确的电话号码"
            isValid = false
        }

        return isValid
    }

    /**
     * 更新媒体信息
     */
    private fun updateMediaInfo(request: MiaoChangMediaUpdateRequest) {
        lifecycleScope.launch {
            try {
                // 显示加载状态
                showLoading(true)

                // 禁用缓存确保数据实时性
                RetrofitClient.setUseCache(false)

                Log.d(TAG, "开始更新媒体信息")
                Log.d(TAG, "Logo格式: ${request.logo?.let { if (it.startsWith("data:image/")) "Data URI (${it.length}字节)" else if (it.startsWith("http")) "URL" else "未知格式" } ?: "无"}")
                Log.d(TAG, "抖音二维码格式: ${request.douyinQrCode?.let { if (it.startsWith("data:image/")) "Data URI (${it.length}字节)" else if (it.startsWith("http")) "URL" else "未知格式" } ?: "无"}")
                Log.d(TAG, "微信二维码格式: ${request.wechatQrCode?.let { if (it.startsWith("data:image/")) "Data URI (${it.length}字节)" else if (it.startsWith("http")) "URL" else "未知格式" } ?: "无"}")

                val response = RetrofitClient.apiService.updateMiaoChangMedia(request)

                when (response.code) {
                    200 -> {
                        Log.d(TAG, "媒体信息更新成功")
                        showToast("保存成功")

                        // 更新本地用户数据
                        response.results.let { updatedMedia ->
                            updateLocalUserData(updatedMedia)
                        }

                        // 返回上一页
                        finish()
                    }
                    else -> {
                        Log.w(TAG, "媒体信息更新失败: ${response.msg}")
                        showToast("保存失败: ${response.msg}")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "更新媒体信息异常: ${e.message}", e)
                val errorMessage = ExceptionUtil.catchException(e)
                showToast("保存失败: $errorMessage")
            } finally {
                showLoading(false)
                // 恢复缓存设置
                RetrofitClient.setUseCache(true)
            }
        }
    }

    /**
     * 更新本地用户数据
     */
    private fun updateLocalUserData(userJson: UserJson) {
        userViewModel.userObserver.value = userJson
        ConfigTools.setUser(userJson)
        finish()
    }

    /**
     * 显示加载状态
     */
    private fun showLoading(show: Boolean) {
        binding.mediaEditSaveButton.isEnabled = !show
        binding.mediaEditSaveButton.text = if (show) "保存中..." else "保存更改"
    }


}