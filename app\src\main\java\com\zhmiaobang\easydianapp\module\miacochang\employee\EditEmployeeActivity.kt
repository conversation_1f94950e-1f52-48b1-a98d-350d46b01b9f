package com.zhmiaobang.easydianapp.module.miacochang.employee

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Base64
import android.util.Log
import android.view.View
import android.widget.ArrayAdapter
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityEditEmployeeBinding
import com.zhmiaobang.easydianapp.json.user.UserJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.viewmodel.user.EmployeeViewModel
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream

/**
 * 员工编辑Activity
 *
 * 功能特性：
 * - 编辑员工基本信息
 * - 头像选择和上传
 * - 权限设置
 * - 表单验证和数据保存
 * - 完整的错误处理和用户反馈
 * - 限制只能编辑指定字段
 *
 * <AUTHOR> 4.0 sonnet
 */
class EditEmployeeActivity : BaseActivity() {

    companion object {
        private const val TAG = "EditEmployeeActivity"
        private const val EXTRA_EMPLOYEE = "employee"
        private const val IMAGE_QUALITY = 80
        private const val MAX_BASE64_SIZE = 512 * 1024 // 512KB
    }

    // ViewBinding
    private val binding: ActivityEditEmployeeBinding by lazy {
        ActivityEditEmployeeBinding.inflate(layoutInflater)
    }

    // ViewModel
    private val viewModel: EmployeeViewModel by viewModels()

    // 数据
    private var employee: UserJson? = null
    private var isEditMode: Boolean = false
    private var selectedImageBase64: String? = null

    // 状态选项
    private val statusOptions = arrayOf("在职", "离职")
    private val statusValues = arrayOf(true, false)

    // 图片选择器
    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { handleImageSelection(it) }
    }

    // 相机拍照
    private val cameraLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicturePreview()
    ) { bitmap: Bitmap? ->
        bitmap?.let { handleCameraCapture(it) }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initializeUI()
        setupObservers()

        Log.d(TAG, "EditEmployeeActivity创建完成")
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()

        // 获取传入的数据
        employee = intent.getParcelableExtra(EXTRA_EMPLOYEE)
        isEditMode = employee != null

        setupToolbar()
        setupStatusDropdown()
        setupImagePickers()
        setupClickListeners()

        // 如果是编辑模式，绑定数据
        if (isEditMode) {
            bindDataToUI()
        }
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()

        // 观察编辑结果
        viewModel.employeeEditObserver.observe(this) { response ->
            hideLoading()

            try {
                when (response.code) {
                    200 -> {
                        Log.d(TAG, "员工保存成功")
                        showToast("保存成功")

                        // 返回上一页
                        setResult(Activity.RESULT_OK)
                        finish()
                    }
                    else -> {
                        Log.w(TAG, "员工保存失败: ${response.msg}")
                        showToast("保存失败: ${response.msg}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理保存结果失败: ${e.message}", e)
                showToast("保存失败，请重试")
            }
        }
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        val title = if (isEditMode) "编辑员工" else "新建员工"
        initToolbar(
            toolbar = binding.edEmployeeToolbar,
            title = title,
            showBack = true
        )
    }

    /**
     * 设置状态下拉选择
     */
    private fun setupStatusDropdown() {
        val adapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, statusOptions)
        binding.edEmployeeActStatus.setAdapter(adapter)

        // 设置默认选择
        if (!isEditMode) {
            binding.edEmployeeActStatus.setText(statusOptions[0], false) // 默认选择"在职"
        }
    }

    /**
     * 设置图片选择器
     */
    private fun setupImagePickers() {
        // 图片选择按钮点击事件
        binding.edEmployeeBtnSelectAvatar.setOnClickListener {
            showImagePickerDialog()
        }

        // 图片点击事件（也可以选择图片）
        binding.edEmployeeIvAvatar.setOnClickListener {
            showImagePickerDialog()
        }
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 保存按钮
        binding.edEmployeeBtnSave.setOnClickListener {
            saveEmployee()
        }
    }

    /**
     * 绑定数据到UI
     */
    private fun bindDataToUI() {
        employee?.let { emp ->
            // 基本信息
            binding.edEmployeeEtName.setText(emp.nickname ?: "")
            binding.edEmployeeEtPhone.setText(emp.employeePhone ?: "")

            // 状态
            val statusIndex = statusValues.indexOf(emp.is_active)
            if (statusIndex >= 0) {
                binding.edEmployeeActStatus.setText(statusOptions[statusIndex], false)
            }

            // 权限设置
            binding.edEmployeeSwitchCanAccount.isChecked = emp.canAccount
            binding.edEmployeeSwitchCanCount.isChecked = emp.canCount
            binding.edEmployeeSwitchCanDocs.isChecked = emp.canDocs
            binding.edEmployeeSwitchCanFeed.isChecked = emp.canFeed
            binding.edEmployeeSwitchCanMeasure.isChecked = emp.canMeasure
            binding.edEmployeeSwitchCanPreSeed.isChecked = emp.canPreSeed
            binding.edEmployeeSwitchCanSample.isChecked = emp.canSample

            // 只读信息
            binding.edEmployeeEtId.setText(emp.id.toString())
            binding.edEmployeeEtRole.setText(emp.role.name)

            // 头像
            loadAvatar(emp.avatar)
        }
    }

    /**
     * 加载头像图片
     */
    private fun loadAvatar(imageUrl: String?) {
        binding.edEmployeeIvAvatar.load(imageUrl) {
            crossfade(300)
            placeholder(R.drawable.ic_person)
            error(R.drawable.ic_person)
            transformations(CircleCropTransformation())
        }
    }

    /**
     * 显示图片选择对话框
     */
    private fun showImagePickerDialog() {
        val options = arrayOf("从相册选择", "拍照")

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("选择头像")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> imagePickerLauncher.launch("image/*")
                    1 -> cameraLauncher.launch(null)
                }
            }
            .show()
    }

    /**
     * 处理图片选择
     */
    private fun handleImageSelection(uri: Uri) {
        try {
            val bitmap = MediaStore.Images.Media.getBitmap(contentResolver, uri)
            processSelectedImage(bitmap)
        } catch (e: Exception) {
            Log.e(TAG, "处理选择的图片失败: ${e.message}", e)
            showToast("图片处理失败，请重试")
        }
    }

    /**
     * 处理相机拍照
     */
    private fun handleCameraCapture(bitmap: Bitmap) {
        processSelectedImage(bitmap)
    }

    /**
     * 处理选择的图片
     */
    private fun processSelectedImage(bitmap: Bitmap) {
        lifecycleScope.launch {
            try {
                // 压缩图片
                val compressedBitmap = compressBitmap(bitmap)

                // 转换为Base64
                val base64String = bitmapToBase64(compressedBitmap)

                // 检查大小
                if (base64String.length > MAX_BASE64_SIZE) {
                    showToast("图片过大，请选择较小的图片")
                    return@launch
                }

                // 保存Base64字符串
                selectedImageBase64 = base64String

                // 显示图片
                binding.edEmployeeIvAvatar.setImageBitmap(compressedBitmap)

                Log.d(TAG, "图片处理成功，Base64长度: ${base64String.length}")

            } catch (e: Exception) {
                Log.e(TAG, "图片处理失败: ${e.message}", e)
                showToast("图片处理失败，请重试")
            }
        }
    }

    /**
     * 压缩图片
     */
    private fun compressBitmap(bitmap: Bitmap): Bitmap {
        val maxWidth = 800
        val maxHeight = 800

        val width = bitmap.width
        val height = bitmap.height

        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }

        val ratio = minOf(maxWidth.toFloat() / width, maxHeight.toFloat() / height)
        val newWidth = (width * ratio).toInt()
        val newHeight = (height * ratio).toInt()

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * 将Bitmap转换为Base64字符串
     */
    private fun bitmapToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, byteArrayOutputStream)
        val byteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.DEFAULT)
    }

    /**
     * 保存员工信息
     */
    private fun saveEmployee() {
        // 表单验证
        val name = binding.edEmployeeEtName.text.toString().trim()
        if (name.isEmpty()) {
            binding.edEmployeeTilName.error = "请输入员工姓名"
            return
        } else {
            binding.edEmployeeTilName.error = null
        }

        val phone = binding.edEmployeeEtPhone.text.toString().trim()
        if (phone.isEmpty()) {
            binding.edEmployeeTilPhone.error = "请输入员工电话"
            return
        } else {
            binding.edEmployeeTilPhone.error = null
        }

        val statusText = binding.edEmployeeActStatus.text.toString()
        val statusIndex = statusOptions.indexOf(statusText)
        if (statusIndex < 0) {
            binding.edEmployeeTilStatus.error = "请选择员工状态"
            return
        } else {
            binding.edEmployeeTilStatus.error = null
        }

        val isActive = statusValues[statusIndex]

        try {
            showLoading()

            // 构建要保存的数据（只包含可编辑字段）
            val employeeToSave = if (isEditMode) {
                employee!!.copy(
                    nickname = name,
                    employeePhone = phone,
                    is_active = isActive,
                    avatar = selectedImageBase64 ?: employee!!.avatar,
                    canAccount = binding.edEmployeeSwitchCanAccount.isChecked,
                    canCount = binding.edEmployeeSwitchCanCount.isChecked,
                    canDocs = binding.edEmployeeSwitchCanDocs.isChecked,
                    canFeed = binding.edEmployeeSwitchCanFeed.isChecked,
                    canMeasure = binding.edEmployeeSwitchCanMeasure.isChecked,
                    canPreSeed = binding.edEmployeeSwitchCanPreSeed.isChecked,
                    canSample = binding.edEmployeeSwitchCanSample.isChecked
                )
            } else {
                // 新建模式（实际项目中可能需要后端生成ID等）
                employee!!.copy(
                    nickname = name,
                    employeePhone = phone,
                    is_active = isActive,
                    avatar = selectedImageBase64,
                    canAccount = binding.edEmployeeSwitchCanAccount.isChecked,
                    canCount = binding.edEmployeeSwitchCanCount.isChecked,
                    canDocs = binding.edEmployeeSwitchCanDocs.isChecked,
                    canFeed = binding.edEmployeeSwitchCanFeed.isChecked,
                    canMeasure = binding.edEmployeeSwitchCanMeasure.isChecked,
                    canPreSeed = binding.edEmployeeSwitchCanPreSeed.isChecked,
                    canSample = binding.edEmployeeSwitchCanSample.isChecked
                )
            }

            Log.d(TAG, "开始保存员工: ${employeeToSave.nickname}")
            viewModel.editEmployee(employeeToSave)

        } catch (e: Exception) {
            hideLoading()
            val errorMessage = ExceptionUtil.catchException(e)
            showToast("保存失败: $errorMessage")
            Log.e(TAG, "保存员工失败: ${e.message}", e)
        }
    }

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.edEmployeeProgressIndicator.visibility = View.VISIBLE
        binding.edEmployeeBtnSave.isEnabled = false
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.edEmployeeProgressIndicator.visibility = View.GONE
        binding.edEmployeeBtnSave.isEnabled = true
    }
}