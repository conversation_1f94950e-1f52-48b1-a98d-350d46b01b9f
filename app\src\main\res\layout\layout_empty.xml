<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp">

    <ImageView
        android:id="@+id/iv_empty_icon"
        android:layout_width="96dp"
        android:layout_height="96dp"
        android:layout_marginBottom="16dp"
        android:alpha="0.6"
        android:src="@drawable/ic_image_placeholder"
        app:tint="@color/text_hint" />

    <TextView
        android:id="@+id/tv_empty_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="暂无数据"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_empty_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="暂时没有相关记录"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        android:gravity="center" />

</LinearLayout>
