package com.zhmiaobang.easydianapp.libs
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec
import android.util.Base64
import javax.crypto.spec.IvParameterSpec
import java.security.SecureRandom

object DecryptQrCode {

    fun decryptAESCBCBase64(encryptedBase64: String, keyBase64: String): String? {
        return try {
            // 解码密钥
            val keyBytes = Base64.decode(keyBase64, Base64.DEFAULT)
            val secretKey = SecretKeySpec(keyBytes, "AES")

            // 解码加密数据
            val combined = Base64.decode(encryptedBase64, Base64.DEFAULT)
            val iv = combined.copyOfRange(0, 16)
            val encrypted = combined.copyOfRange(16, combined.size)

            val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
            cipher.init(Cipher.DECRYPT_MODE, secretKey, IvParameterSpec(iv))
            val decrypted = cipher.doFinal(encrypted)
            String(decrypted, Charsets.UTF_8)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 加密字符串数据为AES/CBC/PKCS5Padding格式并Base64编码
     * 
     * @param plainText 要加密的文本
     * @param keyBase64 Base64编码的密钥
     * @return Base64编码的加密数据（包含IV+加密内容）
     */
    fun encryptAESCBCBase64(plainText: String, keyBase64: String): String? {
        return try {
            // 解码密钥
            val keyBytes = Base64.decode(keyBase64, Base64.DEFAULT)
            val secretKey = SecretKeySpec(keyBytes, "AES")
            
            // 生成随机IV
            val iv = ByteArray(16)
            SecureRandom().nextBytes(iv)
            
            // 加密数据
            val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, IvParameterSpec(iv))
            val encrypted = cipher.doFinal(plainText.toByteArray(Charsets.UTF_8))
            
            // 组合IV和加密数据
            val combined = ByteArray(iv.size + encrypted.size)
            System.arraycopy(iv, 0, combined, 0, iv.size)
            System.arraycopy(encrypted, 0, combined, iv.size, encrypted.size)
            
            // Base64编码
            Base64.encodeToString(combined, Base64.DEFAULT)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}


