package com.zhmiaobang.easydianapp.viewmodel.login

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.zhmiaobang.easydianapp.json.CommonResponseJson
import com.zhmiaobang.easydianapp.json.login.LoginPostJson
import com.zhmiaobang.easydianapp.json.login.LoginResultJson
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.viewmodel.BaseViewModel

class LoginViewModel: BaseViewModel() {

    companion object {
        private const val TAG = "LoginViewModel"
    }

    val loginResult: MutableLiveData<CommonResponseJson<LoginResultJson>> by lazy { MutableLiveData<CommonResponseJson<LoginResultJson>>() }

    fun login(code: String) = launch({
        Log.d(TAG, "开始登录请求，员工代码: $code")

        val loginPostJson = LoginPostJson(code = code)
        Log.d(TAG, "登录请求体: code=$code, device=${loginPostJson.device}")

        try {
            val response = RetrofitClient.apiService.loginPost(loginPostJson)
            Log.d(TAG, "登录请求成功，响应: code=${response.code}, msg=${response.msg}")
            loginResult.postValue(response)
        } catch (e: Exception) {
            Log.e(TAG, "登录请求异常: ${e.message}", e)
            throw e // 重新抛出异常，让 BaseViewModel 处理
        }
    })
}