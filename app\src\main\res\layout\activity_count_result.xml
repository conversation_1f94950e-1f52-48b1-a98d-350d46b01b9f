<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".module.count.CountResultActivity">

    <!-- Material Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/count_result_toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:titleTextColor="@android:color/white"
        app:subtitleTextColor="@android:color/white"
        app:titleCentered="true"
        app:subtitleCentered="true"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@android:color/white" />

    <!-- 图片显示区域 - 右上角，占用1/3宽度 -->
    <ImageView
        android:id="@+id/count_result_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        android:scaleType="centerCrop"
        android:background="@android:color/darker_gray"
        android:contentDescription="计数结果图片"
        app:layout_constraintTop_toBottomOf="@+id/count_result_toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_percent="0.33"
        app:layout_constraintDimensionRatio="3:4" />

    <!-- 计数功能区域 - 在图片下方 -->
    <LinearLayout
        android:id="@+id/count_result_counting_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:orientation="vertical"
        android:background="@drawable/photo_preview_background"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@+id/count_result_image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_percent="0.33">

        <!-- 开始计数按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/count_result_start_button"
            style="@style/Widget.Material3.Button.UnelevatedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="开始计数"
            android:textSize="14sp"
            android:backgroundTint="@color/hIndex_blue"
            android:textColor="@android:color/white"
            app:icon="@drawable/ic_shrimp_count"
            app:iconTint="@android:color/white"
            app:iconGravity="textStart" />

        <!-- 进度指示器 -->
        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/count_result_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:visibility="gone"
            app:indicatorColor="@color/hIndex_blue"
            app:trackColor="@android:color/darker_gray" />

        <!-- 状态文本 -->
        <TextView
            android:id="@+id/count_result_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="点击开始计数按钮开始检测虾苗"
            android:textSize="12sp"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:visibility="visible" />

        <!-- 计数结果显示 -->
        <LinearLayout
            android:id="@+id/count_result_display_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="检测结果"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:textStyle="bold"
                android:gravity="center" />

            <TextView
                android:id="@+id/count_result_count_display"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="0"
                android:textSize="24sp"
                android:textColor="@color/hIndex_blue"
                android:textStyle="bold"
                android:gravity="center" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="个虾苗"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                android:gravity="center" />

        </LinearLayout>

        <!-- 重试按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/count_result_retry_button"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="重新计数"
            android:textSize="12sp"
            android:textColor="@color/hIndex_blue"
            android:visibility="gone"
            app:strokeColor="@color/hIndex_blue"
            app:icon="@drawable/ic_arrow_back_24"
            app:iconTint="@color/hIndex_blue"
            app:iconGravity="textStart" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>