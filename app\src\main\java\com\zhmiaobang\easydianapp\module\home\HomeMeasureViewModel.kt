package com.zhmiaobang.easydianapp.module.home

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson

/**
 * 测量模块ViewModel
 *
 * <AUTHOR> 4.0 sonnet
 */
class HomeMeasureViewModel : ViewModel() {

    // 选中的ONNX模型
    val selectedModelObserver: MutableLiveData<OnnxModelJson> by lazy {
        MutableLiveData<OnnxModelJson>()
    }

    // 电话号码
    val phoneNumberObserver: MutableLiveData<String> by lazy {
        MutableLiveData<String>()
    }

    // 图片路径
    val srcMeasurePathObserver: MutableLiveData<String> by lazy {
        MutableLiveData<String>()
    }

    /**
     * 设置选中的模型
     */
    fun setSelectedModel(model: OnnxModelJson) {
        selectedModelObserver.value = model
    }

    /**
     * 获取选中的模型
     */
    fun getSelectedModel(): OnnxModelJson? {
        return selectedModelObserver.value
    }

    /**
     * 设置电话号码
     */
    fun setPhoneNumber(phoneNumber: String) {
        phoneNumberObserver.value = phoneNumber
    }

    /**
     * 获取电话号码
     */
    fun getPhoneNumber(): String? {
        return phoneNumberObserver.value
    }

    /**
     * 设置图片路径
     */
    fun setSrcMeasurePath(path: String) {
        srcMeasurePathObserver.value = path
    }

    /**
     * 获取图片路径
     */
    fun getSrcMeasurePath(): String? {
        return srcMeasurePathObserver.value
    }
}