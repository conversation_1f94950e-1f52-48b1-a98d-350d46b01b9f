<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/frameLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".module.home.HomeCountFragment" >

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="48dp"
        android:layout_marginEnd="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edCountPhone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="电话号码" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/txtCountSelectCategory"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:drawablePadding="8dp"
        android:padding="16dp"
        android:text="选择虾苗类型"
        android:textSize="16sp"
        android:textStyle="bold"
        app:drawableRightCompat="@drawable/ic_arrow_right"
        app:layout_constraintEnd_toEndOf="@+id/textInputLayout"
        app:layout_constraintStart_toStartOf="@+id/textInputLayout"
        app:layout_constraintTop_toBottomOf="@+id/textInputLayout" />

    <CheckBox
        android:id="@+id/count_chk_remember_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="记住类型和电话号码"
        app:layout_constraintStart_toStartOf="@+id/txtCountSelectCategory"
        app:layout_constraintTop_toBottomOf="@+id/txtCountSelectCategory" />

    <TextView
        android:id="@+id/count_txt_image_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="添加图片"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        app:layout_constraintEnd_toEndOf="@+id/txtCountSelectCategory"
        app:layout_constraintStart_toStartOf="@+id/txtCountSelectCategory"
        app:layout_constraintTop_toBottomOf="@+id/count_chk_remember_phone" />

    <LinearLayout
        android:id="@+id/count_layout_image_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="@+id/count_txt_image_title"
        app:layout_constraintStart_toStartOf="@+id/count_txt_image_title"
        app:layout_constraintTop_toBottomOf="@+id/count_txt_image_title">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/count_btn_gallery"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="选择相册"
            android:textSize="14sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/count_btn_camera"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="打开相机"
            android:textSize="14sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>