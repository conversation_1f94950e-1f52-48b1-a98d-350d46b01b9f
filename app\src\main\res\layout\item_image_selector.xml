<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardUseCompatPadding="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题和删除按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/image_selector_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="图片标题"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/hIndex_blue" />

            <ImageView
                android:id="@+id/image_selector_delete"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_delete"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:visibility="gone"
                app:tint="@color/hIndex_red" />

        </LinearLayout>

        <!-- 图片预览区域 -->
        <FrameLayout
            android:id="@+id/image_selector_preview_container"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/image_preview_border">

            <!-- 默认状态：显示选择提示 -->
            <LinearLayout
                android:id="@+id/image_selector_empty_state"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_image_placeholder"
                    android:layout_marginBottom="6dp"
                    app:tint="@color/hIndex_text_secondary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="点击此区域选择图片"
                    android:textSize="12sp"
                    android:textColor="@color/hIndex_text_secondary"
                    android:gravity="center"
                    android:maxLines="2" />

            </LinearLayout>

            <!-- 图片预览 -->
            <ImageView
                android:id="@+id/image_selector_preview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:visibility="gone" />

            <!-- 加载状态 -->
            <ProgressBar
                android:id="@+id/image_selector_loading"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>

        <!-- 选择按钮 (已隐藏，通过点击预览区域选择图片) -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/image_selector_gallery_btn"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="选择图片"
            android:textSize="14sp"
            android:visibility="gone"
            app:icon="@drawable/ic_image"
            app:iconSize="18dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
