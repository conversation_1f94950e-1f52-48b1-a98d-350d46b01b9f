package com.zhmiaobang.easydianapp.json.preseed

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class PreSeedJson(
    val batch: String?=null,
    val createTime: String,
    val expiredTime: String?=null,
    val id: Int,
    val miaochang: SimpleMiaoChangJson,
    val no: String,
    val preSeedPerson: SimpleUserJson?=null,
    val preSeedPhone: String?=null,
    val preSeedTime: String?=null,
    val quantity: Int,
    val status: Int,
    val trace_expired_time: String?=null,
    val updateTime: String?=null,
    val user: SimpleUserJson,
    val wechatQrCode: String
): Parcelable