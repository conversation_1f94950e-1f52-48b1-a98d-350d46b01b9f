<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.miacochang.preseed.GetPreSeedQrCodeActivity">

    <!-- Material Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/preseed_qrcode_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@android:color/white"
        app:title="试水苗二维码"
        app:titleCentered="true"
        app:titleTextColor="@android:color/white" />

    <!-- Loading Progress Bar -->
    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/preseed_qrcode_progress_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="?attr/actionBarSize"
        android:indeterminate="true"
        android:visibility="visible"
        app:indicatorColor="@color/hIndex_blue"
        app:trackColor="@color/hIndex_blue_light" />

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/preseed_qrcode_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        android:fillViewport="true"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Miaochang Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/preseed_qrcode_card_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/hIndex_card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Miaochang Name -->
                    <TextView
                        android:id="@+id/preseed_qrcode_tv_miaochang_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        tools:text="示例苗场" />

                    <!-- Validity Period -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="8dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="有效期至："
                            android:textColor="@color/hIndex_text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/preseed_qrcode_tv_expired_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/hIndex_text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="2024-12-31 23:59:59" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- QR Code Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/preseed_qrcode_card_qr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/hIndex_card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <!-- QR Code Title -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="微信扫码预播种"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- WeChat Hint -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="请使用微信扫描下方二维码"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="14sp"
                        android:gravity="center" />

                    <!-- QR Code Image -->
                    <ImageView
                        android:id="@+id/preseed_qrcode_iv_qr_code"
                        android:layout_width="250dp"
                        android:layout_height="250dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/rounded_button_background"
                        android:contentDescription="预播种微信二维码"
                        android:padding="8dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/ic_qr_code"
                        tools:src="@drawable/ic_qr_code" />

                    <!-- Loading Indicator -->
                    <com.google.android.material.progressindicator.CircularProgressIndicator
                        android:id="@+id/preseed_qrcode_progress_loading"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        app:indicatorColor="@color/hIndex_blue" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Usage Instructions -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/preseed_qrcode_card_instructions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/hIndex_card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="使用说明"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="1. 请在二维码有效期内使用"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="2. 使用微信扫描上方二维码"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="3. 扫描成功后即可领取试水苗确认"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="14sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Error State -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/preseed_qrcode_card_error"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/hIndex_card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="加载失败"
                        android:textColor="@color/hIndex_red"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/preseed_qrcode_tv_error_message"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="网络连接失败，请检查网络后重试"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="14sp"
                        android:gravity="center" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/preseed_qrcode_btn_retry"
                        style="@style/Widget.Material3.Button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="重试"
                        android:textColor="@android:color/white"
                        android:backgroundTint="@color/hIndex_blue" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>