package com.zhmiaobang.easydianapp.libs.network

import android.accounts.NetworkErrorException
import android.util.Log
import com.google.gson.JsonSyntaxException
import com.google.gson.stream.MalformedJsonException
import retrofit2.HttpException
import java.io.InterruptedIOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
object ExceptionUtil {
    /**
     * Handles exceptions and returns an appropriate error message.
     */
    fun catchException(e: Throwable): String {
        e.printStackTrace()

        return when (e) {
            is HttpException -> catchHttpException(e.code())
            is SocketTimeoutException -> "请求超时"
            is UnknownHostException, is NetworkErrorException -> "服务器链接失败"
            is MalformedJsonException, is JsonSyntaxException -> "数据解析错误"
            is InterruptedIOException -> "服务器连接失败，请稍后重试"
            is ConnectException -> "连接服务器失败"
            else -> {
                Log.d("ExceptionUtil", "Unhandled exception: ${e.message}")
                "网络失败，请检测网络设置"
            }
        }
    }
    /**
     * Handles HTTP exceptions and returns an appropriate error message.
     */
    private fun catchHttpException(errorCode: Int): String {
        return if (errorCode in 200 until 300) {
            Log.d("ExceptionUtil", "HTTP request succeeded with code: $errorCode")
            "请求成功"
        } else {
            catchHttpExceptionCode(errorCode)
        }
    }


    /**
     * 处理网络异常
     */
    private fun catchHttpExceptionCode(errorCode: Int): String = when (errorCode) {
        400 -> "请求格式错误"
        401 -> "认证失败"
        in 402 until 500 -> "数据请求错误"
        in 500..600 -> "无服务器连接失败，请检查网络"
        else -> "通讯异常"
    }
}