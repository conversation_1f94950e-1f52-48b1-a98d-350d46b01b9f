package com.zhmiaobang.easydianapp.viewmodel.measure

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.zhmiaobang.easydianapp.json.CommonRestfulJson
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.utils.PaginatedData
import com.zhmiaobang.easydianapp.utils.PaginationState
import com.zhmiaobang.easydianapp.viewmodel.BaseViewModel

/**
 * 测量模块ViewModel - 支持分页功能
 * 扩展版本 by Claude 4.0 sonnet
 */
class MeasureViewModel: BaseViewModel() {

    companion object {
        private const val TAG = "MeasureViewModel"
    }

    // 原有的观察者（保持兼容性）
    val onnxModelListObserver: MutableLiveData<CommonRestfulJson<OnnxModelJson>> by lazy { MutableLiveData<CommonRestfulJson<OnnxModelJson>>() }

    // 新增：分页数据观察者
    val paginatedDataObserver: MutableLiveData<PaginatedData<OnnxModelJson>> by lazy { MutableLiveData<PaginatedData<OnnxModelJson>>() }

    // 分页状态管理
    private var paginationState = PaginationState()

    // 所有ONNX模型数据（去重）
    private val allOnnxModels = LinkedHashSet<OnnxModelJson>()

    /**
     * 原有方法（保持兼容性）
     */
    fun getonnxList(page:Int=1)=launch({
        onnxModelListObserver.postValue(RetrofitClient.apiService.get_onnx_model_list(page))
    })

    /**
     * 加载第一页数据（使用缓存）
     */
    fun loadFirstPage() = launch({
        if (paginationState.isLoading) {
            Log.d(TAG, "正在加载中，忽略重复请求")
            return@launch
        }

        Log.d(TAG, "开始加载第一页数据（使用缓存）...")
        paginationState = paginationState.startFirstLoading()
        allOnnxModels.clear()

        try {
            // 正常加载使用缓存
            RetrofitClient.setUseCache(true)

            val response = RetrofitClient.apiService.get_onnx_model_list(page = 1)
            Log.d(TAG, "第一页加载成功: count=${response.count}, results数量=${response.results.size}")

            // 添加数据到集合（自动去重）
            allOnnxModels.addAll(response.results)

            // 更新分页状态
            val hasNext = !response.next.isNullOrEmpty()
            paginationState = paginationState.loadSuccess(hasNext, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allOnnxModels.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "第一页加载失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "加载失败")

            val paginatedData = PaginatedData(
                items = allOnnxModels.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        }
    })

    /**
     * 加载下一页数据（使用缓存）
     */
    fun loadNextPage() = launch({
        if (!paginationState.canLoadMore) {
            Log.d(TAG, "无法加载更多: isLoading=${paginationState.isLoading}, hasMore=${paginationState.hasMoreData}")
            return@launch
        }

        Log.d(TAG, "开始加载第${paginationState.currentPage}页数据（使用缓存）...")
        paginationState = paginationState.startLoadingMore()

        // 先发送加载状态
        val loadingData = PaginatedData(
            items = allOnnxModels.toList(),
            paginationState = paginationState
        )
        paginatedDataObserver.postValue(loadingData)

        try {
            // 加载更多使用缓存
            RetrofitClient.setUseCache(true)

            val response = RetrofitClient.apiService.get_onnx_model_list(page = paginationState.currentPage)
            Log.d(TAG, "第${paginationState.currentPage}页加载成功: count=${response.count}, results数量=${response.results.size}")

            // 添加新数据到集合（自动去重）
            allOnnxModels.addAll(response.results)

            // 更新分页状态
            val hasNext = !response.next.isNullOrEmpty()
            paginationState = paginationState.loadSuccess(hasNext, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allOnnxModels.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "第${paginationState.currentPage}页加载失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "加载失败")

            val paginatedData = PaginatedData(
                items = allOnnxModels.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
        }
    })

    /**
     * 刷新数据（强制不使用缓存）
     */
    fun refresh() = launch({
        Log.d(TAG, "开始刷新数据（不使用缓存）...")
        paginationState = paginationState.reset().startFirstLoading()
        allOnnxModels.clear()

        try {
            // 刷新时不使用缓存
            RetrofitClient.setUseCache(false)

            val response = RetrofitClient.apiService.get_onnx_model_list(page = 1)
            Log.d(TAG, "刷新成功: count=${response.count}, results数量=${response.results.size}")

            // 添加数据到集合
            allOnnxModels.addAll(response.results)

            // 更新分页状态
            val hasNext = !response.next.isNullOrEmpty()
            paginationState = paginationState.loadSuccess(hasNext, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allOnnxModels.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "刷新失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "刷新失败")

            val paginatedData = PaginatedData(
                items = allOnnxModels.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
        }
    })

    /**
     * 重试加载
     */
    fun retryLoad() {
        Log.d(TAG, "重试加载")
        if (allOnnxModels.isEmpty()) {
            loadFirstPage()
        } else {
            loadNextPage()
        }
    }

    /**
     * 获取当前分页状态
     */
    fun getCurrentPaginationState(): PaginationState {
        return paginationState
    }
}