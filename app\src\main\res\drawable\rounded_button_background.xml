<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed State -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/hIndex_background" />
            <corners android:radius="8dp" />
            <stroke 
                android:width="1dp" 
                android:color="@color/primary_color" />
        </shape>
    </item>
    
    <!-- Focused State -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/hIndex_background" />
            <corners android:radius="8dp" />
            <stroke 
                android:width="1dp" 
                android:color="@color/primary_color" />
        </shape>
    </item>
    
    <!-- Normal State -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/hIndex_background" />
            <corners android:radius="8dp" />
            <stroke 
                android:width="1dp" 
                android:color="@color/hIndex_text_hint" />
        </shape>
    </item>
    
</selector>
