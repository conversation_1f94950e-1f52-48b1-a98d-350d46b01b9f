<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/count_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".module.count.CountCameraActivity">

    <!-- 顶部工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/count_toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:titleTextColor="@android:color/white"
        app:titleCentered="true"
        app:title="拍照计数"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@android:color/white">

        <!-- 工具栏右侧菜单按钮 -->
        <ImageButton
            android:id="@+id/count_settings_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="end"
            android:layout_marginEnd="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="相机设置"
            android:src="@drawable/ic_settings"
            android:tint="@android:color/white" />
    </com.google.android.material.appbar.MaterialToolbar>

    <!-- 相机预览区域 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/count_camera_preview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toTopOf="@id/count_zoom_container"
        app:layout_constraintDimensionRatio="3:4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/count_toolbar"
        app:scaleType="fillCenter" />

    <!-- 对焦指示器 -->
    <View
        android:id="@+id/count_focus_ring"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="@drawable/focus_ring"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/count_camera_preview"
        app:layout_constraintStart_toStartOf="@id/count_camera_preview"
        app:layout_constraintEnd_toEndOf="@id/count_camera_preview"
        app:layout_constraintBottom_toBottomOf="@id/count_camera_preview" />

    <!-- 缩放比例指示器 -->
    <TextView
        android:id="@+id/count_zoom_ratio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/zoom_indicator_background"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:text="1.0x"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        app:layout_constraintTop_toTopOf="@id/count_camera_preview"
        app:layout_constraintEnd_toEndOf="@id/count_camera_preview" />

    <!-- 水平缩放控制容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/count_zoom_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/zoom_container_background_horizontal"
        android:padding="12dp"
        app:layout_constraintBottom_toTopOf="@id/count_capture_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 最小缩放标签 -->
        <TextView
            android:id="@+id/count_zoom_min_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1x"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- 水平缩放滑块 -->
        <com.google.android.material.slider.Slider
            android:id="@+id/count_zoom_slider"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:stepSize="0.1"
            android:value="1.0"
            android:valueFrom="1.0"
            android:valueTo="10.0"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/count_zoom_min_label"
            app:layout_constraintEnd_toStartOf="@id/count_zoom_max_label"
            app:thumbColor="@color/white"
            app:thumbRadius="10dp"
            app:trackColor="@color/hIndex_blue"
            app:trackColorActive="@color/white"
            app:trackColorInactive="@color/hIndex_blue"
            app:trackHeight="6dp" />

        <!-- 最大缩放标签 -->
        <TextView
            android:id="@+id/count_zoom_max_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="10x"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>



    <!-- 底部拍照控制区域 -->
    <LinearLayout
        android:id="@+id/count_capture_container"
        android:layout_width="0dp"
        android:layout_height="120dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@android:color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 拍照按钮 -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/count_capture_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/ic_camera_24"
            android:contentDescription="拍照"
            app:fabSize="auto"
            app:fabCustomSize="80dp"
            app:backgroundTint="@color/hIndex_blue"
            app:tint="@android:color/white"
            app:elevation="8dp"
            app:borderWidth="0dp" />

    </LinearLayout>

    <!-- 相机状态指示器 -->
    <TextView
        android:id="@+id/count_camera_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/status_indicator_background"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:text="准备就绪"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/count_capture_container"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 右下角照片预览容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/count_photo_preview_container"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/photo_preview_background"
        android:padding="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/count_capture_container"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 照片预览 - 3:4比例 -->
        <ImageView
            android:id="@+id/count_photo_preview"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="8dp"
            android:scaleType="centerCrop"
            android:background="@drawable/photo_preview_frame"
            app:layout_constraintDimensionRatio="3:4"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/count_sharpness_score" />

        <!-- 清晰度分数 -->
        <TextView
            android:id="@+id/count_sharpness_score"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="清晰度: 0.00"
            android:textColor="@android:color/white"
            android:textSize="10sp"
            android:textAlignment="center"
            android:background="@drawable/sharpness_score_background"
            android:padding="4dp"
            app:layout_constraintBottom_toTopOf="@id/count_confirm_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 确认按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/count_confirm_button"
            style="@style/Widget.Material3.Button.UnelevatedButton"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:text="确认"
            android:textSize="10sp"
            android:backgroundTint="@color/hIndex_blue"
            android:textColor="@android:color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>