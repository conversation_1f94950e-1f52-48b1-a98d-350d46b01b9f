<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp">

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progress_indicator"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:indeterminate="true"
        app:indicatorColor="@color/primary_color"
        app:trackColor="@color/divider_color" />

    <TextView
        android:id="@+id/tv_loading_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="正在加载..."
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

</LinearLayout>
