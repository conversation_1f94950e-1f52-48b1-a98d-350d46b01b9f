package com.zhmiaobang.easydianapp.module.miacochang.feed

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Base64
import android.util.Log
import android.view.View
import android.widget.ArrayAdapter
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityEditFeedpointBinding
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.viewmodel.feed.FeedViewModel
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.RoundedCornersTransformation
import com.zhmiaobang.easydianapp.json.feed.FeedPointJson
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream

/**
 * 投料点编辑Activity
 *
 * 功能特性：
 * - 新建和编辑投料点信息
 * - 表单验证和数据保存
 * - 封面图片选择和上传
 * - 支持拍照和相册选择
 * - 完整的错误处理和用户反馈
 *
 * <AUTHOR> 4.0 sonnet
 */
class EditFeedpointActivity : BaseActivity() {

    companion object {
        private const val TAG = "EditFeedpointActivity"
        private const val EXTRA_FEED_POINT = "feed_point"
        private const val IMAGE_QUALITY = 80
        private const val MAX_BASE64_SIZE = 512 * 1024 // 512KB
        private val SUPPORTED_IMAGE_TYPES = arrayOf("image/jpeg", "image/png")
    }

    // ViewBinding
    private val binding: ActivityEditFeedpointBinding by lazy {
        ActivityEditFeedpointBinding.inflate(layoutInflater)
    }

    // ViewModel
    private val viewModel: FeedViewModel by viewModels()

    // 数据
    private var feedPoint: FeedPointJson? = null
    private var isEditMode: Boolean = false
    private var coverImageData: String? = null

    // 图片选择器
    private lateinit var galleryLauncher: ActivityResultLauncher<Intent>
    private lateinit var cameraLauncher: ActivityResultLauncher<Intent>

    // 权限请求
    private lateinit var cameraPermissionLauncher: ActivityResultLauncher<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initializeUI()
        setupObservers()
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()

        // 获取传入的数据
        feedPoint = intent.getParcelableExtra(EXTRA_FEED_POINT)
        isEditMode = feedPoint != null

        setupToolbar()
        setupStatusDropdown()
        setupImagePickers()
        setupClickListeners()

        // 如果是编辑模式，绑定数据
        if (isEditMode) {
            bindDataToUI()
        }
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()

        // 观察编辑结果
        viewModel.pointEditObserver.observe(this) { response ->
            hideLoading()

            try {
                when (response.code) {
                    200 -> {
                        Log.d(TAG, "投料点保存成功")
                        showToast("保存成功")

                        // 返回上一页
                        setResult(Activity.RESULT_OK)
                        finish()
                    }
                    else -> {
                        Log.w(TAG, "投料点保存失败: ${response.msg}")
                        showToast("保存失败: ${response.msg}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理保存结果失败: ${e.message}", e)
                showToast("保存失败，请重试")
            }
        }
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        val title = if (isEditMode) "编辑投料点" else "新建投料点"
        initToolbar(
            toolbar = binding.toolbar,
            title = title,
            showBack = true
        )
    }

    /**
     * 设置状态下拉选择
     */
    private fun setupStatusDropdown() {
        val statusOptions = arrayOf("有效", "无效")
        val adapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, statusOptions)
        binding.editStatus.setAdapter(adapter)

        // 设置默认值
        binding.editStatus.setText(statusOptions[0], false)
    }

    /**
     * 设置图片选择器
     */
    private fun setupImagePickers() {
        // 相册启动器
        galleryLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.data?.let { uri ->
                    handleImageUri(uri)
                }
            }
        }

        // 相机启动器
        cameraLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val bitmap = result.data?.extras?.get("data") as? Bitmap
                bitmap?.let { handleImageBitmap(it) }
            }
        }

        // 相机权限启动器
        cameraPermissionLauncher = registerForActivityResult(ActivityResultContracts.RequestPermission()) { granted ->
            if (granted) {
                launchCamera()
            } else {
                showToast("需要相机权限才能拍照")
            }
        }
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 图片预览区域点击
        binding.imagePreviewContainer.setOnClickListener {
            openGallery()
        }

        // 选择图片按钮
        binding.btnSelectImage.setOnClickListener {
            openGallery()
        }

        // 拍照按钮
        binding.btnTakePhoto.setOnClickListener {
            checkCameraPermissionAndTakePhoto()
        }

        // 删除图片按钮
        binding.imageDeleteBtn.setOnClickListener {
            clearImage()
        }

        // 保存按钮
        binding.btnSave.setOnClickListener {
            saveFeedPoint()
        }
    }

    /**
     * 绑定数据到UI
     */
    private fun bindDataToUI() {
        feedPoint?.let { point ->
            binding.editNo.setText(point.no)
            binding.editName.setText(point.name)
            binding.editDescription.setText(point.description ?: "")
            binding.editRemark.setText(point.remark_admin ?: "")
            binding.editFeedPeriod.setText(point.feedPeriod.toString())

            // 设置状态
            val statusText = if (point.status == 1) "有效" else "无效"
            binding.editStatus.setText(statusText, false)

            // 加载封面图片
            loadCoverImage(point.cover)
        }
    }

    /**
     * 加载封面图片
     */
    private fun loadCoverImage(coverUrl: String?) {
        if (coverUrl.isNullOrBlank()) return

        binding.imagePreview.load(coverUrl) {
            crossfade(300)
            placeholder(R.drawable.ic_image_placeholder)
            error(R.drawable.ic_image_placeholder)
            transformations(RoundedCornersTransformation(8f))
        }

        // 显示图片预览
        showImagePreview()
        coverImageData = coverUrl
    }

    /**
     * 打开相册
     */
    private fun openGallery() {
        val intent = Intent(Intent.ACTION_PICK).apply {
            type = "image/*"
            data = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            putExtra(Intent.EXTRA_MIME_TYPES, SUPPORTED_IMAGE_TYPES)
        }
        galleryLauncher.launch(intent)
    }

    /**
     * 检查相机权限并拍照
     */
    private fun checkCameraPermissionAndTakePhoto() {
        when {
            ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED -> {
                launchCamera()
            }
            else -> {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    /**
     * 启动相机
     */
    private fun launchCamera() {
        val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        cameraLauncher.launch(intent)
    }

    /**
     * 处理图片URI
     */
    private fun handleImageUri(uri: Uri) {
        try {
            val inputStream = contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()

            if (bitmap != null) {
                handleImageBitmap(bitmap)
            } else {
                showToast("图片加载失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理图片URI失败: ${e.message}", e)
            showToast("图片处理失败")
        }
    }

    /**
     * 处理图片Bitmap
     */
    private fun handleImageBitmap(bitmap: Bitmap) {
        try {
            // 压缩图片
            val compressedBitmap = compressImage(bitmap)

            // 转换为base64
            val base64String = bitmapToBase64(compressedBitmap)

            if (base64String != null) {
                // 显示预览
                binding.imagePreview.setImageBitmap(compressedBitmap)
                showImagePreview()

                // 保存数据
                coverImageData = base64String

                Log.d(TAG, "图片处理成功，Base64大小: ${base64String.length}字节")
            } else {
                showToast("图片处理失败，文件过大")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理图片失败: ${e.message}", e)
            showToast("图片处理失败")
        }
    }

    /**
     * 压缩图片
     */
    private fun compressImage(bitmap: Bitmap): Bitmap {
        val maxWidth = 800
        val maxHeight = 600

        val width = bitmap.width
        val height = bitmap.height

        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }

        val ratio = minOf(maxWidth.toFloat() / width, maxHeight.toFloat() / height)
        val newWidth = (width * ratio).toInt()
        val newHeight = (height * ratio).toInt()

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * 将Bitmap转换为Base64字符串
     */
    private fun bitmapToBase64(bitmap: Bitmap): String? {
        return try {
            val byteArrayOutputStream = ByteArrayOutputStream()
            var quality = IMAGE_QUALITY

            // 循环压缩直到满足大小要求
            do {
                byteArrayOutputStream.reset()
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, byteArrayOutputStream)
                quality -= 10
            } while (byteArrayOutputStream.size() > MAX_BASE64_SIZE && quality > 30)

            if (byteArrayOutputStream.size() > MAX_BASE64_SIZE) {
                Log.w(TAG, "图片压缩后仍然过大: ${byteArrayOutputStream.size()}字节")
                return null
            }

            val byteArray = byteArrayOutputStream.toByteArray()
            val base64 = Base64.encodeToString(byteArray, Base64.NO_WRAP)
            "data:image/jpeg;base64,$base64"
        } catch (e: Exception) {
            Log.e(TAG, "转换Base64失败: ${e.message}", e)
            null
        }
    }

    /**
     * 显示图片预览
     */
    private fun showImagePreview() {
        binding.imageEmptyState.visibility = View.GONE
        binding.imagePreview.visibility = View.VISIBLE
        binding.imageDeleteBtn.visibility = View.VISIBLE
    }

    /**
     * 清除图片
     */
    private fun clearImage() {
        binding.imageEmptyState.visibility = View.VISIBLE
        binding.imagePreview.visibility = View.GONE
        binding.imageDeleteBtn.visibility = View.GONE
        binding.imagePreview.setImageDrawable(null)
        coverImageData = null
    }

    /**
     * 保存投料点信息
     */
    private fun saveFeedPoint() {
        if (!validateForm()) {
            return
        }

        val no = binding.editNo.text.toString().trim()
        val name = binding.editName.text.toString().trim()
        val description = binding.editDescription.text.toString().trim().ifBlank { null }
        val remark = binding.editRemark.text.toString().trim().ifBlank { null }
        val feedPeriod = binding.editFeedPeriod.text.toString().toIntOrNull() ?: 1
        val status = if (binding.editStatus.text.toString() == "有效") 1 else 2

        // 创建或更新FeedPointJson对象
        val feedPointJson = if (isEditMode && feedPoint != null) {
            // 编辑模式：更新现有对象
            feedPoint!!.copy(
                no = no,
                name = name,
                description = description,
                remark_admin = remark,
                feedPeriod = feedPeriod,
                status = status,
                cover = coverImageData ?: feedPoint!!.cover
            )
        } else {
            // 新建模式：创建新对象
            FeedPointJson(
                id = 0, // 新建时ID为0，由服务器分配
                no = no,
                name = name,
                miaochang = 1, // TODO: 从用户信息获取苗场ID
                cover = coverImageData,
                code = generateCode(), // 生成唯一编码
                qrcode = generateQrCode(), // 生成二维码
                nfccode = generateNfcCode(), // 生成NFC码
                status = status,
                feedPeriod = feedPeriod,
                description = description,
                remark_admin = remark
            )
        }

        // 调用API保存
        showLoading()

        try {
            // 禁用缓存确保数据实时性
            RetrofitClient.setUseCache(false)

            viewModel.edit_feed_point(feedPointJson)
        } catch (e: Exception) {
            hideLoading()
            val errorMessage = ExceptionUtil.catchException(e)
            showToast("保存失败: $errorMessage")
            Log.e(TAG, "保存投料点失败: ${e.message}", e)
        }
    }

    /**
     * 表单验证
     */
    private fun validateForm(): Boolean {
        val no = binding.editNo.text.toString().trim()
        val name = binding.editName.text.toString().trim()
        val feedPeriod = binding.editFeedPeriod.text.toString().trim()

        // 清除之前的错误
        binding.editNoLayout.error = null
        binding.editNameLayout.error = null
        binding.editFeedPeriodLayout.error = null

        var isValid = true

        // 验证编号（必填）
        if (no.isBlank()) {
            binding.editNoLayout.error = "请输入投料点编号"
            isValid = false
        }

        // 验证名称（必填）
        if (name.isBlank()) {
            binding.editNameLayout.error = "请输入投料点名称"
            isValid = false
        }

        // 验证喂养周期（必填且为正整数）
        if (feedPeriod.isBlank()) {
            binding.editFeedPeriodLayout.error = "请输入喂养周期"
            isValid = false
        } else {
            val period = feedPeriod.toIntOrNull()
            if (period == null || period <= 0) {
                binding.editFeedPeriodLayout.error = "请输入有效的喂养周期（正整数）"
                isValid = false
            }
        }

        return isValid
    }

    /**
     * 生成唯一编码
     */
    private fun generateCode(): String {
        return "CODE_${System.currentTimeMillis()}"
    }

    /**
     * 生成二维码
     */
    private fun generateQrCode(): String {
        return "QR_${System.currentTimeMillis()}"
    }

    /**
     * 生成NFC码
     */
    private fun generateNfcCode(): String {
        return "NFC_${System.currentTimeMillis()}"
    }

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.progressIndicator.visibility = View.VISIBLE
        binding.btnSave.isEnabled = false
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.progressIndicator.visibility = View.GONE
        binding.btnSave.isEnabled = true
    }
}