package com.zhmiaobang.easydianapp.viewmodel.user

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.zhmiaobang.easydianapp.json.CommonResponseJson
import com.zhmiaobang.easydianapp.json.CommonRestfulJson
import com.zhmiaobang.easydianapp.json.employee.EmployeeLoginCodeJson
import com.zhmiaobang.easydianapp.json.user.UserJson
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.utils.PaginatedData
import com.zhmiaobang.easydianapp.utils.PaginationState
import com.zhmiaobang.easydianapp.viewmodel.BaseViewModel

/**
 * 员工管理相关的ViewModel
 *
 * 功能特性：
 * - 获取员工列表（支持分页）
 * - 编辑员工信息
 * - 数据状态管理
 * - 完整的错误处理和加载状态
 *
 * <AUTHOR> 4.0 sonnet
 */
class EmployeeViewModel: BaseViewModel() {

    companion object {
        private const val TAG = "EmployeeViewModel"
    }

    // 分页数据观察者
    val paginatedDataObserver: MutableLiveData<PaginatedData<UserJson>> by lazy {
        MutableLiveData<PaginatedData<UserJson>>()
    }

    // 员工编辑观察者
    val employeeEditObserver: MutableLiveData<CommonResponseJson<UserJson>> by lazy {
        MutableLiveData<CommonResponseJson<UserJson>>()
    }

    // 分页状态
    private var paginationState = PaginationState()

    // 所有已加载的员工数据
    private val allEmployees = LinkedHashSet<UserJson>()

    /**
     * 加载第一页数据（正常加载，使用缓存）
     */
    fun loadFirstPage() = launch({
        if (paginationState.isLoading) {
            Log.d(TAG, "正在加载中，忽略重复请求")
            return@launch
        }

        Log.d(TAG, "开始加载第一页数据（使用缓存）...")
        paginationState = paginationState.startFirstLoading()
        allEmployees.clear()

        try {
            // 正常加载使用缓存
            RetrofitClient.setUseCache(true)

            val response = RetrofitClient.apiService.get_employee_list()
            Log.d(TAG, "第一页加载成功: count=${response.count}, results数量=${response.results.size}")

            // 添加数据到集合（自动去重）
            allEmployees.addAll(response.results)

            // 更新分页状态 - 员工列表通常不分页，所以设置为无更多数据
            paginationState = paginationState.loadSuccess(false, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allEmployees.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "第一页加载失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "加载失败")

            val paginatedData = PaginatedData(
                items = allEmployees.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        }
    })

    /**
     * 刷新数据（禁用缓存，获取最新数据）
     */
    fun refresh() = launch({
        if (paginationState.isLoading) {
            Log.d(TAG, "正在加载中，忽略刷新请求")
            return@launch
        }

        Log.d(TAG, "刷新数据（禁用缓存）...")
        paginationState = paginationState.reset().startFirstLoading()
        allEmployees.clear()

        try {
            // 刷新时禁用缓存，确保获取最新数据
            RetrofitClient.setUseCache(false)

            val response = RetrofitClient.apiService.get_employee_list()
            Log.d(TAG, "刷新成功: count=${response.count}, results数量=${response.results.size}")

            // 添加数据到集合（自动去重）
            allEmployees.addAll(response.results)

            // 更新分页状态
            paginationState = paginationState.loadSuccess(false, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allEmployees.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "刷新失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "刷新失败")

            val paginatedData = PaginatedData(
                items = allEmployees.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        } finally {
            // 恢复缓存设置
            RetrofitClient.setUseCache(true)
        }
    })

    /**
     * 重试加载（禁用缓存，获取最新数据）
     */
    fun retryLoad() {
        Log.d(TAG, "重试加载（禁用缓存）")

        try {
            // 重试时禁用缓存，确保获取最新数据
            RetrofitClient.setUseCache(false)
            loadFirstPage()
        } finally {
            // 恢复缓存设置
            RetrofitClient.setUseCache(true)
        }
    }

    /**
     * 获取当前分页状态
     */
    fun getCurrentPaginationState(): PaginationState = paginationState

    /**
     * 编辑员工信息
     * 注意：目前API中没有员工编辑接口，这里预留实现
     */
    fun editEmployee(userJson: UserJson) = launch({
        Log.d(TAG, "开始编辑员工: ${userJson.nickname}")
        try {
            // TODO: 等待后端提供员工编辑接口
            // val response = RetrofitClient.apiService.edit_employee(userJson)
            // Log.d(TAG, "员工编辑请求成功: code=${response.code}")
            // employeeEditObserver.postValue(response)

            // 临时模拟成功响应

            employeeEditObserver.postValue(RetrofitClient.apiService.edit_employee(userJson))
            Log.d(TAG, "员工编辑模拟成功")
        } catch (e: Exception) {
            Log.e(TAG, "员工编辑请求失败: ${e.message}", e)
            throw e
        }
    })


    val getLoginCodeObserver: MutableLiveData<CommonResponseJson<EmployeeLoginCodeJson>> by lazy { MutableLiveData<CommonResponseJson<EmployeeLoginCodeJson>>() }

    fun get_login_qrcode(userJson: UserJson)=launch({
        getLoginCodeObserver.postValue(RetrofitClient.apiService.get_employee_login_code(userJson.id))
    }, onComplete = {
        completeObserver.postValue(true)
    }, onError = {
        val msg= ExceptionUtil.catchException(it)
        errorObserver.postValue(msg)
    })
}