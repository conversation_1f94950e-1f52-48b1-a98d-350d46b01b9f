package com.zhmiaobang.easydianapp.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.model.ImageItem

/**
 * ViewPager2 图片适配器
 * 用于在 MainActivity 中展示图片轮播
 * 优化版本 by Claude 4.0 sonnet
 */
class ImagePagerAdapter(private val imageItems: List<ImageItem>) : 
    RecyclerView.Adapter<ImagePagerAdapter.ImageViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImageViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_image_pager, parent, false)
        return ImageViewHolder(view)
    }

    override fun onBindViewHolder(holder: ImageViewHolder, position: Int) {
        holder.bind(imageItems[position])
    }

    override fun getItemCount(): Int = imageItems.size

    class ImageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val imageView: ImageView = itemView.findViewById(R.id.image_view)
        private val titleText: TextView = itemView.findViewById(R.id.title_text)
        private val descriptionText: TextView = itemView.findViewById(R.id.description_text)

        fun bind(imageItem: ImageItem) {
            // 使用 Coil 加载图片
            imageView.load(imageItem.imageRes) {
                crossfade(300)
                placeholder(R.drawable.ic_image_placeholder)
                error(R.drawable.ic_image_error)
            }

            titleText.text = imageItem.title
            descriptionText.text = imageItem.description
        }
    }
}
