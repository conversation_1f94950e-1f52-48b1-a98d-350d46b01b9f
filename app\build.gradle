plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id 'androidx.navigation.safeargs.kotlin'
    id 'org.jetbrains.kotlin.plugin.serialization' version '2.0.21'
    id("kotlin-kapt")
    id("kotlin-parcelize")
    id("com.google.devtools.ksp") version "2.1.21-2.0.1"
}

android {
    namespace 'com.zhmiaobang.easydianapp'
    compileSdk 36

    defaultConfig {
        applicationId "com.zhmiaobang.easydianapp"
        minSdk 28

        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }
    buildFeatures {
        viewBinding true
        buildConfig true
    }

    splits {
        abi {
            enable true
            universalApk false
            reset()
            //noinspection ChromeOsAbiSupport
            include 'arm64-v8a' // , 'x86', 'x86_64', 'arm64-v8a'
        }
    }
    buildToolsVersion '36.0.0'
    ndkVersion '29.0.13599879 rc2'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.androidx.activity
    implementation libs.androidx.legacy.support.v4

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core


    implementation libs.androidx.camera.core
    implementation libs.androidx.camera.camera2
    // If you want to additionally use the CameraX Lifecycle library
    implementation libs.androidx.camera.lifecycle
    // If you want to additionally use the CameraX VideoCapture library
    implementation libs.androidx.camera.video
    // If you want to additionally use the CameraX View class
    implementation libs.androidx.camera.view
    // If you want to additionally add CameraX ML Kit Vision Integration
    implementation libs.androidx.camera.mlkit.vision
    // If you want to additionally use the CameraX Extensions library
    implementation libs.androidx.camera.extensions

    implementation libs.androidx.concurrent.futures.ktx
    implementation libs.androidx.constraintlayout.v221
    implementation "androidx.core:core-splashscreen:1.2.0-beta02"
    implementation "androidx.drawerlayout:drawerlayout:1.2.0"
    implementation "androidx.exifinterface:exifinterface:1.4.1"
    def fragment_version = "1.8.8"
    // Kotlin
    implementation "androidx.fragment:fragment-ktx:$fragment_version"
    implementation "androidx.gridlayout:gridlayout:1.1.0"
    def lifecycle_version = "2.9.1"
    def arch_version = "2.2.0"

    // ViewModel
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"

    // LiveData
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    // Lifecycles only (without ViewModel or LiveData)
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"


    // Saved state module for ViewModel
    implementation "androidx.lifecycle:lifecycle-viewmodel-savedstate:$lifecycle_version"

    // ViewModel integration with Navigation3
    implementation libs.androidx.lifecycle.viewmodel.navigation3

    // Annotation processor
    kapt "androidx.lifecycle:lifecycle-compiler:$lifecycle_version"
//    // alternately - if using Java8, use the following instead of lifecycle-compiler
//    implementation "androidx.lifecycle:lifecycle-common-java8:$lifecycle_version"

    // optional - helpers for implementing LifecycleOwner in a Service
    implementation libs.androidx.lifecycle.service

    // optional - ProcessLifecycleOwner provides a lifecycle for the whole application process
    implementation libs.androidx.lifecycle.process

    // optional - ReactiveStreams support for LiveData
    implementation "androidx.lifecycle:lifecycle-reactivestreams-ktx:$lifecycle_version"

    // optional - Test helpers for LiveData
    testImplementation "androidx.arch.core:core-testing:$arch_version"

    // optional - Test helpers for Lifecycle runtime
    testImplementation "androidx.lifecycle:lifecycle-runtime-testing:$lifecycle_version"
    def nav_version = "2.9.1"

    // Jetpack Compose Integration
    implementation "androidx.navigation:navigation-compose:$nav_version"

    // Views/Fragments Integration
    implementation "androidx.navigation:navigation-fragment:$nav_version"
    implementation "androidx.navigation:navigation-ui:$nav_version"
    implementation "androidx.navigation:navigation-runtime-ktx:$nav_version"
    implementation libs.androidx.navigation.fragment.ktx
    implementation libs.androidx.navigation.ui.ktx
    // Feature module support for Fragments
    implementation libs.androidx.navigation.dynamic.features.fragment

    // Testing Navigation
    androidTestImplementation "androidx.navigation:navigation-testing:$nav_version"

    // JSON serialization library, works with the Kotlin serialization plugin.
    implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.7.3"

//    implementation "androidx.pdf:pdf-viewer-fragment:1.0.0-alpha09"
    implementation "androidx.recyclerview:recyclerview:1.4.0"
    // For control over item selection of both touch and mouse driven selection
    implementation libs.androidx.recyclerview.selection


    def room_version = "2.7.2"

    implementation "androidx.room:room-runtime:$room_version"
    implementation libs.androidx.room.ktx
    // If this project uses any Kotlin source, use Kotlin Symbol Processing (KSP)
    // See KSP Quickstart to add KSP to your build
    ksp "androidx.room:room-compiler:$room_version"




    // optional - Test helpers
    testImplementation "androidx.room:room-testing:$room_version"

    // optional - Paging 3 Integration
    implementation "androidx.room:room-paging:$room_version"
    implementation libs.androidx.work.runtime.ktx

    implementation libs.kotlinx.coroutines.android
    implementation project(':opencv')
    implementation 'com.squareup.retrofit2:retrofit:2.11.0' // 网络请求框架 Retrofit
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0' // Gson 转换器，配合 Retrofit 使用

    // ONNX Runtime for Android
    implementation 'com.microsoft.onnxruntime:onnxruntime-android:1.21.1'

    // 图片处理相关
    implementation 'androidx.exifinterface:exifinterface:1.3.7'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0' // OkHttp 核心库
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0' // OkHttp 日志拦截器
    implementation 'com.tencent:mmkv:2.2.2' // 腾讯 MMKV 高性能本地存储
    implementation 'io.coil-kt.coil3:coil:3.2.0' // Coil 图片加载库（新版）
    implementation 'io.coil-kt.coil3:coil-network-okhttp:3.2.0' // Coil 网络支持（OkHttp）
    implementation 'com.github.chrisbanes:PhotoView:2.3.0' // PhotoView 图片缩放库
    implementation 'com.huawei.hms:scanplus:2.12.0.301'
}