<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipe_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background_gradient_start">

        <!-- 顶部状态栏占位 -->
        <View
            android:id="@+id/status_bar_placeholder"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 应用Logo和标题 -->
        <ImageView
            android:id="@+id/app_logo"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="32dp"
            android:contentDescription="@string/app_name"
            android:src="@mipmap/ic_launcher"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/status_bar_placeholder" />

        <TextView
            android:id="@+id/app_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/app_name"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_logo" />

        <!-- ViewPager2 图片轮播 -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="0dp"
            android:layout_height="240dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="32dp"
            android:background="@drawable/rounded_corner_background"
            android:elevation="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_title" />

        <!-- 页面指示器 -->
        <LinearLayout
            android:id="@+id/dots_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view_pager" />

        <!-- 状态文本 -->
        <TextView
            android:id="@+id/sample_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="32dp"
            android:gravity="center"
            android:text="@string/startup_loading"
            android:textColor="@color/text_secondary"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/dots_indicator" />

        <!-- 加载进度条 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:indeterminateTint="@color/primary_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/sample_text" />

        <!-- 底部版权信息 -->
        <TextView
            android:id="@+id/copyright_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:text="@string/copyright_text"
            android:textColor="@color/text_hint"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>