<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>虾苗养殖管理系统</title>
    
    <!-- TailwindCSS v3.x CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome 6.4.x CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        /* iOS安全区域适配 */
        :root {
            --safe-area-inset-top: env(safe-area-inset-top, 44px);
            --safe-area-inset-bottom: env(safe-area-inset-bottom, 34px);
        }
        
        /* 移动端优化 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }
        
        input, textarea {
            -webkit-user-select: text;
            user-select: text;
        }
        
        /* 页面容器 */
        .app-container {
            width: 375px;
            min-height: 100vh;
            margin: 0 auto;
            background: #f8fafc;
            position: relative;
            overflow-x: hidden;
        }
        
        /* 页面切换动画 */
        .page {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            min-height: 100vh;
            background: #f8fafc;
            transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
            transform: translateX(100%);
            opacity: 0;
            z-index: 1;
        }
        
        .page.active {
            transform: translateX(0);
            opacity: 1;
            z-index: 10;
        }
        
        .page.prev {
            transform: translateX(-100%);
            opacity: 0;
            z-index: 1;
        }
        
        /* 按钮触控优化 */
        .btn-touch {
            min-height: 44px;
            min-width: 44px;
            touch-action: manipulation;
            transition: all 0.1s ease;
        }
        
        .btn-touch:active {
            transform: scale(0.95);
            opacity: 0.8;
        }
        
        /* 高对比度主题（户外使用） */
        .high-contrast {
            --primary: #1e40af;
            --primary-dark: #1e3a8a;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border: #d1d5db;
        }
        
        /* 加载动画 */
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* 震动效果模拟 */
        .shake {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        /* 底部导航安全区域 */
        .bottom-nav {
            padding-bottom: var(--safe-area-inset-bottom);
        }
        
        /* 顶部状态栏安全区域 */
        .top-safe {
            padding-top: var(--safe-area-inset-top);
        }
        
        /* 图表容器响应式 */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        /* 自定义滚动条 */
        .custom-scroll::-webkit-scrollbar {
            width: 4px;
        }
        
        .custom-scroll::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }
        
        /* 卡片阴影效果 */
        .card-shadow {
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }
        
        .card-shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- 应用容器 -->
    <div class="app-container high-contrast">
        
        <!-- 启动页面 -->
        <div id="splash-page" class="page active">
            <div class="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-600 to-blue-800 text-white">
                <div class="text-center">
                    <!-- Logo区域 -->
                    <div class="mb-8">
                        <div class="w-24 h-24 mx-auto bg-white rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-fish text-4xl text-blue-600"></i>
                        </div>
                        <h1 class="text-2xl font-bold mb-2">虾苗养殖管理</h1>
                        <p class="text-blue-100 text-sm">专业 · 高效 · 智能</p>
                    </div>
                    
                    <!-- 加载动画 -->
                    <div class="mb-8">
                        <div class="loading-spinner w-8 h-8 border-2 border-white border-t-transparent rounded-full mx-auto"></div>
                        <p class="text-sm text-blue-100 mt-4">正在初始化系统...</p>
                    </div>
                    
                    <!-- 版本信息 -->
                    <div class="text-xs text-blue-200">
                        <p>版本 v1.0.0</p>
                        <p>© 2024 智慧养殖科技</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 登录页面 -->
        <div id="login-page" class="page">
            <div class="min-h-screen bg-white">
                <!-- 顶部状态栏 -->
                <div class="top-safe bg-blue-600 text-white text-center py-2">
                    <div class="flex items-center justify-between px-4">
                        <div class="w-6"></div>
                        <span class="text-sm font-medium">员工登录</span>
                        <div class="w-6"></div>
                    </div>
                </div>

                <!-- 登录内容 -->
                <div class="px-6 py-8">
                    <!-- 公司Logo -->
                    <div class="text-center mb-12">
                        <div class="w-20 h-20 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-fish text-3xl text-blue-600"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800 mb-2">智慧养殖科技</h2>
                        <p class="text-sm text-gray-500">虾苗养殖管理系统</p>
                    </div>

                    <!-- 二维码扫描区域 -->
                    <div class="mb-8">
                        <div class="bg-gray-50 rounded-lg p-6 text-center">
                            <div class="w-48 h-48 mx-auto bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center mb-4">
                                <div class="text-center">
                                    <i class="fas fa-qrcode text-4xl text-gray-400 mb-2"></i>
                                    <p class="text-sm text-gray-500">请扫描员工二维码</p>
                                </div>
                            </div>
                            <button class="btn-touch bg-blue-600 text-white px-6 py-3 rounded-lg font-medium w-full" onclick="simulateQRScan()">
                                <i class="fas fa-camera mr-2"></i>
                                启动扫码登录
                            </button>
                        </div>
                    </div>

                    <!-- 手动输入备选 -->
                    <div class="mb-6">
                        <div class="text-center mb-4">
                            <span class="text-sm text-gray-500">或</span>
                        </div>
                        <button class="btn-touch w-full border border-gray-300 text-gray-700 py-3 rounded-lg font-medium" onclick="showManualLogin()">
                            <i class="fas fa-keyboard mr-2"></i>
                            手动输入工号
                        </button>
                    </div>

                    <!-- 手动登录表单（隐藏） -->
                    <div id="manual-login" class="hidden">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">员工工号</label>
                                <input type="text" id="employee-id" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入6位工号">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                <input type="password" id="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入密码">
                            </div>
                            <button class="btn-touch bg-blue-600 text-white px-6 py-3 rounded-lg font-medium w-full" onclick="manualLogin()">
                                <i class="fas fa-sign-in-alt mr-2"></i>
                                登录
                            </button>
                        </div>
                    </div>

                    <!-- 底部信息 -->
                    <div class="text-center text-xs text-gray-400 mt-8">
                        <p>网络状态: <span class="network-status text-green-500"><i class="fas fa-wifi"></i> 已连接</span></p>
                        <p class="mt-1">GPS定位: <span class="text-green-500"><i class="fas fa-map-marker-alt"></i> 养殖场A区</span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主页面 -->
        <div id="home-page" class="page">
            <div class="min-h-screen bg-gray-50">
                <!-- 顶部状态栏 -->
                <div class="top-safe bg-white border-b border-gray-200">
                    <div class="px-4 py-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-blue-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">张师傅</p>
                                    <p class="text-xs text-gray-500">A区管理员</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="text-right">
                                    <p class="text-xs text-gray-500">今日天气</p>
                                    <p class="text-sm font-medium text-gray-800">
                                        <i class="fas fa-sun text-yellow-500 mr-1"></i>26°C
                                    </p>
                                </div>
                                <button class="btn-touch p-2" onclick="Router.navigate('profile-page')">
                                    <i class="fas fa-cog text-gray-600"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 今日数据概览 -->
                <div class="px-4 py-4">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">今日概览</h3>
                    <div class="grid grid-cols-2 gap-3 mb-6">
                        <div class="bg-white rounded-lg p-4 card-shadow">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs text-gray-500 mb-1">虾苗计数</p>
                                    <p class="text-xl font-bold text-blue-600">1,248</p>
                                    <p class="text-xs text-green-600">
                                        <i class="fas fa-arrow-up mr-1"></i>+12%
                                    </p>
                                </div>
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-fish text-blue-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-4 card-shadow">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs text-gray-500 mb-1">平均体长</p>
                                    <p class="text-xl font-bold text-green-600">3.2cm</p>
                                    <p class="text-xs text-green-600">
                                        <i class="fas fa-arrow-up mr-1"></i>+0.3cm
                                    </p>
                                </div>
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-ruler text-green-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-4 card-shadow">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs text-gray-500 mb-1">投料次数</p>
                                    <p class="text-xl font-bold text-orange-600">8</p>
                                    <p class="text-xs text-gray-500">次/今日</p>
                                </div>
                                <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-seedling text-orange-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-4 card-shadow">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs text-gray-500 mb-1">存活率</p>
                                    <p class="text-xl font-bold text-purple-600">94.5%</p>
                                    <p class="text-xs text-green-600">
                                        <i class="fas fa-arrow-up mr-1"></i>+1.2%
                                    </p>
                                </div>
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-chart-line text-purple-600"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="px-4">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">功能菜单</h3>
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <button class="btn-touch bg-white rounded-lg p-6 card-shadow text-center" onclick="Router.navigate('count-page')">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-fish text-blue-600 text-xl"></i>
                            </div>
                            <p class="font-medium text-gray-800 mb-1">虾苗计数</p>
                            <p class="text-xs text-gray-500">AI智能计数</p>
                        </button>

                        <button class="btn-touch bg-white rounded-lg p-6 card-shadow text-center" onclick="Router.navigate('measure-page')">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-ruler text-green-600 text-xl"></i>
                            </div>
                            <p class="font-medium text-gray-800 mb-1">体长测量</p>
                            <p class="text-xs text-gray-500">精确测量分析</p>
                        </button>

                        <button class="btn-touch bg-white rounded-lg p-6 card-shadow text-center" onclick="Router.navigate('stats-page')">
                            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-chart-bar text-purple-600 text-xl"></i>
                            </div>
                            <p class="font-medium text-gray-800 mb-1">试水苗统计</p>
                            <p class="text-xs text-gray-500">数据分析报表</p>
                        </button>

                        <button class="btn-touch bg-white rounded-lg p-6 card-shadow text-center" onclick="Router.navigate('feed-page')">
                            <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-seedling text-orange-600 text-xl"></i>
                            </div>
                            <p class="font-medium text-gray-800 mb-1">投料管理</p>
                            <p class="text-xs text-gray-500">投料记录跟踪</p>
                        </button>
                    </div>
                </div>

                <!-- 底部导航 -->
                <div class="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-white border-t border-gray-200 bottom-nav">
                    <div class="flex items-center justify-around py-2">
                        <button class="btn-touch flex flex-col items-center py-2 px-3 text-blue-600">
                            <i class="fas fa-home text-lg mb-1"></i>
                            <span class="text-xs">首页</span>
                        </button>
                        <button class="btn-touch flex flex-col items-center py-2 px-3 text-gray-400" onclick="Router.navigate('count-page')">
                            <i class="fas fa-fish text-lg mb-1"></i>
                            <span class="text-xs">计数</span>
                        </button>
                        <button class="btn-touch flex flex-col items-center py-2 px-3 text-gray-400" onclick="Router.navigate('stats-page')">
                            <i class="fas fa-chart-bar text-lg mb-1"></i>
                            <span class="text-xs">统计</span>
                        </button>
                        <button class="btn-touch flex flex-col items-center py-2 px-3 text-gray-400" onclick="Router.navigate('feed-page')">
                            <i class="fas fa-seedling text-lg mb-1"></i>
                            <span class="text-xs">投料</span>
                        </button>
                        <button class="btn-touch flex flex-col items-center py-2 px-3 text-gray-400" onclick="Router.navigate('profile-page')">
                            <i class="fas fa-user text-lg mb-1"></i>
                            <span class="text-xs">我的</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 虾苗计数页面 -->
        <div id="count-page" class="page">
            <div class="min-h-screen bg-gray-50">
                <!-- 顶部导航 -->
                <div class="top-safe bg-white border-b border-gray-200">
                    <div class="flex items-center justify-between px-4 py-3">
                        <button class="btn-touch p-2" onclick="Router.navigate('home-page')">
                            <i class="fas fa-arrow-left text-gray-600"></i>
                        </button>
                        <h1 class="text-lg font-bold text-gray-800">虾苗计数</h1>
                        <button class="btn-touch p-2" onclick="showCountHistory()">
                            <i class="fas fa-history text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- 计数流程 -->
                <div class="px-4 py-4">
                    <!-- 步骤指示器 -->
                    <div class="flex items-center justify-center mb-6">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                            <div class="w-12 h-1 bg-blue-600 mx-2"></div>
                            <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-bold">2</div>
                            <div class="w-12 h-1 bg-gray-300 mx-2"></div>
                            <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-bold">3</div>
                        </div>
                    </div>

                    <!-- 手机号验证 -->
                    <div id="step-1" class="step-content">
                        <div class="bg-white rounded-lg p-6 card-shadow mb-4">
                            <h3 class="text-lg font-bold text-gray-800 mb-4">操作员验证</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">手机号码</label>
                                    <input type="tel" id="phone-input" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入11位手机号" maxlength="11">
                                    <p class="text-xs text-gray-500 mt-1">用于记录操作员信息</p>
                                </div>
                                <button class="btn-touch bg-blue-600 text-white px-6 py-3 rounded-lg font-medium w-full" onclick="validatePhone()">
                                    <i class="fas fa-check mr-2"></i>
                                    验证并继续
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 品种选择 -->
                    <div id="step-2" class="step-content hidden">
                        <div class="bg-white rounded-lg p-6 card-shadow mb-4">
                            <h3 class="text-lg font-bold text-gray-800 mb-4">选择虾苗品种</h3>
                            <div class="grid grid-cols-1 gap-3 mb-4">
                                <button class="btn-touch border-2 border-gray-200 rounded-lg p-4 text-left hover:border-blue-500 hover:bg-blue-50" onclick="selectBreed('白对虾')">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium text-gray-800">白对虾</p>
                                            <p class="text-sm text-gray-500">Litopenaeus vannamei</p>
                                        </div>
                                        <i class="fas fa-chevron-right text-gray-400"></i>
                                    </div>
                                </button>
                                <button class="btn-touch border-2 border-gray-200 rounded-lg p-4 text-left hover:border-blue-500 hover:bg-blue-50" onclick="selectBreed('斑节虾')">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium text-gray-800">斑节虾</p>
                                            <p class="text-sm text-gray-500">Penaeus monodon</p>
                                        </div>
                                        <i class="fas fa-chevron-right text-gray-400"></i>
                                    </div>
                                </button>
                                <button class="btn-touch border-2 border-gray-200 rounded-lg p-4 text-left hover:border-blue-500 hover:bg-blue-50" onclick="selectBreed('其他')">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium text-gray-800">其他品种</p>
                                            <p class="text-sm text-gray-500">自定义输入</p>
                                        </div>
                                        <i class="fas fa-chevron-right text-gray-400"></i>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 图像采集 -->
                    <div id="step-3" class="step-content hidden">
                        <div class="bg-white rounded-lg p-6 card-shadow mb-4">
                            <h3 class="text-lg font-bold text-gray-800 mb-4">图像采集</h3>

                            <!-- 相机预览区域 -->
                            <div class="bg-gray-100 rounded-lg mb-4 relative overflow-hidden">
                                <div class="aspect-square flex items-center justify-center">
                                    <div id="camera-preview" class="text-center">
                                        <i class="fas fa-camera text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-sm text-gray-500">点击拍照或选择图片</p>
                                    </div>
                                    <img id="preview-image" class="hidden w-full h-full object-cover" alt="预览图片">
                                </div>

                                <!-- 拍照按钮 -->
                                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                                    <button class="btn-touch w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center" onclick="takePhoto()">
                                        <i class="fas fa-camera text-xl text-gray-600"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="grid grid-cols-2 gap-3 mb-4">
                                <button class="btn-touch border border-gray-300 text-gray-700 py-3 rounded-lg font-medium" onclick="selectFromGallery()">
                                    <i class="fas fa-images mr-2"></i>
                                    相册选择
                                </button>
                                <button class="btn-touch bg-blue-600 text-white py-3 rounded-lg font-medium" onclick="startCounting()">
                                    <i class="fas fa-play mr-2"></i>
                                    开始计数
                                </button>
                            </div>

                            <!-- 计数结果 -->
                            <div id="count-result" class="hidden bg-blue-50 rounded-lg p-4">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-blue-600 mb-2" id="count-number">0</div>
                                    <p class="text-sm text-gray-600 mb-2">检测到虾苗数量</p>
                                    <div class="flex items-center justify-center text-sm text-gray-500">
                                        <span>置信度: </span>
                                        <span class="font-medium text-green-600 ml-1" id="confidence">95.2%</span>
                                    </div>
                                    <div class="mt-4 grid grid-cols-2 gap-3">
                                        <button class="btn-touch border border-gray-300 text-gray-700 py-2 rounded-lg" onclick="retakePhoto()">
                                            <i class="fas fa-redo mr-2"></i>
                                            重新拍照
                                        </button>
                                        <button class="btn-touch bg-green-600 text-white py-2 rounded-lg" onclick="saveCount()">
                                            <i class="fas fa-save mr-2"></i>
                                            保存结果
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 体长测量页面 -->
        <div id="measure-page" class="page">
            <div class="min-h-screen bg-gray-50">
                <!-- 顶部导航 -->
                <div class="top-safe bg-white border-b border-gray-200">
                    <div class="flex items-center justify-between px-4 py-3">
                        <button class="btn-touch p-2" onclick="Router.navigate('home-page')">
                            <i class="fas fa-arrow-left text-gray-600"></i>
                        </button>
                        <h1 class="text-lg font-bold text-gray-800">体长测量</h1>
                        <button class="btn-touch p-2" onclick="showMeasureHistory()">
                            <i class="fas fa-chart-line text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- 测量设置 -->
                <div class="px-4 py-4">
                    <!-- 苗池选择 -->
                    <div class="bg-white rounded-lg p-4 card-shadow mb-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-3">选择苗池</h3>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="pool-select">
                            <option value="">请选择苗池</option>
                            <option value="A1">A1池 - 白对虾</option>
                            <option value="A2">A2池 - 白对虾</option>
                            <option value="B1">B1池 - 斑节虾</option>
                            <option value="B2">B2池 - 斑节虾</option>
                            <option value="C1">C1池 - 混养</option>
                        </select>
                    </div>

                    <!-- 测量模式 -->
                    <div class="bg-white rounded-lg p-4 card-shadow mb-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-3">测量模式</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <button class="btn-touch border-2 border-blue-500 bg-blue-50 text-blue-700 py-3 rounded-lg font-medium" onclick="selectMeasureMode('precise')">
                                <i class="fas fa-crosshairs mb-2 block"></i>
                                精确模式
                                <span class="block text-xs text-blue-500 mt-1">高精度测量</span>
                            </button>
                            <button class="btn-touch border-2 border-gray-300 text-gray-700 py-3 rounded-lg font-medium" onclick="selectMeasureMode('fast')">
                                <i class="fas fa-bolt mb-2 block"></i>
                                快速模式
                                <span class="block text-xs text-gray-500 mt-1">批量测量</span>
                            </button>
                        </div>
                    </div>

                    <!-- 测量界面 -->
                    <div class="bg-white rounded-lg p-4 card-shadow mb-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-3">图像采集</h3>

                        <!-- 相机预览 -->
                        <div class="bg-gray-100 rounded-lg mb-4 relative overflow-hidden">
                            <div class="aspect-video flex items-center justify-center">
                                <div id="measure-camera-preview" class="text-center">
                                    <i class="fas fa-camera text-4xl text-gray-400 mb-2"></i>
                                    <p class="text-sm text-gray-500">拍摄虾苗进行测量</p>
                                    <p class="text-xs text-gray-400 mt-1">建议放置参考物（如硬币）</p>
                                </div>
                                <img id="measure-preview-image" class="hidden w-full h-full object-cover" alt="测量图片">
                            </div>

                            <!-- 测量标尺覆盖层 -->
                            <div id="measure-overlay" class="hidden absolute inset-0 pointer-events-none">
                                <svg class="w-full h-full">
                                    <line x1="50" y1="100" x2="150" y2="100" stroke="#ef4444" stroke-width="2"/>
                                    <circle cx="50" cy="100" r="3" fill="#ef4444"/>
                                    <circle cx="150" cy="100" r="3" fill="#ef4444"/>
                                    <text x="100" y="90" text-anchor="middle" fill="#ef4444" font-size="12">3.2cm</text>
                                </svg>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="grid grid-cols-2 gap-3 mb-4">
                            <button class="btn-touch border border-gray-300 text-gray-700 py-3 rounded-lg font-medium" onclick="takeMeasurePhoto()">
                                <i class="fas fa-camera mr-2"></i>
                                拍照测量
                            </button>
                            <button class="btn-touch bg-blue-600 text-white py-3 rounded-lg font-medium" onclick="startMeasuring()">
                                <i class="fas fa-ruler mr-2"></i>
                                开始测量
                            </button>
                        </div>
                    </div>

                    <!-- 测量结果 -->
                    <div id="measure-results" class="hidden bg-white rounded-lg p-4 card-shadow mb-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-3">测量结果</h3>
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <p class="text-xs text-gray-500 mb-1">平均体长</p>
                                <p class="text-2xl font-bold text-blue-600" id="avg-length">3.2cm</p>
                            </div>
                            <div class="text-center">
                                <p class="text-xs text-gray-500 mb-1">检测数量</p>
                                <p class="text-2xl font-bold text-green-600" id="detected-count">15</p>
                            </div>
                            <div class="text-center">
                                <p class="text-xs text-gray-500 mb-1">最大值</p>
                                <p class="text-lg font-bold text-orange-600" id="max-length">4.1cm</p>
                            </div>
                            <div class="text-center">
                                <p class="text-xs text-gray-500 mb-1">最小值</p>
                                <p class="text-lg font-bold text-purple-600" id="min-length">2.8cm</p>
                            </div>
                        </div>

                        <!-- 体长分布图表 -->
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">体长分布</h4>
                            <div class="chart-container">
                                <canvas id="length-chart"></canvas>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="grid grid-cols-2 gap-3">
                            <button class="btn-touch border border-gray-300 text-gray-700 py-2 rounded-lg" onclick="retakeMeasure()">
                                <i class="fas fa-redo mr-2"></i>
                                重新测量
                            </button>
                            <button class="btn-touch bg-green-600 text-white py-2 rounded-lg" onclick="saveMeasurement()">
                                <i class="fas fa-save mr-2"></i>
                                保存数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 试水苗统计页面 -->
        <div id="stats-page" class="page">
            <div class="min-h-screen bg-gray-50">
                <!-- 顶部导航 -->
                <div class="top-safe bg-white border-b border-gray-200">
                    <div class="flex items-center justify-between px-4 py-3">
                        <button class="btn-touch p-2" onclick="Router.navigate('home-page')">
                            <i class="fas fa-arrow-left text-gray-600"></i>
                        </button>
                        <h1 class="text-lg font-bold text-gray-800">试水苗统计</h1>
                        <button class="btn-touch p-2" onclick="exportStatsData()">
                            <i class="fas fa-download text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- 筛选器 -->
                <div class="px-4 py-4">
                    <div class="bg-white rounded-lg p-4 card-shadow mb-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-3">数据筛选</h3>
                        <div class="grid grid-cols-2 gap-3 mb-3">
                            <div>
                                <label class="block text-xs text-gray-500 mb-1">开始日期</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded text-sm" id="start-date">
                            </div>
                            <div>
                                <label class="block text-xs text-gray-500 mb-1">结束日期</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded text-sm" id="end-date">
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-3 mb-3">
                            <div>
                                <label class="block text-xs text-gray-500 mb-1">苗池</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded text-sm" id="filter-pool">
                                    <option value="">全部苗池</option>
                                    <option value="A1">A1池</option>
                                    <option value="A2">A2池</option>
                                    <option value="B1">B1池</option>
                                    <option value="B2">B2池</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs text-gray-500 mb-1">品种</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded text-sm" id="filter-breed">
                                    <option value="">全部品种</option>
                                    <option value="白对虾">白对虾</option>
                                    <option value="斑节虾">斑节虾</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn-touch bg-blue-600 text-white px-4 py-2 rounded-lg text-sm w-full" onclick="applyFilters()">
                            <i class="fas fa-filter mr-2"></i>
                            应用筛选
                        </button>
                    </div>

                    <!-- 统计概览 -->
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <div class="bg-white rounded-lg p-4 card-shadow text-center">
                            <p class="text-xs text-gray-500 mb-1">总投放量</p>
                            <p class="text-xl font-bold text-blue-600">125,680</p>
                            <p class="text-xs text-green-600">
                                <i class="fas fa-arrow-up mr-1"></i>+8.5%
                            </p>
                        </div>
                        <div class="bg-white rounded-lg p-4 card-shadow text-center">
                            <p class="text-xs text-gray-500 mb-1">平均存活率</p>
                            <p class="text-xl font-bold text-green-600">94.2%</p>
                            <p class="text-xs text-green-600">
                                <i class="fas fa-arrow-up mr-1"></i>+2.1%
                            </p>
                        </div>
                    </div>

                    <!-- 图表按钮 -->
                    <div class="mb-4">
                        <button class="btn-touch bg-blue-600 text-white px-4 py-2 rounded-lg text-sm w-full" onclick="showChartModal()">
                            <i class="fas fa-chart-line mr-2"></i>
                            查看统计图表
                        </button>
                    </div>

                    <!-- 投放记录列表 -->
                    <div class="bg-white rounded-lg card-shadow">
                        <div class="p-4 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-800">投放记录</h3>
                        </div>
                        <div class="max-h-96 overflow-y-auto custom-scroll" id="stats-list">
                            <!-- 记录项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 统计图表模态框 -->
                <div id="chart-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                    <div class="bg-white rounded-lg w-full max-w-sm">
                        <div class="p-4 border-b border-gray-200 flex items-center justify-between">
                            <h3 class="text-lg font-bold text-gray-800">统计图表</h3>
                            <button class="btn-touch p-2" onclick="closeChartModal()">
                                <i class="fas fa-times text-gray-600"></i>
                            </button>
                        </div>
                        <div class="p-4">
                            <div class="chart-container mb-4">
                                <canvas id="stats-chart"></canvas>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <button class="btn-touch border border-gray-300 text-gray-700 py-2 rounded-lg text-sm" onclick="showTrendChart()">
                                    <i class="fas fa-chart-line mr-2"></i>
                                    趋势图
                                </button>
                                <button class="btn-touch border border-gray-300 text-gray-700 py-2 rounded-lg text-sm" onclick="showPieChart()">
                                    <i class="fas fa-chart-pie mr-2"></i>
                                    分布图
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 投料管理页面 -->
        <div id="feed-page" class="page">
            <div class="min-h-screen bg-gray-50">
                <!-- 顶部导航 -->
                <div class="top-safe bg-white border-b border-gray-200">
                    <div class="flex items-center justify-between px-4 py-3">
                        <button class="btn-touch p-2" onclick="Router.navigate('home-page')">
                            <i class="fas fa-arrow-left text-gray-600"></i>
                        </button>
                        <h1 class="text-lg font-bold text-gray-800">投料管理</h1>
                        <button class="btn-touch p-2" onclick="showFeedHistory()">
                            <i class="fas fa-history text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- 投料流程 -->
                <div class="px-4 py-4">
                    <!-- 扫码识别投料点 -->
                    <div class="bg-white rounded-lg p-4 card-shadow mb-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-3">投料点识别</h3>
                        <div class="bg-gray-100 rounded-lg p-6 text-center mb-4">
                            <div class="w-32 h-32 mx-auto bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center mb-3">
                                <div class="text-center">
                                    <i class="fas fa-qrcode text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-xs text-gray-500">扫描投料点</p>
                                </div>
                            </div>
                            <button class="btn-touch bg-blue-600 text-white px-6 py-3 rounded-lg font-medium w-full" onclick="scanFeedPoint()">
                                <i class="fas fa-camera mr-2"></i>
                                扫描投料点二维码
                            </button>
                        </div>

                        <!-- 投料点信息 -->
                        <div id="feed-point-info" class="hidden bg-blue-50 rounded-lg p-3">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-blue-800" id="feed-point-name">A1池-投料点01</p>
                                    <p class="text-sm text-blue-600" id="feed-point-location">东南角投料台</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs text-blue-600">上次投料</p>
                                    <p class="text-sm font-medium text-blue-800">2小时前</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 投料包选择 -->
                    <div id="feed-selection" class="hidden">
                        <div class="bg-white rounded-lg p-4 card-shadow mb-4">
                            <h3 class="text-lg font-bold text-gray-800 mb-3">选择投料包</h3>
                            <div class="grid grid-cols-2 gap-3 mb-4">
                                <button class="btn-touch border-2 border-gray-200 rounded-lg p-3 text-center hover:border-blue-500 hover:bg-blue-50" onclick="selectFeedType('5kg')">
                                    <i class="fas fa-box text-2xl text-gray-600 mb-2"></i>
                                    <p class="font-medium text-gray-800">5kg装</p>
                                    <p class="text-xs text-gray-500">标准投料包</p>
                                </button>
                                <button class="btn-touch border-2 border-gray-200 rounded-lg p-3 text-center hover:border-blue-500 hover:bg-blue-50" onclick="selectFeedType('10kg')">
                                    <i class="fas fa-boxes text-2xl text-gray-600 mb-2"></i>
                                    <p class="font-medium text-gray-800">10kg装</p>
                                    <p class="text-xs text-gray-500">大容量包装</p>
                                </button>
                                <button class="btn-touch border-2 border-gray-200 rounded-lg p-3 text-center hover:border-blue-500 hover:bg-blue-50" onclick="selectFeedType('custom')">
                                    <i class="fas fa-balance-scale text-2xl text-gray-600 mb-2"></i>
                                    <p class="font-medium text-gray-800">自定义</p>
                                    <p class="text-xs text-gray-500">手动输入重量</p>
                                </button>
                                <button class="btn-touch border-2 border-gray-200 rounded-lg p-3 text-center hover:border-blue-500 hover:bg-blue-50" onclick="selectFeedType('supplement')">
                                    <i class="fas fa-pills text-2xl text-gray-600 mb-2"></i>
                                    <p class="font-medium text-gray-800">营养补充</p>
                                    <p class="text-xs text-gray-500">维生素添加剂</p>
                                </button>
                            </div>

                            <!-- 自定义重量输入 -->
                            <div id="custom-weight" class="hidden mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">投料重量 (kg)</label>
                                <input type="number" step="0.1" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入重量" id="feed-weight">
                            </div>

                            <!-- 投料包数量 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">投料包数量</label>
                                <div class="flex items-center space-x-3">
                                    <button class="btn-touch w-10 h-10 border border-gray-300 rounded-lg flex items-center justify-center" onclick="adjustQuantity(-1)">
                                        <i class="fas fa-minus text-gray-600"></i>
                                    </button>
                                    <input type="number" min="1" value="1" class="w-20 text-center px-3 py-2 border border-gray-300 rounded-lg" id="feed-quantity">
                                    <button class="btn-touch w-10 h-10 border border-gray-300 rounded-lg flex items-center justify-center" onclick="adjustQuantity(1)">
                                        <i class="fas fa-plus text-gray-600"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 操作员拍照 -->
                        <div class="bg-white rounded-lg p-4 card-shadow mb-4">
                            <h3 class="text-lg font-bold text-gray-800 mb-3">操作员确认</h3>
                            <div class="bg-gray-100 rounded-lg mb-3 relative overflow-hidden">
                                <div class="aspect-square flex items-center justify-center">
                                    <div id="operator-camera-preview" class="text-center">
                                        <i class="fas fa-user-circle text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-sm text-gray-500">前置摄像头拍照</p>
                                        <p class="text-xs text-gray-400">记录操作员信息</p>
                                    </div>
                                    <img id="operator-photo" class="hidden w-full h-full object-cover" alt="操作员照片">
                                </div>
                            </div>
                            <button class="btn-touch bg-green-600 text-white px-6 py-3 rounded-lg font-medium w-full" onclick="takeOperatorPhoto()">
                                <i class="fas fa-camera mr-2"></i>
                                拍照确认身份
                            </button>
                        </div>

                        <!-- 投料确认 -->
                        <div class="bg-white rounded-lg p-4 card-shadow mb-4">
                            <h3 class="text-lg font-bold text-gray-800 mb-3">投料确认</h3>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                                <div class="flex items-start">
                                    <i class="fas fa-exclamation-triangle text-yellow-600 mt-1 mr-2"></i>
                                    <div>
                                        <p class="text-sm font-medium text-yellow-800">投料提醒</p>
                                        <p class="text-xs text-yellow-700 mt-1">请确认投料点位置正确，避免过量投料</p>
                                    </div>
                                </div>
                            </div>
                            <button class="btn-touch bg-orange-600 text-white px-6 py-3 rounded-lg font-medium w-full" onclick="confirmFeeding()">
                                <i class="fas fa-check mr-2"></i>
                                确认投料完成
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 个人中心页面 -->
        <div id="profile-page" class="page">
            <div class="min-h-screen bg-gray-50">
                <!-- 顶部导航 -->
                <div class="top-safe bg-white border-b border-gray-200">
                    <div class="flex items-center justify-between px-4 py-3">
                        <button class="btn-touch p-2" onclick="Router.navigate('home-page')">
                            <i class="fas fa-arrow-left text-gray-600"></i>
                        </button>
                        <h1 class="text-lg font-bold text-gray-800">个人中心</h1>
                        <div class="w-8"></div>
                    </div>
                </div>

                <!-- 用户信息 -->
                <div class="px-4 py-4">
                    <div class="bg-white rounded-lg p-6 card-shadow mb-4">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-2xl text-blue-600"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-bold text-gray-800">张师傅</h3>
                                <p class="text-sm text-gray-500">工号: EMP001</p>
                                <p class="text-sm text-gray-500">A区管理员</p>
                            </div>
                            <button class="btn-touch p-2 text-gray-400">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                        <div class="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                            <div class="text-center">
                                <p class="text-lg font-bold text-blue-600">156</p>
                                <p class="text-xs text-gray-500">工作天数</p>
                            </div>
                            <div class="text-center">
                                <p class="text-lg font-bold text-green-600">98.5%</p>
                                <p class="text-xs text-gray-500">出勤率</p>
                            </div>
                            <div class="text-center">
                                <p class="text-lg font-bold text-orange-600">A+</p>
                                <p class="text-xs text-gray-500">绩效评级</p>
                            </div>
                        </div>
                    </div>

                    <!-- 工作统计 -->
                    <div class="bg-white rounded-lg p-4 card-shadow mb-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">本月工作统计</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-fish text-blue-600 text-sm"></i>
                                    </div>
                                    <span class="text-gray-700">虾苗计数</span>
                                </div>
                                <span class="font-bold text-gray-800">248次</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-ruler text-green-600 text-sm"></i>
                                    </div>
                                    <span class="text-gray-700">体长测量</span>
                                </div>
                                <span class="font-bold text-gray-800">89次</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-seedling text-orange-600 text-sm"></i>
                                    </div>
                                    <span class="text-gray-700">投料管理</span>
                                </div>
                                <span class="font-bold text-gray-800">156次</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-chart-bar text-purple-600 text-sm"></i>
                                    </div>
                                    <span class="text-gray-700">数据统计</span>
                                </div>
                                <span class="font-bold text-gray-800">67次</span>
                            </div>
                        </div>
                    </div>

                    <!-- 设置选项 -->
                    <div class="bg-white rounded-lg card-shadow mb-4">
                        <div class="p-4 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-800">系统设置</h3>
                        </div>
                        <div class="divide-y divide-gray-200">
                            <button class="btn-touch w-full px-4 py-4 flex items-center justify-between text-left" onclick="toggleNotifications()">
                                <div class="flex items-center">
                                    <i class="fas fa-bell text-gray-600 mr-3"></i>
                                    <span class="text-gray-700">通知设置</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm text-green-600 mr-2">已开启</span>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </button>
                            <button class="btn-touch w-full px-4 py-4 flex items-center justify-between text-left" onclick="changeTheme()">
                                <div class="flex items-center">
                                    <i class="fas fa-palette text-gray-600 mr-3"></i>
                                    <span class="text-gray-700">主题模式</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-500 mr-2">高对比度</span>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </button>
                            <button class="btn-touch w-full px-4 py-4 flex items-center justify-between text-left" onclick="changeLanguage()">
                                <div class="flex items-center">
                                    <i class="fas fa-globe text-gray-600 mr-3"></i>
                                    <span class="text-gray-700">语言设置</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-500 mr-2">简体中文</span>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </button>
                            <button class="btn-touch w-full px-4 py-4 flex items-center justify-between text-left" onclick="showHelp()">
                                <div class="flex items-center">
                                    <i class="fas fa-question-circle text-gray-600 mr-3"></i>
                                    <span class="text-gray-700">帮助文档</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </button>
                            <button class="btn-touch w-full px-4 py-4 flex items-center justify-between text-left" onclick="showAbout()">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle text-gray-600 mr-3"></i>
                                    <span class="text-gray-700">关于应用</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 退出登录 -->
                    <div class="bg-white rounded-lg card-shadow mb-20">
                        <button class="btn-touch w-full px-4 py-4 text-red-600 font-medium" onclick="logout()">
                            <i class="fas fa-sign-out-alt mr-3"></i>
                            退出登录
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <!-- JavaScript核心功能 -->
    <script>
        // 应用状态管理
        const AppState = {
            currentPage: 'splash-page',
            user: null,
            data: {
                shrimpCounts: [],
                measurements: [],
                feedRecords: [],
                waterTests: []
            }
        };
        
        // 路由管理系统
        const Router = {
            navigate(pageId, addToHistory = true) {
                const currentPage = document.querySelector('.page.active');
                const targetPage = document.getElementById(pageId);
                
                if (!targetPage || pageId === AppState.currentPage) return;
                
                // 页面切换动画
                if (currentPage) {
                    currentPage.classList.remove('active');
                    currentPage.classList.add('prev');
                }
                
                targetPage.classList.remove('prev');
                targetPage.classList.add('active');
                
                // 更新状态
                AppState.currentPage = pageId;
                
                // 更新浏览器历史
                if (addToHistory) {
                    history.pushState({ page: pageId }, '', `#${pageId}`);
                }
                
                // 清理动画类
                setTimeout(() => {
                    document.querySelectorAll('.page.prev').forEach(page => {
                        page.classList.remove('prev');
                    });
                }, 300);
            },
            
            back() {
                history.back();
            }
        };
        
        // 浏览器历史记录处理
        window.addEventListener('popstate', (event) => {
            if (event.state && event.state.page) {
                Router.navigate(event.state.page, false);
            }
        });
        
        // 登录相关功能
        function simulateQRScan() {
            // 模拟二维码扫描
            const button = event.target;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>扫描中...';
            button.disabled = true;

            setTimeout(() => {
                // 模拟扫描成功
                AppState.user = {
                    id: 'EMP001',
                    name: '张师傅',
                    role: '管理员',
                    phone: '13800138000'
                };
                Router.navigate('home-page');
            }, 2000);
        }

        function showManualLogin() {
            document.getElementById('manual-login').classList.remove('hidden');
        }

        function manualLogin() {
            const employeeId = document.getElementById('employee-id').value;
            const password = document.getElementById('password').value;

            if (!employeeId || !password) {
                alert('请填写完整信息');
                return;
            }

            // 模拟登录验证
            AppState.user = {
                id: employeeId,
                name: '张师傅',
                role: '管理员',
                phone: '13800138000'
            };
            Router.navigate('home-page');
        }

        // 虾苗计数相关功能
        let currentStep = 1;
        let selectedBreed = '';
        let operatorPhone = '';

        function validatePhone() {
            const phone = document.getElementById('phone-input').value;
            const phoneRegex = /^1[3-9]\d{9}$/;

            if (!phoneRegex.test(phone)) {
                document.getElementById('phone-input').classList.add('shake');
                setTimeout(() => {
                    document.getElementById('phone-input').classList.remove('shake');
                }, 500);
                alert('请输入正确的11位手机号');
                return;
            }

            operatorPhone = phone;
            nextStep();
        }

        function selectBreed(breed) {
            selectedBreed = breed;
            nextStep();
        }

        function nextStep() {
            // 隐藏当前步骤
            document.getElementById(`step-${currentStep}`).classList.add('hidden');

            // 更新步骤指示器
            const currentIndicator = document.querySelector(`.step-content:nth-child(${currentStep + 2}) .w-8`);
            if (currentIndicator) {
                currentIndicator.classList.remove('bg-gray-300', 'text-gray-500');
                currentIndicator.classList.add('bg-blue-600', 'text-white');
            }

            currentStep++;

            // 显示下一步骤
            document.getElementById(`step-${currentStep}`).classList.remove('hidden');

            // 更新步骤指示器
            updateStepIndicator();
        }

        function updateStepIndicator() {
            const indicators = document.querySelectorAll('.w-8.h-8.rounded-full');
            const lines = document.querySelectorAll('.w-12.h-1');

            indicators.forEach((indicator, index) => {
                if (index < currentStep) {
                    indicator.classList.remove('bg-gray-300', 'text-gray-500');
                    indicator.classList.add('bg-blue-600', 'text-white');
                } else {
                    indicator.classList.remove('bg-blue-600', 'text-white');
                    indicator.classList.add('bg-gray-300', 'text-gray-500');
                }
            });

            lines.forEach((line, index) => {
                if (index < currentStep - 1) {
                    line.classList.remove('bg-gray-300');
                    line.classList.add('bg-blue-600');
                } else {
                    line.classList.remove('bg-blue-600');
                    line.classList.add('bg-gray-300');
                }
            });
        }

        function takePhoto() {
            // 模拟拍照功能
            const preview = document.getElementById('preview-image');
            const cameraPreview = document.getElementById('camera-preview');

            // 使用Unsplash的虾苗图片作为示例
            preview.src = 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=400&fit=crop';
            preview.classList.remove('hidden');
            cameraPreview.classList.add('hidden');
        }

        function selectFromGallery() {
            // 模拟从相册选择
            takePhoto(); // 使用相同的预览逻辑
        }

        function startCounting() {
            const preview = document.getElementById('preview-image');
            if (preview.classList.contains('hidden')) {
                alert('请先拍照或选择图片');
                return;
            }

            // 模拟AI计数过程
            const resultDiv = document.getElementById('count-result');
            const countNumber = document.getElementById('count-number');
            const confidence = document.getElementById('confidence');

            // 显示加载状态
            countNumber.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            resultDiv.classList.remove('hidden');

            setTimeout(() => {
                // 模拟计数结果
                const randomCount = Math.floor(Math.random() * 200) + 50;
                const randomConfidence = (Math.random() * 10 + 90).toFixed(1);

                countNumber.textContent = randomCount;
                confidence.textContent = randomConfidence + '%';
            }, 2000);
        }

        function retakePhoto() {
            const preview = document.getElementById('preview-image');
            const cameraPreview = document.getElementById('camera-preview');
            const resultDiv = document.getElementById('count-result');

            preview.classList.add('hidden');
            cameraPreview.classList.remove('hidden');
            resultDiv.classList.add('hidden');
        }

        function saveCount() {
            const count = document.getElementById('count-number').textContent;
            const confidence = document.getElementById('confidence').textContent;

            // 保存到应用状态
            const record = {
                id: Date.now(),
                count: parseInt(count),
                confidence: confidence,
                breed: selectedBreed,
                operator: operatorPhone,
                timestamp: new Date().toISOString(),
                date: new Date().toLocaleDateString('zh-CN')
            };

            AppState.data.shrimpCounts.push(record);

            alert('计数结果已保存！');
            Router.navigate('home-page');
        }

        function showCountHistory() {
            // 这里可以显示历史记录模态框
            alert('历史记录功能（演示版本）');
        }

        // 页面特定的初始化函数
        function initializePage(pageId) {
            switch(pageId) {
                case 'count-page':
                    // 重置计数页面状态
                    currentStep = 1;
                    document.querySelectorAll('.step-content').forEach(step => step.classList.add('hidden'));
                    document.getElementById('step-1').classList.remove('hidden');
                    updateStepIndicator();
                    break;
                case 'measure-page':
                    // 重置测量页面状态
                    document.getElementById('measure-results').classList.add('hidden');
                    document.getElementById('measure-overlay').classList.add('hidden');
                    break;
                case 'feed-page':
                    // 重置投料页面状态
                    document.getElementById('feed-point-info').classList.add('hidden');
                    document.getElementById('feed-selection').classList.add('hidden');
                    selectedFeedType = '';
                    break;
                case 'stats-page':
                    // 设置默认日期范围
                    const today = new Date();
                    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    document.getElementById('start-date').value = weekAgo.toISOString().split('T')[0];
                    document.getElementById('end-date').value = today.toISOString().split('T')[0];
                    break;
            }
        }

        // 更新路由系统以支持页面初始化
        const OriginalNavigate = Router.navigate;
        Router.navigate = function(pageId, addToHistory = true) {
            OriginalNavigate.call(this, pageId, addToHistory);
            initializePage(pageId);
        };

        // 添加网络状态检测
        function checkNetworkStatus() {
            const isOnline = navigator.onLine;
            const statusElements = document.querySelectorAll('.network-status');
            statusElements.forEach(el => {
                if (isOnline) {
                    el.innerHTML = '<span class="text-green-500"><i class="fas fa-wifi"></i> 已连接</span>';
                } else {
                    el.innerHTML = '<span class="text-red-500"><i class="fas fa-wifi-slash"></i> 离线模式</span>';
                }
            });
        }

        // 监听网络状态变化
        window.addEventListener('online', checkNetworkStatus);
        window.addEventListener('offline', checkNetworkStatus);

        // 添加触摸反馈
        function addTouchFeedback() {
            document.addEventListener('touchstart', function(e) {
                if (e.target.classList.contains('btn-touch')) {
                    e.target.style.transform = 'scale(0.95)';
                    e.target.style.opacity = '0.8';
                }
            });

            document.addEventListener('touchend', function(e) {
                if (e.target.classList.contains('btn-touch')) {
                    setTimeout(() => {
                        e.target.style.transform = '';
                        e.target.style.opacity = '';
                    }, 100);
                }
            });
        }

        // 添加键盘支持
        document.addEventListener('keydown', function(e) {
            // ESC键返回上一页
            if (e.key === 'Escape' && AppState.currentPage !== 'home-page') {
                Router.back();
            }

            // Enter键提交表单
            if (e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement.tagName === 'INPUT') {
                    const form = activeElement.closest('.step-content, .bg-white');
                    const submitBtn = form?.querySelector('button[onclick*="validate"], button[onclick*="Login"], button[onclick*="confirm"]');
                    if (submitBtn) {
                        submitBtn.click();
                    }
                }
            }
        });

        // 添加图片懒加载
        function lazyLoadImages() {
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        observer.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }

        // 添加错误处理
        window.addEventListener('error', function(e) {
            console.error('应用错误:', e.error);
            // 在生产环境中，这里可以发送错误报告
        });

        // 应用初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化模拟数据
            initMockData();

            // 初始化功能
            checkNetworkStatus();
            addTouchFeedback();
            lazyLoadImages();

            // 3秒后自动跳转到登录页面
            setTimeout(() => {
                Router.navigate('login-page');
            }, 3000);
        });

        // 体长测量相关功能
        let measureMode = 'precise';
        let selectedPool = '';

        function selectMeasureMode(mode) {
            measureMode = mode;
            // 更新UI显示
            document.querySelectorAll('[onclick*="selectMeasureMode"]').forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50', 'text-blue-700');
                btn.classList.add('border-gray-300', 'text-gray-700');
            });
            event.target.classList.remove('border-gray-300', 'text-gray-700');
            event.target.classList.add('border-blue-500', 'bg-blue-50', 'text-blue-700');
        }

        function takeMeasurePhoto() {
            const preview = document.getElementById('measure-preview-image');
            const cameraPreview = document.getElementById('measure-camera-preview');

            // 使用虾苗测量图片
            preview.src = 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop';
            preview.classList.remove('hidden');
            cameraPreview.classList.add('hidden');
        }

        function startMeasuring() {
            const preview = document.getElementById('measure-preview-image');
            if (preview.classList.contains('hidden')) {
                alert('请先拍照');
                return;
            }

            const pool = document.getElementById('pool-select').value;
            if (!pool) {
                alert('请选择苗池');
                return;
            }

            // 显示测量覆盖层
            document.getElementById('measure-overlay').classList.remove('hidden');

            // 模拟测量过程
            setTimeout(() => {
                showMeasureResults();
            }, 2000);
        }

        function showMeasureResults() {
            const resultsDiv = document.getElementById('measure-results');

            // 生成随机测量数据
            const avgLength = (Math.random() * 1.5 + 2.5).toFixed(1);
            const detectedCount = Math.floor(Math.random() * 20) + 10;
            const maxLength = (parseFloat(avgLength) + Math.random() * 0.8).toFixed(1);
            const minLength = (parseFloat(avgLength) - Math.random() * 0.5).toFixed(1);

            document.getElementById('avg-length').textContent = avgLength + 'cm';
            document.getElementById('detected-count').textContent = detectedCount;
            document.getElementById('max-length').textContent = maxLength + 'cm';
            document.getElementById('min-length').textContent = minLength + 'cm';

            resultsDiv.classList.remove('hidden');

            // 创建体长分布图表
            createLengthChart();
        }

        function createLengthChart() {
            const ctx = document.getElementById('length-chart').getContext('2d');

            // 生成模拟分布数据
            const labels = ['2.0-2.5', '2.5-3.0', '3.0-3.5', '3.5-4.0', '4.0-4.5'];
            const data = [2, 5, 8, 4, 1];

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '虾苗数量',
                        data: data,
                        backgroundColor: 'rgba(59, 130, 246, 0.5)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function retakeMeasure() {
            const preview = document.getElementById('measure-preview-image');
            const cameraPreview = document.getElementById('measure-camera-preview');
            const resultsDiv = document.getElementById('measure-results');
            const overlay = document.getElementById('measure-overlay');

            preview.classList.add('hidden');
            cameraPreview.classList.remove('hidden');
            resultsDiv.classList.add('hidden');
            overlay.classList.add('hidden');
        }

        function saveMeasurement() {
            const avgLength = document.getElementById('avg-length').textContent;
            const detectedCount = document.getElementById('detected-count').textContent;
            const pool = document.getElementById('pool-select').value;

            const record = {
                id: Date.now(),
                pool: pool,
                avgLength: avgLength,
                count: parseInt(detectedCount),
                mode: measureMode,
                timestamp: new Date().toISOString(),
                date: new Date().toLocaleDateString('zh-CN')
            };

            AppState.data.measurements.push(record);
            alert('测量数据已保存！');
            Router.navigate('home-page');
        }

        function showMeasureHistory() {
            alert('测量历史功能（演示版本）');
        }

        // 统计页面相关功能
        function applyFilters() {
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            const pool = document.getElementById('filter-pool').value;
            const breed = document.getElementById('filter-breed').value;

            // 重新渲染统计列表
            renderStatsList();
        }

        function renderStatsList() {
            const listContainer = document.getElementById('stats-list');

            // 生成模拟统计数据
            const statsData = [];
            for (let i = 0; i < 50; i++) {
                const date = new Date();
                date.setDate(date.getDate() - Math.floor(Math.random() * 60));

                statsData.push({
                    id: i + 1,
                    date: date.toLocaleDateString('zh-CN'),
                    time: `${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
                    pool: ['A1池', 'A2池', 'B1池', 'B2池'][Math.floor(Math.random() * 4)],
                    breed: ['白对虾', '斑节虾'][Math.floor(Math.random() * 2)],
                    quantity: Math.floor(Math.random() * 5000) + 1000,
                    survival: (Math.random() * 10 + 85).toFixed(1),
                    operator: ['张师傅', '李师傅', '王师傅'][Math.floor(Math.random() * 3)],
                    status: ['已投放', '监测中', '已完成'][Math.floor(Math.random() * 3)]
                });
            }

            // 渲染列表
            listContainer.innerHTML = statsData.map(item => `
                <div class="p-4 border-b border-gray-200 last:border-b-0">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full ${item.status === '已完成' ? 'bg-green-500' : item.status === '监测中' ? 'bg-yellow-500' : 'bg-blue-500'} mr-2"></div>
                            <span class="font-medium text-gray-800">${item.pool} - ${item.breed}</span>
                        </div>
                        <span class="text-xs text-gray-500">${item.date} ${item.time}</span>
                    </div>
                    <div class="grid grid-cols-3 gap-2 text-sm">
                        <div>
                            <p class="text-xs text-gray-500">投放数量</p>
                            <p class="font-medium text-blue-600">${item.quantity.toLocaleString()}</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">存活率</p>
                            <p class="font-medium text-green-600">${item.survival}%</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">操作员</p>
                            <p class="font-medium text-gray-700">${item.operator}</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function exportStatsData() {
            alert('数据导出功能（演示版本）\n将生成Excel文件并通过邮件发送');
        }

        // 初始化模拟数据
        function initMockData() {
            // 生成虾苗计数历史记录
            for (let i = 0; i < 30; i++) {
                const date = new Date();
                date.setDate(date.getDate() - Math.floor(Math.random() * 30));

                AppState.data.shrimpCounts.push({
                    id: Date.now() + i,
                    count: Math.floor(Math.random() * 300) + 100,
                    confidence: (Math.random() * 10 + 85).toFixed(1) + '%',
                    breed: ['白对虾', '斑节虾'][Math.floor(Math.random() * 2)],
                    operator: '138****' + String(Math.floor(Math.random() * 10000)).padStart(4, '0'),
                    timestamp: date.toISOString(),
                    date: date.toLocaleDateString('zh-CN')
                });
            }

            // 初始化统计页面数据
            renderStatsList();
        }

        // 投料管理相关功能
        let selectedFeedType = '';
        let feedPointInfo = null;

        function scanFeedPoint() {
            const button = event.target;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>扫描中...';
            button.disabled = true;

            setTimeout(() => {
                // 模拟扫描成功
                feedPointInfo = {
                    id: 'FP001',
                    name: 'A1池-投料点01',
                    location: '东南角投料台',
                    lastFeed: '2小时前'
                };

                // 显示投料点信息
                document.getElementById('feed-point-info').classList.remove('hidden');
                document.getElementById('feed-point-name').textContent = feedPointInfo.name;
                document.getElementById('feed-point-location').textContent = feedPointInfo.location;

                // 显示投料选择界面
                document.getElementById('feed-selection').classList.remove('hidden');

                button.innerHTML = '<i class="fas fa-check mr-2"></i>扫描成功';
                button.disabled = false;
            }, 2000);
        }

        function selectFeedType(type) {
            selectedFeedType = type;

            // 更新UI显示
            document.querySelectorAll('[onclick*="selectFeedType"]').forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50');
                btn.classList.add('border-gray-200');
            });
            event.target.classList.remove('border-gray-200');
            event.target.classList.add('border-blue-500', 'bg-blue-50');

            // 显示/隐藏自定义重量输入
            const customWeight = document.getElementById('custom-weight');
            if (type === 'custom') {
                customWeight.classList.remove('hidden');
            } else {
                customWeight.classList.add('hidden');
            }
        }

        function adjustQuantity(delta) {
            const quantityInput = document.getElementById('feed-quantity');
            let currentValue = parseInt(quantityInput.value) || 1;
            currentValue = Math.max(1, currentValue + delta);
            quantityInput.value = currentValue;
        }

        function takeOperatorPhoto() {
            const preview = document.getElementById('operator-photo');
            const cameraPreview = document.getElementById('operator-camera-preview');

            // 使用操作员头像图片
            preview.src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face';
            preview.classList.remove('hidden');
            cameraPreview.classList.add('hidden');
        }

        function confirmFeeding() {
            if (!selectedFeedType) {
                alert('请选择投料包类型');
                return;
            }

            const operatorPhoto = document.getElementById('operator-photo');
            if (operatorPhoto.classList.contains('hidden')) {
                alert('请先拍照确认身份');
                return;
            }

            const quantity = document.getElementById('feed-quantity').value;
            const weight = selectedFeedType === 'custom' ?
                document.getElementById('feed-weight').value :
                selectedFeedType.replace('kg', '');

            // 保存投料记录
            const record = {
                id: Date.now(),
                feedPoint: feedPointInfo.name,
                feedType: selectedFeedType,
                quantity: parseInt(quantity),
                weight: parseFloat(weight) || 0,
                operator: AppState.user?.name || '张师傅',
                timestamp: new Date().toISOString(),
                date: new Date().toLocaleDateString('zh-CN'),
                time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
            };

            AppState.data.feedRecords.push(record);

            alert('投料记录已保存！');
            Router.navigate('home-page');
        }

        function showFeedHistory() {
            alert('投料历史功能（演示版本）');
        }

        // 个人中心相关功能
        function toggleNotifications() {
            alert('通知设置功能（演示版本）');
        }

        function changeTheme() {
            alert('主题切换功能（演示版本）\n当前：高对比度模式');
        }

        function changeLanguage() {
            alert('语言设置功能（演示版本）\n当前：简体中文');
        }

        function showHelp() {
            alert('帮助文档（演示版本）\n\n基本操作：\n1. 虾苗计数：验证手机号 → 选择品种 → 拍照计数\n2. 体长测量：选择苗池 → 选择模式 → 拍照测量\n3. 投料管理：扫描投料点 → 选择投料包 → 拍照确认\n4. 数据统计：查看历史记录和分析图表');
        }

        function showAbout() {
            alert('关于应用（演示版本）\n\n虾苗养殖管理系统 v1.0.0\n© 2024 智慧养殖科技\n\n专为水产养殖场设计的移动管理工具\n支持虾苗计数、体长测量、投料管理等功能');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                AppState.user = null;
                AppState.currentPage = 'splash-page';
                Router.navigate('login-page');
            }
        }

        // 图表相关功能
        function showChartModal() {
            document.getElementById('chart-modal').classList.remove('hidden');
            showTrendChart();
        }

        function closeChartModal() {
            document.getElementById('chart-modal').classList.add('hidden');
        }

        function showTrendChart() {
            const ctx = document.getElementById('stats-chart').getContext('2d');

            // 清除之前的图表
            Chart.getChart(ctx)?.destroy();

            // 生成趋势数据
            const labels = [];
            const data = [];
            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
                data.push(Math.floor(Math.random() * 2000) + 3000);
            }

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '日投放量',
                        data: data,
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function showPieChart() {
            const ctx = document.getElementById('stats-chart').getContext('2d');

            // 清除之前的图表
            Chart.getChart(ctx)?.destroy();

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['白对虾', '斑节虾', '其他'],
                    datasets: [{
                        data: [65, 30, 5],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
