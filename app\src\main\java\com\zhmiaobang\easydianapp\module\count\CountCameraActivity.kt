package com.zhmiaobang.easydianapp.module.count

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Matrix
import androidx.exifinterface.media.ExifInterface
import android.hardware.camera2.CameraMetadata
import android.media.MediaActionSound
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.VibrationEffect
import android.os.Vibrator
import android.provider.Settings
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.view.WindowManager
import android.view.animation.AlphaAnimation
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityCountCameraBinding
import com.zhmiaobang.easydianapp.json.count.BatchCountJson
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.opencv.android.Utils
import org.opencv.core.Core
import org.opencv.core.CvType
import org.opencv.core.Mat
import org.opencv.core.MatOfDouble
import org.opencv.imgproc.Imgproc
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.math.max
import kotlin.math.min

/**
 * 拍照计数Activity - 集成高级相机控制功能
 *
 * 功能特性：
 * - 权限管理和相机初始化
 * - 缩放控制 (1x-10x)
 * - 自动对焦和点击对焦
 * - 白平衡控制 (自动/日光/阴天/荧光)
 * - 曝光补偿调节 (-2.0 到 +2.0 EV)
 * - 拍照功能和文件保存
 * - 用户反馈 (音效/震动/视觉)
 */
class CountCameraActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "CountCameraActivity"
        private const val FILENAME_PREFIX = "COUNT_IMG_"
        private const val FILENAME_DATE_FORMAT = "yyyyMMdd_HHmmss"
        private const val REQUEST_CODE_PERMISSIONS = 10
        private val REQUIRED_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)

        // 相机控制常量
        private const val ZOOM_RATIO_MIN = 1.0f
        private const val ZOOM_RATIO_MAX = 10.0f

        // Toast消息常量
        private const val MSG_PERMISSION_REQUIRED = "需要相机权限才能使用拍照功能"
        private const val MSG_PERMISSION_DENIED = "相机权限被拒绝，请在设置中手动开启"
        private const val MSG_CAMERA_INIT_FAILED = "相机初始化失败"
        private const val MSG_PHOTO_SAVED = "照片已保存"
        private const val MSG_PHOTO_SAVE_FAILED = "照片保存失败"
        private const val MSG_CAMERA_NOT_READY = "相机未准备就绪"
        private const val MSG_CONTINUOUS_CAPTURE = "连续拍照中，请保持稳定..."
        private const val MSG_ANALYZING_PHOTOS = "正在分析照片清晰度..."
    }

    // ViewBinding
    private lateinit var binding: ActivityCountCameraBinding

    // 数据传递
    private var batchCountJson: BatchCountJson? = null
    private var imgSrc: String = ""

    // 相机相关
    private var imageCapture: ImageCapture? = null
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()

    // 相机控制
    private var currentZoomRatio = 1.0f

    // 手势检测
    private lateinit var scaleGestureDetector: ScaleGestureDetector
    private lateinit var gestureDetector: GestureDetector

    // 权限管理
    private lateinit var permissionLauncher: ActivityResultLauncher<String>

    // 音效和震动
    private lateinit var mediaActionSound: MediaActionSound
    private lateinit var vibrator: Vibrator

    // UI状态
    private var isCameraReady = false
    private var isCapturing = false

    // 连续拍照相关
    private var captureCount = 0
    private val maxCaptureCount = 3
    private val captureInterval = 2000L // 2秒间隔
    private val capturedPhotos = mutableListOf<File>()

    // OpenCV相关
    private var isOpenCVLoaded = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 初始化ViewBinding
        binding = ActivityCountCameraBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 获取传递的数据
        batchCountJson = intent.getParcelableExtra("batch_count")

        // 初始化组件
        initializeComponents()
        setupSystemBars()
        setupToolbar()
        setupPermissionLauncher()
        setupGestureDetectors()
        setupCameraControls()

        // 检查权限并初始化相机
        if (allPermissionsGranted()) {
            initializeCamera()
        } else {
            requestPermissions()
        }
    }

    override fun onResume() {
        super.onResume()
        try {
            // 尝试加载OpenCV库
            System.loadLibrary("opencv_java4")
            isOpenCVLoaded = true
            Log.d(TAG, "OpenCV库加载成功")
        } catch (e: UnsatisfiedLinkError) {
            Log.e(TAG, "无法加载OpenCV库: ${e.message}")
            isOpenCVLoaded = false
        }
    }

    /**
     * 初始化组件
     */
    private fun initializeComponents() {
        // 初始化音效
        mediaActionSound = MediaActionSound()
        mediaActionSound.load(MediaActionSound.SHUTTER_CLICK)

        // 初始化震动器
        vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
    }

    /**
     * 设置系统栏
     */
    private fun setupSystemBars() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.countMain) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())

            // 为 Toolbar 设置顶部内边距
            val toolbar = binding.countToolbar
            val layoutParams = toolbar.layoutParams

            // 获取 actionBarSize 的正确方式
            val typedValue = android.util.TypedValue()
            val actionBarSize = if (theme.resolveAttribute(android.R.attr.actionBarSize, typedValue, true)) {
                resources.getDimensionPixelSize(typedValue.resourceId)
            } else {
                // 如果无法获取，使用默认值 56dp
                (56 * resources.displayMetrics.density).toInt()
            }

            layoutParams.height = actionBarSize + systemBars.top
            toolbar.layoutParams = layoutParams
            toolbar.setPadding(
                toolbar.paddingLeft,
                systemBars.top,
                toolbar.paddingRight,
                toolbar.paddingBottom
            )

            // 主容器不设置内边距
            v.setPadding(0, 0, 0, systemBars.bottom)
            insets
        }
    }

    /**
     * 设置工具栏
     */
    private fun setupToolbar() {
        setSupportActionBar(binding.countToolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(false)

        // 设置返回按钮点击事件
        binding.countToolbar.setNavigationOnClickListener {
            onBackPressed()
        }

        // 设置设置按钮点击事件
        binding.countSettingsButton.setOnClickListener {
            showCameraSettingsDialog()
        }
    }

    /**
     * 设置权限请求启动器
     */
    private fun setupPermissionLauncher() {
        permissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (isGranted) {
                initializeCamera()
            } else {
                showPermissionDeniedDialog()
            }
        }
    }

    /**
     * 设置手势检测器
     */
    private fun setupGestureDetectors() {
        // 缩放手势检测器
        scaleGestureDetector = ScaleGestureDetector(this, object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                val scale = detector.scaleFactor
                val newZoomRatio = (currentZoomRatio * scale).coerceIn(ZOOM_RATIO_MIN, ZOOM_RATIO_MAX)
                setZoomRatio(newZoomRatio)
                return true
            }
        })

        // 点击手势检测器 (用于对焦)
        gestureDetector = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onSingleTapUp(e: MotionEvent): Boolean {
                performFocusAt(e.x, e.y)
                return true
            }
        })

        // 为预览视图设置触摸监听器
        binding.countCameraPreview.setOnTouchListener { _, event ->
            scaleGestureDetector.onTouchEvent(event)
            if (!scaleGestureDetector.isInProgress) {
                gestureDetector.onTouchEvent(event)
            }
            true
        }
    }

    /**
     * 设置相机控制面板
     */
    private fun setupCameraControls() {
        // 缩放滑块
        binding.countZoomSlider.addOnChangeListener { _, value, _ ->
            setZoomRatio(value)
        }

        // 拍照按钮
        binding.countCaptureButton.setOnClickListener {
            takePhoto()
        }

        // 确认按钮
        binding.countConfirmButton.setOnClickListener {
            confirmSelectedPhoto()
        }
    }

    /**
     * 检查是否已获得所有必需权限
     */
    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 请求权限
     */
    private fun requestPermissions() {
        permissionLauncher.launch(Manifest.permission.CAMERA)
    }

    /**
     * 显示权限被拒绝对话框
     */
    private fun showPermissionDeniedDialog() {
        AlertDialog.Builder(this)
            .setTitle("权限需求")
            .setMessage(MSG_PERMISSION_DENIED)
            .setPositiveButton("去设置") { _, _ ->
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", packageName, null)
                }
                startActivity(intent)
            }
            .setNegativeButton("取消") { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 初始化相机
     */
    private fun initializeCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                bindCameraUseCases()
                isCameraReady = true
                updateCameraStatus("准备就绪")
            } catch (exc: Exception) {
                Log.e(TAG, "相机初始化失败", exc)
                showToast(MSG_CAMERA_INIT_FAILED)
            }
        }, ContextCompat.getMainExecutor(this))
    }

    /**
     * 绑定相机用例
     */
    private fun bindCameraUseCases() {
        val cameraProvider = cameraProvider ?: return

        // 预览用例
        val preview = Preview.Builder()
            .build()
            .also {
                it.surfaceProvider = binding.countCameraPreview.surfaceProvider
            }

        // 图像捕获用例
        imageCapture = ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
            .setTargetRotation(windowManager.defaultDisplay.rotation)
            .build()

        // 选择相机 (优先后置)
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

        try {
            // 解绑所有用例
            cameraProvider.unbindAll()

            // 绑定用例到相机
            camera = cameraProvider.bindToLifecycle(
                this, cameraSelector, preview, imageCapture
            )

            // 设置初始相机控制
            setupInitialCameraControls()

        } catch (exc: Exception) {
            Log.e(TAG, "用例绑定失败", exc)
            showToast(MSG_CAMERA_INIT_FAILED)
        }
    }

    /**
     * 设置初始相机控制参数
     */
    private fun setupInitialCameraControls() {
        camera?.let { camera ->
            // 设置缩放范围
            val zoomState = camera.cameraInfo.zoomState.value
            zoomState?.let {
                val maxZoom = min(it.maxZoomRatio, ZOOM_RATIO_MAX)
                binding.countZoomSlider.valueTo = maxZoom
                binding.countZoomSlider.value = it.zoomRatio
                currentZoomRatio = it.zoomRatio
                updateZoomRatioDisplay(it.zoomRatio)
            }
        }
    }

    /**
     * 设置缩放比例
     */
    private fun setZoomRatio(ratio: Float) {
        camera?.let { camera ->
            val clampedRatio = ratio.coerceIn(ZOOM_RATIO_MIN, ZOOM_RATIO_MAX)
            camera.cameraControl.setZoomRatio(clampedRatio)
            currentZoomRatio = clampedRatio
            updateZoomRatioDisplay(clampedRatio)
        }
    }

    /**
     * 更新缩放比例显示
     */
    private fun updateZoomRatioDisplay(ratio: Float) {
        binding.countZoomRatio.text = String.format("%.1fx", ratio)
    }





    /**
     * 在指定位置执行对焦
     */
    private fun performFocusAt(x: Float, y: Float) {
        camera?.let { camera ->
            // 计算相对于预览视图的坐标
            val meteringPointFactory = binding.countCameraPreview.meteringPointFactory
            val focusPoint = meteringPointFactory.createPoint(x, y)

            // 创建对焦动作
            val focusAction = FocusMeteringAction.Builder(focusPoint, FocusMeteringAction.FLAG_AF)
                .setAutoCancelDuration(3, java.util.concurrent.TimeUnit.SECONDS)
                .build()

            // 执行对焦
            camera.cameraControl.startFocusAndMetering(focusAction)

            // 显示对焦指示器
            showFocusRing(x, y)
            updateCameraStatus("正在对焦")
        }
    }

    /**
     * 显示对焦指示器
     */
    private fun showFocusRing(x: Float, y: Float) {
        // 设置对焦环位置
        binding.countFocusRing.x = x - binding.countFocusRing.width / 2
        binding.countFocusRing.y = y - binding.countFocusRing.height / 2

        // 显示对焦环并设置动画
        binding.countFocusRing.visibility = View.VISIBLE
        binding.countFocusRing.alpha = 1.0f

        // 淡出动画
        binding.countFocusRing.animate()
            .alpha(0.0f)
            .setDuration(1000)
            .withEndAction {
                binding.countFocusRing.visibility = View.GONE
            }
            .start()
    }

    /**
     * 拍照 - 连续拍照3次，选择最清晰的照片
     */
    private fun takePhoto() {
        if (!isCameraReady || isCapturing) {
            showToast(MSG_CAMERA_NOT_READY)
            return
        }

        if (!isOpenCVLoaded) {
            showToast("OpenCV未加载完成，请稍后再试")
            return
        }

        val imageCapture = imageCapture ?: return
        isCapturing = true
        captureCount = 0
        capturedPhotos.clear()

        // 禁用拍照按钮
        binding.countCaptureButton.isEnabled = false

        // 开始连续拍照
        startContinuousCapture()
    }

    /**
     * 开始连续拍照
     */
    private fun startContinuousCapture() {
        lifecycleScope.launch {
            repeat(maxCaptureCount) { index ->
                captureCount = index + 1
                updateCameraStatus("拍照中 $captureCount/$maxCaptureCount")

                // 拍照前的视觉反馈
                flashScreen()

                // 单次拍照
                captureSinglePhoto { success, photoFile ->
                    if (success && photoFile != null) {
                        capturedPhotos.add(photoFile)

                        // 播放拍照音效
                        mediaActionSound.play(MediaActionSound.SHUTTER_CLICK)

                        // 震动反馈
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                            vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
                        } else {
                            @Suppress("DEPRECATION")
                            vibrator.vibrate(50)
                        }
                    } else {
                        Log.e(TAG, "第${captureCount}次拍照失败")
                    }
                }

                // 如果不是最后一次拍照，等待间隔时间
                if (index < maxCaptureCount - 1) {
                    delay(captureInterval)
                }
            }

            // 所有拍照完成，选择最佳照片
            selectBestPhoto()
        }
    }

    /**
     * 单次拍照
     */
    private fun captureSinglePhoto(callback: (Boolean, File?) -> Unit) {
        val imageCapture = imageCapture ?: run {
            callback(false, null)
            return
        }

        // 创建输出文件
        val photoFile = createPhotoFile()

        // 创建输出选项
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        // 拍照
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(this),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(outputFileResults: ImageCapture.OutputFileResults) {
                    callback(true, photoFile)
                }

                override fun onError(exception: ImageCaptureException) {
                    Log.e(TAG, "单次拍照失败: ${exception.message}", exception)
                    callback(false, null)
                }
            }
        )
    }

    /**
     * 选择最佳照片 - 基于OpenCV清晰度分析
     */
    private fun selectBestPhoto() {
        if (capturedPhotos.isEmpty()) {
            showToast(MSG_PHOTO_SAVE_FAILED)
            resetCaptureState()
            return
        }

        updateCameraStatus("分析照片清晰度...")

        lifecycleScope.launch {
            try {
                var bestPhoto: File? = null
                var bestSharpness = 0.0

                for (photoFile in capturedPhotos) {
                    val sharpness = calculateImageSharpness(photoFile)
                    Log.d(TAG, "照片 ${photoFile.name} 清晰度: $sharpness")

                    if (sharpness > bestSharpness) {
                        bestSharpness = sharpness
                        bestPhoto = photoFile
                    }
                }

                if (bestPhoto != null) {
                    // 保存最佳照片路径
                    imgSrc = bestPhoto.absolutePath
                    batchCountJson?.imgSrc = imgSrc

                    // 删除其他照片
                    capturedPhotos.forEach { photo ->
                        if (photo != bestPhoto) {
                            photo.delete()
                        }
                    }

                    showToast("已选择最清晰照片")
                    updateCameraStatus("照片已保存 (清晰度: ${String.format("%.2f", bestSharpness)})")

                    // 显示照片预览而不是直接返回
                    showPhotoPreview(bestPhoto, bestSharpness)
                } else {
                    showToast(MSG_PHOTO_SAVE_FAILED)
                    resetCaptureState()
                }

            } catch (e: Exception) {
                Log.e(TAG, "分析照片清晰度失败: ${e.message}", e)
                // 如果分析失败，使用第一张照片
                if (capturedPhotos.isNotEmpty()) {
                    val firstPhoto = capturedPhotos[0]
                    imgSrc = firstPhoto.absolutePath
                    batchCountJson?.imgSrc = imgSrc

                    // 删除其他照片
                    capturedPhotos.drop(1).forEach { it.delete() }

                    showToast("照片已保存 (使用第一张)")
                    // 显示照片预览，使用文件大小作为清晰度指标
                    showPhotoPreview(firstPhoto, firstPhoto.length().toDouble())
                } else {
                    showToast(MSG_PHOTO_SAVE_FAILED)
                    resetCaptureState()
                }
            }
        }
    }

    /**
     * 计算图片清晰度 - 使用简化的边缘检测算法
     */
    private fun calculateImageSharpness(imageFile: File): Double {
        return try {
            if (!isOpenCVLoaded) {
                // 如果OpenCV未加载，使用文件大小作为简单的质量指标
                return imageFile.length().toDouble()
            }

            // 读取图片
            val bitmap = BitmapFactory.decodeFile(imageFile.absolutePath)
            if (bitmap == null) {
                Log.e(TAG, "无法读取图片: ${imageFile.absolutePath}")
                return 0.0
            }

            // 转换为OpenCV Mat
            val mat = Mat(bitmap.height, bitmap.width, CvType.CV_8UC4)
            Utils.bitmapToMat(bitmap, mat)

            // 转换为灰度图
            val grayMat = Mat()
            Imgproc.cvtColor(mat, grayMat, Imgproc.COLOR_RGBA2GRAY)

            // 应用拉普拉斯算子
            val laplacianMat = Mat()
            Imgproc.Laplacian(grayMat, laplacianMat, CvType.CV_64F)

            // 计算标准差作为清晰度指标
            val mean = MatOfDouble()
            val stddev = MatOfDouble()
            Core.meanStdDev(laplacianMat, mean, stddev)

            // 获取标准差值
            val stddevArray = stddev.toArray()
            val sharpness = if (stddevArray.isNotEmpty()) {
                stddevArray[0] * stddevArray[0]
            } else {
                0.0
            }

            // 释放资源
            mat.release()
            grayMat.release()
            laplacianMat.release()
            mean.release()
            stddev.release()
            bitmap.recycle()

            sharpness
        } catch (e: Exception) {
            Log.e(TAG, "计算图片清晰度失败: ${e.message}", e)
            // 如果OpenCV计算失败，使用文件大小作为备选方案
            imageFile.length().toDouble()
        }
    }

    /**
     * 显示照片预览
     */
    private fun showPhotoPreview(photoFile: File, sharpness: Double) {
        try {
            // 加载照片并处理旋转
            val correctedBitmap = loadAndCorrectBitmap(photoFile)
            if (correctedBitmap != null) {
                binding.countPhotoPreview.setImageBitmap(correctedBitmap)

                // 显示清晰度分数
                val sharpnessText = if (sharpness > 1000000) {
                    // 如果是文件大小，转换为更友好的显示
                    "大小: ${String.format("%.1f", sharpness / 1024)}KB"
                } else {
                    "清晰度: ${String.format("%.2f", sharpness)}"
                }
                binding.countSharpnessScore.text = sharpnessText

                // 显示预览容器
                binding.countPhotoPreviewContainer.visibility = View.VISIBLE

                // 隐藏缩放控制，给预览更多空间
                binding.countZoomContainer.visibility = View.GONE

                // 更新状态
                updateCameraStatus("已选择最清晰照片，请确认")

                Log.d(TAG, "照片预览显示成功: ${photoFile.name}, 清晰度: $sharpness")
            } else {
                Log.e(TAG, "无法加载照片进行预览: ${photoFile.absolutePath}")
                showToast("照片预览失败")
                resetCaptureState()
            }
        } catch (e: Exception) {
            Log.e(TAG, "显示照片预览失败: ${e.message}", e)
            showToast("照片预览失败")
            resetCaptureState()
        }
    }

    /**
     * 加载图片并修正旋转方向
     */
    private fun loadAndCorrectBitmap(photoFile: File): Bitmap? {
        return try {
            // 首先获取图片的EXIF信息
            val exif = ExifInterface(photoFile.absolutePath)
            val orientation = exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )

            // 加载原始图片
            val originalBitmap = BitmapFactory.decodeFile(photoFile.absolutePath)
            if (originalBitmap == null) {
                Log.e(TAG, "无法解码图片文件: ${photoFile.absolutePath}")
                return null
            }

            // 根据EXIF信息计算需要旋转的角度
            val rotationAngle = when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> 90f
                ExifInterface.ORIENTATION_ROTATE_180 -> 180f
                ExifInterface.ORIENTATION_ROTATE_270 -> 270f
                ExifInterface.ORIENTATION_FLIP_HORIZONTAL -> 0f // 水平翻转，暂不处理
                ExifInterface.ORIENTATION_FLIP_VERTICAL -> 0f // 垂直翻转，暂不处理
                ExifInterface.ORIENTATION_TRANSPOSE -> 0f // 复杂变换，暂不处理
                ExifInterface.ORIENTATION_TRANSVERSE -> 0f // 复杂变换，暂不处理
                else -> 0f // ORIENTATION_NORMAL 或未知
            }

            // 如果不需要旋转，直接返回原图
            if (rotationAngle == 0f) {
                Log.d(TAG, "图片无需旋转，EXIF方向: $orientation")
                return originalBitmap
            }

            // 创建旋转矩阵
            val matrix = Matrix()
            matrix.postRotate(rotationAngle)

            // 应用旋转
            val rotatedBitmap = Bitmap.createBitmap(
                originalBitmap,
                0,
                0,
                originalBitmap.width,
                originalBitmap.height,
                matrix,
                true
            )

            // 释放原始图片内存
            if (rotatedBitmap != originalBitmap) {
                originalBitmap.recycle()
            }

            Log.d(TAG, "图片旋转完成: ${rotationAngle}度, EXIF方向: $orientation")
            rotatedBitmap

        } catch (e: Exception) {
            Log.e(TAG, "处理图片旋转失败: ${e.message}", e)
            // 如果处理失败，尝试直接加载原图
            BitmapFactory.decodeFile(photoFile.absolutePath)
        }
    }

    /**
     * 确认选择的照片
     */
    private fun confirmSelectedPhoto() {
        try {
            // 确保imgSrc已设置
            if (imgSrc.isEmpty()) {
                showToast("照片路径错误，请重新拍照")
                resetCaptureState()
                return
            }

            // 包装batchJson的imgSrc
            batchCountJson?.let { json ->
                json.imgSrc = imgSrc
                Log.d(TAG, "BatchCountJson已更新: imgSrc = $imgSrc")
                Log.d(TAG, "BatchCountJson完整信息: $json")
            }

            // 显示确认Toast
            val fileName = File(imgSrc).name
            showToast("照片已确认保存: $fileName")

            // 隐藏预览容器
            binding.countPhotoPreviewContainer.visibility = View.GONE

            // 显示缩放控制
            binding.countZoomContainer.visibility = View.VISIBLE

            // 更新状态
            updateCameraStatus("照片已确认保存")

            Log.d(TAG, "照片确认完成，准备返回结果")

            // 返回结果
            setResultAndFinish()

        } catch (e: Exception) {
            Log.e(TAG, "确认照片失败: ${e.message}", e)
            showToast("确认照片失败，请重试")
            resetCaptureState()
        }
    }

    /**
     * 重置拍照状态
     */
    private fun resetCaptureState() {
        isCapturing = false
        captureCount = 0
        capturedPhotos.clear()
        binding.countCaptureButton.isEnabled = true
        binding.countPhotoPreviewContainer.visibility = View.GONE
        binding.countZoomContainer.visibility = View.VISIBLE
        updateCameraStatus("准备就绪")
    }

    /**
     * 创建照片文件
     */
    private fun createPhotoFile(): File {
        val timeStamp = SimpleDateFormat(FILENAME_DATE_FORMAT, Locale.getDefault()).format(Date())
        val fileName = FILENAME_PREFIX + timeStamp + ".jpg"
        val storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File(storageDir, fileName)
    }

    /**
     * 闪屏效果 (拍照视觉反馈)
     */
    private fun flashScreen() {
        val flashOverlay = View(this).apply {
            setBackgroundColor(Color.WHITE)
            alpha = 0.0f
        }

        binding.countMain.addView(flashOverlay)

        val flashAnimation = AlphaAnimation(0.0f, 0.8f).apply {
            duration = 100
            repeatCount = 1
            repeatMode = AlphaAnimation.REVERSE
        }

        flashOverlay.startAnimation(flashAnimation)

        // 动画结束后移除覆盖层
        flashOverlay.postDelayed({
            binding.countMain.removeView(flashOverlay)
        }, 200)
    }

    /**
     * 更新相机状态显示
     */
    private fun updateCameraStatus(status: String) {
        binding.countCameraStatus.text = status
        binding.countCameraStatus.visibility = View.VISIBLE

        // 3秒后隐藏状态
        binding.countCameraStatus.postDelayed({
            binding.countCameraStatus.visibility = View.GONE
        }, 3000)
    }

    /**
     * 显示相机设置对话框
     */
    private fun showCameraSettingsDialog() {
        AlertDialog.Builder(this)
            .setTitle("相机设置")
            .setMessage("当前相机参数：\n" +
                    "缩放: ${String.format("%.1fx", currentZoomRatio)}")
            .setPositiveButton("确定", null)
            .show()
    }



    /**
     * 设置结果并结束Activity
     */
    private fun setResultAndFinish() {
        try {
           batchCountJson?.imgSrc =imgSrc
            val intent = Intent(this, CountResultActivity::class.java)
            intent.putExtra("batch_count",batchCountJson)
            startActivity(intent)
            finish()
        } catch (e: Exception) {
            Log.e(TAG, "设置返回结果失败: ${e.message}", e)
            showToast("返回数据失败")
            finish()
        }
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * 生命周期管理 - 销毁时释放资源
     */
    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
        mediaActionSound.release()
    }

    /**
     * 处理返回按键
     */
    override fun onBackPressed() {
        if (isCapturing) {
            showToast("正在连续拍照，请稍候...")
            return
        }

        // 如果正在显示照片预览，先隐藏预览
        if (binding.countPhotoPreviewContainer.visibility == View.VISIBLE) {
            binding.countPhotoPreviewContainer.visibility = View.GONE
            binding.countZoomContainer.visibility = View.VISIBLE
            resetCaptureState()
            return
        }

        super.onBackPressed()
    }
}