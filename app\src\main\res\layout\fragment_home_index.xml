<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.home.HomeIndexFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <RelativeLayout
            android:id="@+id/hIndex_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp">

            <!-- User Info -->
            <LinearLayout
                android:id="@+id/hIndex_user_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/hIndex_user_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/hIndex_user_name"
                    android:textColor="@color/hIndex_text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/hIndex_user_role"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/hIndex_user_role"
                    android:textColor="@color/hIndex_text_secondary"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- Weather Info -->
            <LinearLayout
                android:id="@+id/hIndex_weather_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/hIndex_weather_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_weather" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/hIndex_weather_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/hIndex_weather_today"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/hIndex_temperature"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/hIndex_temperature"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <!-- Settings Button -->
            <ImageView
                android:id="@+id/hIndex_settings_btn"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:src="@drawable/ic_settings" />

        </RelativeLayout>

        <!-- Today Overview Section -->
        <LinearLayout
            android:id="@+id/hIndex_overview_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/hIndex_overview_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/hIndex_today_overview"
                android:textColor="@color/hIndex_text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- Data Cards Grid -->
            <GridLayout
                android:id="@+id/hIndex_data_grid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="2"
                android:rowCount="2">

                <!-- Shrimp Count Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/hIndex_shrimp_count_card"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="6dp"
                    app:cardBackgroundColor="@color/hIndex_card_background"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginBottom="8dp"
                            android:src="@drawable/ic_fish" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_shrimp_count_value"
                            android:textColor="@color/hIndex_blue"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:layout_marginEnd="4dp"
                                android:src="@drawable/ic_trend_up" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="+12%"
                                android:textColor="@color/hIndex_trend_up"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_shrimp_count"
                            android:textColor="@color/hIndex_text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Average Length Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/hIndex_avg_length_card"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="6dp"
                    app:cardBackgroundColor="@color/hIndex_card_background"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginBottom="8dp"
                            android:src="@drawable/ic_length" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_avg_length_value"
                            android:textColor="@color/hIndex_green"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:layout_marginEnd="4dp"
                                android:src="@drawable/ic_trend_up" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="+0.3cm"
                                android:textColor="@color/hIndex_trend_up"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_avg_length"
                            android:textColor="@color/hIndex_text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Feeding Times Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/hIndex_feeding_times_card"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="6dp"
                    app:cardBackgroundColor="@color/hIndex_card_background"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginBottom="8dp"
                            android:src="@drawable/ic_feeding" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_feeding_times_value"
                            android:textColor="@color/hIndex_orange"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_feeding_times_unit"
                            android:textColor="@color/hIndex_text_hint"
                            android:textSize="10sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_feeding_times"
                            android:textColor="@color/hIndex_text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Survival Rate Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/hIndex_survival_rate_card"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="6dp"
                    app:cardBackgroundColor="@color/hIndex_card_background"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginBottom="8dp"
                            android:src="@drawable/ic_survival_rate" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_survival_rate_value"
                            android:textColor="@color/hIndex_purple"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:layout_marginEnd="4dp"
                                android:src="@drawable/ic_trend_up" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="+1.2%"
                                android:textColor="@color/hIndex_trend_up"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_survival_rate"
                            android:textColor="@color/hIndex_text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </GridLayout>

        </LinearLayout>

        <!-- Function Menu Section -->
        <LinearLayout
            android:id="@+id/hIndex_function_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/hIndex_function_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/hIndex_function_menu"
                android:textColor="@color/hIndex_text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- Function Menu Grid -->
            <GridLayout
                android:id="@+id/hIndex_function_grid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="2"
                android:rowCount="2">

                <!-- Shrimp Count Function -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/hIndex_func_shrimp_count_card"
                    android:layout_width="0dp"
                    android:layout_height="140dp"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="6dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground"
                    app:cardBackgroundColor="@color/hIndex_card_background"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <View
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:layout_marginBottom="12dp"
                            android:background="@drawable/ic_shrimp_count"
                            android:backgroundTint="@color/hIndex_blue" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:text="@string/hIndex_func_shrimp_count"
                            android:textColor="@color/hIndex_text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_func_shrimp_count_desc"
                            android:textColor="@color/hIndex_text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Length Measure Function -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/hIndex_func_length_measure_card"
                    android:layout_width="0dp"
                    android:layout_height="140dp"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="6dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground"
                    app:cardBackgroundColor="@color/hIndex_card_background"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <View
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:layout_marginBottom="12dp"
                            android:background="@drawable/ic_length"
                            android:backgroundTint="@color/hIndex_green" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:text="@string/hIndex_func_length_measure"
                            android:textColor="@color/hIndex_text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_func_length_measure_desc"
                            android:textColor="@color/hIndex_text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Water Test Function -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/hIndex_func_water_test_card"
                    android:layout_width="0dp"
                    android:layout_height="140dp"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="6dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground"
                    app:cardBackgroundColor="@color/hIndex_card_background"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <View
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:layout_marginBottom="12dp"
                            android:background="@drawable/ic_water_test"
                            android:backgroundTint="@color/hIndex_purple" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:text="@string/hIndex_func_water_test"
                            android:textColor="@color/hIndex_text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_func_water_test_desc"
                            android:textColor="@color/hIndex_text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Feeding Management Function -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/hIndex_func_feeding_manage_card"
                    android:layout_width="0dp"
                    android:layout_height="140dp"
                    android:layout_rowWeight="1"
                    android:layout_columnWeight="1"
                    android:layout_margin="6dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground"
                    app:cardBackgroundColor="@color/hIndex_card_background"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <View
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:layout_marginBottom="12dp"
                            android:background="@drawable/ic_feeding"
                            android:backgroundTint="@color/hIndex_orange" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:text="@string/hIndex_func_feeding_manage"
                            android:textColor="@color/hIndex_text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hIndex_func_feeding_manage_desc"
                            android:textColor="@color/hIndex_text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </GridLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>