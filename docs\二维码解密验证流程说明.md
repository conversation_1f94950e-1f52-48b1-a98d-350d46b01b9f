# 二维码解密验证流程说明

## 🔐 加密格式分析

### 服务端加密流程
```python
# 1. 构建原始内容（包含过期时间戳）
codeStr = self.code + "|" + str(int(self.expire_time.timestamp()))
# 例如: "EMP001|1642780800" (EMP001是员工代码，1642780800是过期时间戳)

# 2. AES加密
ecode = encryptCode(codeStr, settings.ANDROID_ENCRYPT_KEY)

# 3. 构建最终二维码
self.qrcodeString = "mb|login|" + ecode + "|end"
# 例如: "mb|login|base64EncryptedContent|end"
```

### 二维码结构
```
完整格式: mb|login|{AES加密的Base64内容}|end
         ↑      ↑                    ↑
       前缀   加密内容              后缀
```

## 🔓 Android端解密流程

### 1. 格式验证
```kotlin
// 验证前缀和后缀
if (!qrContent.startsWith("mb|login|") || !qrContent.endsWith("|end")) {
    return false // 格式错误
}
```

### 2. 提取加密内容
```kotlin
val encryptedContent = qrContent.substring(
    "mb|login|".length, 
    qrContent.length - "|end".length
)
```

### 3. AES解密过程

#### 3.1 密钥处理
```kotlin
val keyBytes = Base64.decode(MyApp.ANDROID_ENCRYPT_KEY, Base64.DEFAULT)
val secretKey = SecretKeySpec(keyBytes, "AES")
```

#### 3.2 数据解码
```kotlin
val encryptedBytes = Base64.decode(encryptedData, Base64.DEFAULT)
```

#### 3.3 IV提取和解密
```kotlin
// 方案1: IV在密文前16字节（主要方案）
val iv = encryptedBytes.sliceArray(0..15)
val cipherText = encryptedBytes.sliceArray(16 until encryptedBytes.size)

// 方案2: 零IV（备用方案）
val zeroIV = ByteArray(16)
```

#### 3.4 解密执行
```kotlin
val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
val ivSpec = IvParameterSpec(iv)
cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec)
val decryptedBytes = cipher.doFinal(cipherText)
```

### 4. 解密结果解析
```kotlin
val decryptedContent = String(decryptedBytes, Charsets.UTF_8)
// 期望格式: "员工代码|过期时间戳"
// 例如: "EMP001|1642780800"

val parts = decryptedContent.split("|")
val employeeCode = parts[0]  // 员工代码
val timestamp = parts[1].toLong()  // 过期时间戳
```

### 5. 时间戳验证
```kotlin
fun isTimestampValid(timestamp: Long): Boolean {
    val currentTime = System.currentTimeMillis() / 1000

    // 验证逻辑：当前时间应该小于过期时间
    // timestamp是过期时间，不是生成时间
    return currentTime < timestamp
}
```

## 📋 完整验证流程

### 流程图
```
扫码结果
    ↓
格式验证 (mb|login|....|end)
    ↓
提取加密内容
    ↓
AES解密 (CBC模式 + Base64)
    ↓
解析结果 (员工代码|时间戳)
    ↓
时间戳验证 (24小时有效期)
    ↓
保存员工代码 → 登录成功
```

### 错误处理机制

| 错误类型 | 检测点 | 用户提示 | 日志记录 |
|---------|--------|----------|----------|
| 格式错误 | 前缀后缀验证 | "二维码格式错误" | 前缀或后缀不匹配 |
| 内容为空 | 加密内容提取 | "二维码内容为空" | 加密内容为空 |
| 解密失败 | AES解密 | "二维码解密失败" | 解密异常详情 |
| 格式错误 | 结果解析 | "二维码内容格式错误" | 分割结果不是2部分 |
| 时间戳错误 | 时间戳解析 | "二维码时间戳格式错误" | 时间戳转换失败 |
| 二维码过期 | 时间戳验证 | "二维码已过期，请重新获取" | 时间戳超出有效期 |

## 🔧 技术实现细节

### 关键常量
```kotlin
companion object {
    private const val QR_PREFIX = "mb|login|"
    private const val QR_SUFFIX = "|end"
    private const val AES_TRANSFORMATION = "AES/CBC/PKCS5Padding"
    private const val AES_ALGORITHM = "AES"
    private const val EMPLOYEE_CODE_KEY = "employee_code"
}
```

### 核心函数
1. **validateQRCode()** - 主验证函数
2. **decryptAESCBCBase64()** - AES解密函数
3. **tryDecryptWithZeroIV()** - 备用解密方案
4. **isTimestampValid()** - 时间戳验证函数

### 数据存储
```kotlin
// 保存解密后的员工代码
mmkv.putString(EMPLOYEE_CODE_KEY, employeeCode)

// 保存原始扫码结果（用于调试）
mmkv.putString(SCAN_RESULT_KEY, scanResult)
mmkv.putLong(SCAN_TIMESTAMP_KEY, System.currentTimeMillis())
```

## 🧪 测试验证

### 单元测试覆盖
- ✅ 二维码格式验证
- ✅ 加密内容提取
- ✅ 时间戳验证逻辑
- ✅ 解密结果解析
- ✅ 边界条件处理

### 测试用例示例
```kotlin
@Test
fun testQRCodeFormat_ValidPrefix() {
    val validQRCode = "mb|login|encryptedContent|end"
    assertTrue(validQRCode.startsWith(QR_PREFIX))
    assertTrue(validQRCode.endsWith(QR_SUFFIX))
}
```

## 🚀 使用示例

### 成功流程
```
输入: "mb|login|base64EncryptedContent|end"
  ↓
解密: "EMP001|1642780800"
  ↓
验证: 时间戳有效
  ↓
输出: 员工代码 "EMP001"
  ↓
结果: 登录成功
```

### 失败处理
```
输入: "invalid|format|content"
  ↓
验证: 格式错误
  ↓
输出: null
  ↓
提示: "二维码格式错误"
```

## 📝 注意事项

1. **密钥安全**: ANDROID_ENCRYPT_KEY需要与服务端保持一致
2. **时间同步**: 客户端和服务端时间需要同步，避免时间戳验证错误
3. **IV处理**: 优先使用密文前16字节作为IV，失败时尝试零IV
4. **错误日志**: 详细记录解密过程中的错误，便于调试
5. **用户体验**: 提供清晰的错误提示，避免技术术语

## 🔄 扩展功能

### 可扩展的验证
- 员工权限验证
- 设备绑定验证
- 网络在线验证
- 多重加密支持

### 性能优化
- 解密结果缓存
- 异步解密处理
- 错误重试机制

---

*文档更新时间: 2025-07-16*  
*技术支持: Claude 4.0 sonnet* 🐾
