package com.zhmiaobang.easydianapp.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.RoundedCornersTransformation
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databases.entity.MeasureLog
import com.zhmiaobang.easydianapp.databinding.ItemLoadErrorBinding
import com.zhmiaobang.easydianapp.databinding.ItemLoadingMoreBinding
import com.zhmiaobang.easydianapp.databinding.ItemMeasureLogBinding
import com.zhmiaobang.easydianapp.databinding.ItemNoMoreDataBinding
import com.zhmiaobang.easydianapp.utils.LoadingState
import java.io.File

/**
 * 测量日志列表适配器 - 支持分页加载
 * 用于在 MeasureLogListActivity 中展示测量日志列表
 *
 * 功能特性：
 * - 展示测量日志基本信息（手机号、模型名称、目标图片、计数、创建时间）
 * - 支持查看详情和删除操作
 * - 使用Coil加载图片
 * - 状态指示器显示
 * - 支持分页加载和加载状态显示
 *
 * <AUTHOR> 4.0 sonnet
 */
class MeasureLogAdapter(
    private var measureLogs: MutableList<MeasureLog> = mutableListOf()
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "MeasureLogAdapter"

        // ViewType常量
        private const val TYPE_MEASURE_LOG = 0
        private const val TYPE_LOADING = 1
        private const val TYPE_ERROR = 2
        private const val TYPE_NO_MORE = 3
    }

    // 加载状态
    private var loadingState = LoadingState.IDLE
    private var errorMessage = ""

    // 点击事件监听器
    private var onItemClickListener: ((MeasureLog) -> Unit)? = null
    private var onViewDetailsClickListener: ((MeasureLog) -> Unit)? = null
    private var onDeleteClickListener: ((MeasureLog) -> Unit)? = null
    private var onImageClickListener: ((MeasureLog) -> Unit)? = null
    private var onRetryClickListener: (() -> Unit)? = null

    override fun getItemViewType(position: Int): Int {
        return if (position < measureLogs.size) {
            TYPE_MEASURE_LOG
        } else {
            when (loadingState) {
                LoadingState.LOADING_MORE -> TYPE_LOADING
                LoadingState.ERROR -> TYPE_ERROR
                LoadingState.NO_MORE -> TYPE_NO_MORE
                else -> TYPE_MEASURE_LOG
            }
        }
    }

    override fun getItemCount(): Int {
        return measureLogs.size + if (loadingState != LoadingState.IDLE) 1 else 0
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            TYPE_MEASURE_LOG -> {
                val binding = ItemMeasureLogBinding.inflate(inflater, parent, false)
                MeasureLogViewHolder(binding)
            }
            TYPE_LOADING -> {
                val binding = ItemLoadingMoreBinding.inflate(inflater, parent, false)
                LoadingViewHolder(binding)
            }
            TYPE_ERROR -> {
                val binding = ItemLoadErrorBinding.inflate(inflater, parent, false)
                ErrorViewHolder(binding)
            }
            TYPE_NO_MORE -> {
                val binding = ItemNoMoreDataBinding.inflate(inflater, parent, false)
                NoMoreViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is MeasureLogViewHolder -> {
                if (position < measureLogs.size) {
                    holder.bind(measureLogs[position])
                }
            }
            is ErrorViewHolder -> {
                holder.bind(errorMessage)
            }
        }
    }

    // ==================== 数据管理方法 ====================

    /**
     * 设置新数据（首次加载或刷新）
     */
    fun setData(newMeasureLogs: List<MeasureLog>) {
        measureLogs.clear()
        measureLogs.addAll(newMeasureLogs)
        loadingState = LoadingState.IDLE
        notifyDataSetChanged()
    }

    /**
     * 追加数据（分页加载）
     */
    fun addData(newMeasureLogs: List<MeasureLog>) {
        val startPosition = measureLogs.size
        measureLogs.addAll(newMeasureLogs)
        loadingState = LoadingState.IDLE
        notifyItemRangeInserted(startPosition, newMeasureLogs.size)
    }

    /**
     * 移除指定数据
     */
    fun removeData(measureLog: MeasureLog) {
        val position = measureLogs.indexOf(measureLog)
        if (position != -1) {
            measureLogs.removeAt(position)
            notifyItemRemoved(position)
        }
    }

    /**
     * 显示加载更多状态
     */
    fun showLoadingMore() {
        if (loadingState != LoadingState.LOADING_MORE) {
            loadingState = LoadingState.LOADING_MORE
            notifyItemChanged(itemCount - 1)
        }
    }

    /**
     * 显示加载错误状态
     */
    fun showLoadError(message: String) {
        errorMessage = message
        loadingState = LoadingState.ERROR
        notifyItemChanged(itemCount - 1)
    }

    /**
     * 显示没有更多数据状态
     */
    fun showNoMoreData() {
        if (loadingState != LoadingState.NO_MORE) {
            loadingState = LoadingState.NO_MORE
            notifyItemChanged(itemCount - 1)
        }
    }

    /**
     * 隐藏加载状态
     */
    fun hideLoadingState() {
        if (loadingState != LoadingState.IDLE) {
            loadingState = LoadingState.IDLE
            notifyItemChanged(itemCount - 1)
        }
    }

    // ==================== 事件监听器设置 ====================

    fun setOnItemClickListener(listener: (MeasureLog) -> Unit) {
        onItemClickListener = listener
    }

    fun setOnViewDetailsClickListener(listener: (MeasureLog) -> Unit) {
        onViewDetailsClickListener = listener
    }

    fun setOnDeleteClickListener(listener: (MeasureLog) -> Unit) {
        onDeleteClickListener = listener
    }

    fun setOnImageClickListener(listener: (MeasureLog) -> Unit) {
        onImageClickListener = listener
    }

    fun setOnRetryClickListener(listener: () -> Unit) {
        onRetryClickListener = listener
    }

    // ==================== ViewHolder 类 ====================

    /**
     * 测量日志ViewHolder
     */
    inner class MeasureLogViewHolder(
        private val binding: ItemMeasureLogBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(measureLog: MeasureLog) {
            with(binding) {
                // 绑定基本信息
                tvPhone.text = measureLog.phone
                tvModelName.text = measureLog.getModelName()
                tvCreateTime.text = measureLog.getFormattedCreateTime()

                // 绑定检测参数信息
                tvDetectionParams.text = measureLog.getDetectionParams()

                // 绑定计数结果
                if (measureLog.count != null) {
                    tvCount.text = measureLog.count.toString()
                } else {
                    tvCount.text = "--"
                }

                // 加载目标图片
                loadDestImage(measureLog.destImg)

                // 设置状态指示器
                setStatusIndicator(measureLog.isCompleted())

                // 设置点击事件
                setupClickListeners(measureLog)
            }
        }

        /**
         * 加载目标图片
         */
        private fun loadDestImage(destImgPath: String?) {
            if (destImgPath.isNullOrBlank()) {
                // 没有目标图片，显示占位符
                binding.ivDestImage.setImageResource(R.drawable.ic_image_placeholder)
            } else {
                // 检查是否为本地文件路径
                val imageFile = File(destImgPath)
                if (imageFile.exists()) {
                    // 本地文件，直接加载
                    binding.ivDestImage.load(imageFile) {
                        crossfade(300)
                        placeholder(R.drawable.ic_image_placeholder)
                        error(R.drawable.ic_image_placeholder)
                        transformations(RoundedCornersTransformation(8f))
                    }
                } else {
                    // 可能是URL，尝试加载
                    binding.ivDestImage.load(destImgPath) {
                        crossfade(300)
                        placeholder(R.drawable.ic_image_placeholder)
                        error(R.drawable.ic_image_placeholder)
                        transformations(RoundedCornersTransformation(8f))
                    }
                }
            }
        }

        /**
         * 设置状态指示器
         */
        private fun setStatusIndicator(isCompleted: Boolean) {
            val context = binding.root.context
            if (isCompleted) {
                binding.statusIndicator.backgroundTintList = 
                    context.getColorStateList(R.color.success_color)
                binding.tvStatus.text = "已完成"
                binding.tvStatus.setTextColor(context.getColor(R.color.success_color))
            } else {
                binding.statusIndicator.backgroundTintList = 
                    context.getColorStateList(R.color.warning_color)
                binding.tvStatus.text = "处理中"
                binding.tvStatus.setTextColor(context.getColor(R.color.warning_color))
            }
        }

        /**
         * 设置点击事件
         */
        private fun setupClickListeners(measureLog: MeasureLog) {
            // 整个卡片点击
            binding.root.setOnClickListener {
                onItemClickListener?.invoke(measureLog)
            }

            // 图片点击事件
            binding.ivDestImage.setOnClickListener {
                onImageClickListener?.invoke(measureLog)
            }

            // 查看详情按钮
            binding.btnViewDetails.setOnClickListener {
                onViewDetailsClickListener?.invoke(measureLog)
            }

            // 删除按钮
            binding.btnDelete.setOnClickListener {
                onDeleteClickListener?.invoke(measureLog)
            }
        }
    }

    /**
     * 加载更多ViewHolder
     */
    inner class LoadingViewHolder(
        private val binding: ItemLoadingMoreBinding
    ) : RecyclerView.ViewHolder(binding.root)

    /**
     * 加载错误ViewHolder
     */
    inner class ErrorViewHolder(
        private val binding: ItemLoadErrorBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(message: String) {
            binding.tvErrorMessage.text = message
            binding.btnRetry.setOnClickListener {
                onRetryClickListener?.invoke()
            }
        }
    }

    /**
     * 没有更多数据ViewHolder
     */
    inner class NoMoreViewHolder(
        private val binding: ItemNoMoreDataBinding
    ) : RecyclerView.ViewHolder(binding.root)
}
