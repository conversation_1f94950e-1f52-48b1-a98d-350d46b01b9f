<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.miacochang.employee.EmployeeQrCodeActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/qr_code_toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_color"
            app:title="员工登录二维码"
            app:titleTextColor="@color/white"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Employee Info Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <!-- Employee Avatar -->
                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/iv_employee_avatar"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginEnd="16dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_person"
                        app:shapeAppearanceOverlay="@style/CircleImageView"
                        tools:src="@drawable/ic_person" />

                    <!-- Employee Details -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_employee_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="张师傅" />

                        <TextView
                            android:id="@+id/tv_employee_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            tools:text="ID: 1001" />

                        <TextView
                            android:id="@+id/tv_employee_role"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_hint"
                            android:textSize="12sp"
                            tools:text="A区管理员" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- QR Code Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="16dp"
                        android:text="登录二维码"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- QR Code Image -->
                    <ImageView
                        android:id="@+id/iv_qr_code"
                        android:layout_width="250dp"
                        android:layout_height="250dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/rounded_button_background"
                        android:contentDescription="员工登录二维码"
                        android:padding="8dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/ic_qr_code"
                        tools:src="@drawable/ic_qr_code" />

                    <!-- Countdown Timer -->
                    <TextView
                        android:id="@+id/tv_count_down"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/primary_color"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="剩余时间: 05:00" />

                    <!-- Expired Hint -->
                    <TextView
                        android:id="@+id/tv_expired_hint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="二维码已过期，请重新生成"
                        android:textColor="@color/hIndex_red"
                        android:textSize="14sp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- QR Code Details Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="二维码详情"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- QR Code ID -->
                    <TextView
                        android:id="@+id/tv_qr_code_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="二维码ID: 12345" />

                    <!-- Employee Code -->
                    <TextView
                        android:id="@+id/tv_employee_code"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="员工代码: EMP001" />

                    <!-- Create Time -->
                    <TextView
                        android:id="@+id/tv_create_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="生成时间: 2024-01-01 12:00:00" />

                    <!-- Expire Time -->
                    <TextView
                        android:id="@+id/tv_expire_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="过期时间: 2024-01-01 12:05:00" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Usage Instructions -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="使用说明"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="1. 请在二维码有效期内使用"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="2. 使用登录设备扫描此二维码"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="3. 扫描成功后即可完成登录"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>