<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.miacochang.feed.ShowFeedPointQrCodeActivity">

    <!-- Material Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/feed_qr_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:titleTextColor="@android:color/white"
        app:titleCentered="true"
        app:title="投料点二维码"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@android:color/white" />

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Feed Point Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/hIndex_card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Feed Point Name -->
                    <TextView
                        android:id="@+id/tv_feed_point_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        tools:text="A1池-投料点01" />

                    <!-- Feed Point Details -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginTop="8dp">

                        <!-- Feed Point No -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="编号："
                                android:textColor="@color/hIndex_text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_feed_point_no"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:textColor="@color/hIndex_text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                tools:text="FP001" />

                        </LinearLayout>

                        <!-- Feed Period -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="喂养周期："
                                android:textColor="@color/hIndex_text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_feed_period"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:textColor="@color/hIndex_text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                tools:text="每日3次" />

                        </LinearLayout>

                        <!-- Status -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="状态："
                                android:textColor="@color/hIndex_text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_status"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:textColor="@color/hIndex_green"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                tools:text="有效" />

                        </LinearLayout>

                        <!-- Description -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="描述："
                                android:textColor="@color/hIndex_text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_description"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:textColor="@color/hIndex_text_primary"
                                android:textSize="14sp"
                                tools:text="主要投料点，负责A1池的日常投料" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- QR Code Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/hIndex_card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <!-- QR Code Title -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="投料点二维码"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- QR Code Image -->
                    <ImageView
                        android:id="@+id/iv_qr_code"
                        android:layout_width="250dp"
                        android:layout_height="250dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/rounded_button_background"
                        android:contentDescription="投料点二维码"
                        android:padding="8dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/ic_qr_code"
                        tools:src="@drawable/ic_qr_code" />

                    <!-- QR Code Info -->
                    <TextView
                        android:id="@+id/tv_qr_code_content"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="12sp"
                        android:gravity="center"
                        android:maxLines="2"
                        android:ellipsize="middle"
                        tools:text="QR_1642780800123" />

                    <!-- Loading Indicator -->
                    <com.google.android.material.progressindicator.CircularProgressIndicator
                        android:id="@+id/progress_qr_generation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        app:indicatorColor="@color/hIndex_blue" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <!-- Save Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_save_qr_code"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="保存信息卡片"
                    android:textColor="@color/hIndex_blue"
                    app:strokeColor="@color/hIndex_blue"
                    app:icon="@drawable/ic_save_24"
                    app:iconTint="@color/hIndex_blue" />

                <!-- Share Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_share_qr_code"
                    style="@style/Widget.Material3.Button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="分享卡片"
                    android:textColor="@android:color/white"
                    android:backgroundTint="@color/hIndex_blue"
                    app:icon="@drawable/ic_arrow_right"
                    app:iconTint="@android:color/white" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>