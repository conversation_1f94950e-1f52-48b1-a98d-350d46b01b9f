package com.zhmiaobang.easydianapp

import com.zhmiaobang.easydianapp.databases.entity.MeasureLog
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson
import com.zhmiaobang.easydianapp.json.onnx.ShrimpCate
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试 MeasureLog 实体类的阈值功能
 * 
 * <AUTHOR> 4.0 sonnet
 */
class MeasureLogThresholdTest {

    @Test
    fun testMeasureLogWithThresholds() {
        // 创建测试用的 OnnxModelJson
        val shrimpCate = ShrimpCate(
            id = 1,
            name = "测试虾类",
            description = "测试用虾类分类"
        )

        val onnxModel = OnnxModelJson(
            classes = "虾苗",
            id = 1,
            name = "测试模型",
            description = "测试用ONNX模型",
            isPublic = true,
            localFilename = "test_model.onnx",
            primaryUrl = "http://test.com/model.onnx",
            shrimpCate = shrimpCate,
            status = 1,
            version = "1.0",
            versionCode = 1
        )

        // 测试创建带阈值的 MeasureLog
        val measureLog = MeasureLog.create(
            onnxModelJson = onnxModel,
            phone = "13800138000",
            imgSrc = "/test/image.jpg",
            destImg = "/test/result.jpg",
            count = 156,
            conf = 0.25f,
            nms = 0.45f
        )

        // 验证基本字段
        assertEquals("13800138000", measureLog.phone)
        assertEquals("/test/image.jpg", measureLog.imgSrc)
        assertEquals("/test/result.jpg", measureLog.destImg)
        assertEquals(156, measureLog.count)

        // 验证阈值字段
        assertEquals(0.25f, measureLog.conf!!, 0.001f)
        assertEquals(0.45f, measureLog.nms!!, 0.001f)

        // 验证格式化方法
        assertEquals("0.25", measureLog.getFormattedConfidence())
        assertEquals("0.45", measureLog.getFormattedNms())
        assertEquals("置信度: 0.25, NMS: 0.45", measureLog.getDetectionParams())

        // 验证完成状态
        assertTrue(measureLog.isCompleted())
    }

    @Test
    fun testMeasureLogWithoutThresholds() {
        // 创建测试用的 OnnxModelJson
        val shrimpCate = ShrimpCate(
            id = 1,
            name = "测试虾类",
            description = "测试用虾类分类"
        )

        val onnxModel = OnnxModelJson(
            classes = "虾苗",
            id = 1,
            name = "测试模型",
            description = "测试用ONNX模型",
            isPublic = true,
            localFilename = "test_model.onnx",
            primaryUrl = "http://test.com/model.onnx",
            shrimpCate = shrimpCate,
            status = 1,
            version = "1.0",
            versionCode = 1
        )

        // 测试创建不带阈值的 MeasureLog
        val measureLog = MeasureLog.create(
            onnxModelJson = onnxModel,
            phone = "13800138000",
            imgSrc = "/test/image.jpg"
        )

        // 验证阈值字段为空
        assertNull(measureLog.conf)
        assertNull(measureLog.nms)

        // 验证格式化方法返回默认值
        assertEquals("未记录", measureLog.getFormattedConfidence())
        assertEquals("未记录", measureLog.getFormattedNms())
        assertEquals("置信度: 未记录, NMS: 未记录", measureLog.getDetectionParams())

        // 验证未完成状态
        assertFalse(measureLog.isCompleted())
    }

    @Test
    fun testThresholdFormatting() {
        // 创建测试用的 OnnxModelJson
        val shrimpCate = ShrimpCate(
            id = 1,
            name = "测试虾类",
            description = "测试用虾类分类"
        )

        val onnxModel = OnnxModelJson(
            classes = "虾苗",
            id = 1,
            name = "测试模型",
            description = "测试用ONNX模型",
            isPublic = true,
            localFilename = "test_model.onnx",
            primaryUrl = "http://test.com/model.onnx",
            shrimpCate = shrimpCate,
            status = 1,
            version = "1.0",
            versionCode = 1
        )

        // 测试不同精度的阈值格式化
        val measureLog = MeasureLog.create(
            onnxModelJson = onnxModel,
            phone = "13800138000",
            imgSrc = "/test/image.jpg",
            conf = 0.123456f,
            nms = 0.987654f
        )

        // 验证格式化保留两位小数
        assertEquals("0.12", measureLog.getFormattedConfidence())
        assertEquals("0.99", measureLog.getFormattedNms())
    }
}
