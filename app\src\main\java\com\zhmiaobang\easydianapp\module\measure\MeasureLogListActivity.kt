package com.zhmiaobang.easydianapp.module.measure

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.LinearLayoutManager
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.adapter.MeasureLogAdapter
import com.zhmiaobang.easydianapp.databases.entity.MeasureLog
import com.zhmiaobang.easydianapp.databinding.ActivityMeasureLogListBinding
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.utils.PaginationScrollListener
import com.zhmiaobang.easydianapp.viewmodel.measure.MeasureLogListViewModel

/**
 * 测量日志列表Activity
 *
 * 功能特性：
 * - 显示测量日志列表（支持分页）
 * - 支持按手机号搜索
 * - 支持下拉刷新和上拉加载更多
 * - 支持查看详情和删除操作
 * - 完整的加载状态管理
 *
 * <AUTHOR> 4.0 sonnet
 */
class MeasureLogListActivity : BaseActivity() {

    companion object {
        private const val TAG = "MeasureLogListActivity"
    }

    // ViewBinding
    private lateinit var binding: ActivityMeasureLogListBinding

    // ViewModel
    private val viewModel: MeasureLogListViewModel by viewModels {
        object : androidx.lifecycle.ViewModelProvider.Factory {
            @Suppress("UNCHECKED_CAST")
            override fun <T : androidx.lifecycle.ViewModel> create(modelClass: Class<T>): T {
                return MeasureLogListViewModel(this@MeasureLogListActivity) as T
            }
        }
    }

    // 适配器
    private lateinit var measureLogAdapter: MeasureLogAdapter

    // 当前搜索关键词
    private var currentSearchPhone: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMeasureLogListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeUI()
        setupObservers()
        loadInitialData()
    }

    // ==================== 初始化方法 ====================

    /**
     * 初始化UI组件
     */
   override  fun initializeUI() {
        // 初始化工具栏
        initToolbar(binding.toolbar, "测量记录", showBack = true)

        // 初始化RecyclerView
        setupRecyclerView()

        // 设置下拉刷新
        setupSwipeRefresh()

        // 设置搜索功能
        setupSearch()

        Log.d(TAG, "UI初始化完成")
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        measureLogAdapter = MeasureLogAdapter()
        val layoutManager = LinearLayoutManager(this)

        binding.recyclerView.apply {
            this.layoutManager = layoutManager
            adapter = measureLogAdapter
        }

        // 设置适配器事件监听器
        setupAdapterListeners()

        // 设置分页滚动监听器
        val scrollListener = object : PaginationScrollListener(layoutManager) {
            override fun isLoading(): Boolean {
                return viewModel.getCurrentPaginationState().isLoading
            }

            override fun isLastPage(): Boolean {
                return !viewModel.getCurrentPaginationState().hasMoreData
            }

            override fun loadMoreItems() {
                Log.d(TAG, "滚动触发加载更多")
                viewModel.loadNextPage()
            }
        }

        binding.recyclerView.addOnScrollListener(scrollListener)
    }

    /**
     * 设置适配器事件监听器
     */
    private fun setupAdapterListeners() {
        // 设置点击事件
        measureLogAdapter.setOnItemClickListener { measureLog ->
            Log.d(TAG, "点击测量日志: id=${measureLog.id}")
            // TODO: 可以在这里添加点击整个卡片的逻辑
        }

        // 设置查看详情点击事件
        measureLogAdapter.setOnViewDetailsClickListener { measureLog ->
            Log.d(TAG, "查看详情: id=${measureLog.id}")
            showMeasureLogDetails(measureLog)
        }

        // 设置删除点击事件
        measureLogAdapter.setOnDeleteClickListener { measureLog ->
            Log.d(TAG, "删除测量日志: id=${measureLog.id}")
            showDeleteConfirmDialog(measureLog)
        }

        // 设置图片点击事件
        measureLogAdapter.setOnImageClickListener { measureLog ->
            Log.d(TAG, "点击图片: id=${measureLog.id}")
            openImageDisplayActivity(measureLog)
        }

        // 设置重试点击事件
        measureLogAdapter.setOnRetryClickListener {
            Log.d(TAG, "重试加载")
            viewModel.loadNextPage()
        }
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setOnRefreshListener {
            Log.d(TAG, "下拉刷新触发")
            refreshData()
        }

        // 设置刷新指示器颜色
        binding.swipeRefreshLayout.setColorSchemeResources(
            R.color.primary_color,
            R.color.secondary_color
        )
    }

    /**
     * 设置搜索功能
     */
    private fun setupSearch() {
        // 搜索按钮点击事件
        binding.btnSearch.setOnClickListener {
            val phone = binding.etSearchPhone.text.toString().trim()
            performSearch(phone)
        }

        // 搜索框文本变化监听
        binding.etSearchPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                // 如果搜索框为空，自动清除搜索
                if (s.isNullOrBlank() && !currentSearchPhone.isNullOrBlank()) {
                    performSearch("")
                }
            }
        })
    }

    // ==================== 数据操作方法 ====================

    /**
     * 加载初始数据
     */
    private fun loadInitialData() {
        Log.d(TAG, "开始加载初始数据")
        viewModel.loadFirstPage()
    }

    /**
     * 刷新数据
     */
    private fun refreshData() {
        Log.d(TAG, "刷新数据")
        if (currentSearchPhone.isNullOrBlank()) {
            viewModel.refresh()
        } else {
            viewModel.filterByPhone(currentSearchPhone)
        }
    }

    /**
     * 执行搜索
     */
    private fun performSearch(phone: String) {
        Log.d(TAG, "执行搜索: $phone")
        currentSearchPhone = if (phone.isBlank()) null else phone
        viewModel.filterByPhone(currentSearchPhone)

        // 隐藏软键盘
        binding.etSearchPhone.clearFocus()
    }

    // ==================== 观察者设置 ====================

    /**
     * 设置数据观察者
     */
override fun setupObservers() {
        // 观察分页数据变化
        viewModel.paginatedDataObserver.observe(this) { paginatedData ->
            Log.d(TAG, "收到分页数据更新: items=${paginatedData.items.size}, state=${paginatedData.paginationState.loadingState}")

            try {
                val items = paginatedData.items
                val state = paginatedData.paginationState

                // 停止下拉刷新动画
                binding.swipeRefreshLayout.isRefreshing = false

                // 根据加载状态更新UI
                when (state.loadingState) {
                    LoadingState.LOADING_FIRST -> {
                        // 首次加载，显示主加载指示器
                        showLoading()
                    }
                    LoadingState.LOADING_MORE -> {
                        // 加载更多，在adapter中显示
                        hideLoading()
                        measureLogAdapter.showLoadingMore()
                    }
                    LoadingState.ERROR -> {
                        // 加载错误
                        hideLoading()
                        if (items.isEmpty()) {
                            showError(state.errorMessage ?: "加载失败")
                        } else {
                            measureLogAdapter.showLoadError(state.errorMessage ?: "加载失败")
                        }
                    }
                    LoadingState.NO_MORE -> {
                        // 没有更多数据
                        hideLoading()
                        measureLogAdapter.showNoMoreData()
                    }
                    LoadingState.IDLE -> {
                        // 加载完成
                        hideLoading()
                        measureLogAdapter.hideLoadingState()
                    }
                }

                // 更新数据
                if (state.currentPage <= 1) {
                    // 首次加载或刷新
                    measureLogAdapter.setData(items)
                    if (items.isEmpty()) {
                        showEmpty("暂无测量记录")
                    } else {
                        hideEmpty()
                    }
                } else {
                    // 分页加载
                    val newItems = items.drop(measureLogAdapter.itemCount - if (measureLogAdapter.itemCount > items.size) 1 else 0)
                    if (newItems.isNotEmpty()) {
                        measureLogAdapter.addData(newItems)
                    }
                }

                Log.d(TAG, "数据更新完成: 总计${items.size}条记录")

            } catch (e: Exception) {
                Log.e(TAG, "处理分页数据时发生错误: ${e.message}", e)
                showError("数据处理失败")
            }
        }

        // 观察错误信息
        viewModel.errorObserver.observe(this) { errorMessage ->
            Log.e(TAG, "收到错误信息: $errorMessage")
            showToast(errorMessage)
            binding.swipeRefreshLayout.isRefreshing = false
        }
    }

    // ==================== UI状态管理 ====================

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.loadingView.root.visibility = View.VISIBLE
        binding.swipeRefreshLayout.visibility = View.GONE
        binding.errorView.root.visibility = View.GONE
        binding.emptyView.root.visibility = View.GONE
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.loadingView.root.visibility = View.GONE
        binding.swipeRefreshLayout.visibility = View.VISIBLE
    }

    /**
     * 显示错误状态
     */
    private fun showError(message: String) {
        binding.errorView.root.visibility = View.VISIBLE
        binding.swipeRefreshLayout.visibility = View.GONE
        binding.loadingView.root.visibility = View.GONE
        binding.emptyView.root.visibility = View.GONE

        // 设置错误信息和重试按钮
        // 注意：这里需要根据实际的错误布局文件来设置
        // binding.errorView.tvErrorMessage.text = message
        // binding.errorView.btnRetry.setOnClickListener { refreshData() }
    }

    /**
     * 显示空状态
     */
    private fun showEmpty(message: String) {
        binding.emptyView.root.visibility = View.VISIBLE
        binding.swipeRefreshLayout.visibility = View.GONE
        binding.loadingView.root.visibility = View.GONE
        binding.errorView.root.visibility = View.GONE

        // 设置空状态信息
        // 注意：这里需要根据实际的空状态布局文件来设置
        // binding.emptyView.tvEmptyMessage.text = message
    }

    /**
     * 隐藏空状态
     */
    private fun hideEmpty() {
        binding.emptyView.root.visibility = View.GONE
    }

    // ==================== 对话框和详情显示 ====================

    /**
     * 显示测量日志详情
     */
    private fun showMeasureLogDetails(measureLog: MeasureLog) {
        val details = buildString {
            appendLine("测量记录详情")
            appendLine("━━━━━━━━━━━━━━━━━━━━")
            appendLine("📱 手机号: ${measureLog.phone}")
            appendLine("🤖 模型名称: ${measureLog.getModelName()}")
            appendLine("📂 模型分类: ${measureLog.getCategoryName()}")
            appendLine("📊 计数结果: ${measureLog.count ?: "未完成"}")
            appendLine("⚙️ 置信度阈值: ${measureLog.getFormattedConfidence()}")
            appendLine("🔧 NMS阈值: ${measureLog.getFormattedNms()}")
            appendLine("📷 源图片: ${measureLog.imgSrc}")
            appendLine("🖼️ 目标图片: ${measureLog.destImg ?: "未生成"}")
            appendLine("⏰ 创建时间: ${measureLog.getFormattedCreateTime()}")
            appendLine("✅ 状态: ${if (measureLog.isCompleted()) "已完成" else "处理中"}")
        }

        AlertDialog.Builder(this)
            .setTitle("测量记录详情")
            .setMessage(details)
            .setPositiveButton("确定", null)
            .setNeutralButton("删除") { _, _ ->
                showDeleteConfirmDialog(measureLog)
            }
            .show()
    }

    /**
     * 显示删除确认对话框
     */
    private fun showDeleteConfirmDialog(measureLog: MeasureLog) {
        AlertDialog.Builder(this)
            .setTitle("确认删除")
            .setMessage("确定要删除这条测量记录吗？\n\n手机号: ${measureLog.phone}\n模型: ${measureLog.getModelName()}\n时间: ${measureLog.getFormattedCreateTime()}")
            .setPositiveButton("删除") { _, _ ->
                deleteMeasureLog(measureLog)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 删除测量日志
     */
    private fun deleteMeasureLog(measureLog: MeasureLog) {
        Log.d(TAG, "开始删除测量日志: id=${measureLog.id}")

        viewModel.deleteMeasureLog(measureLog)

        // 从适配器中移除数据
        measureLogAdapter.removeData(measureLog)

        showToast("删除成功")
    }

    /**
     * 打开图片显示Activity
     */
    private fun openImageDisplayActivity(measureLog: MeasureLog) {
        try {
            // 检查是否有目标图片
            if (measureLog.destImg.isNullOrBlank()) {
                Log.w(TAG, "测量记录没有目标图片: id=${measureLog.id}")
                showToast("该记录没有处理结果图片")
                return
            }

            Log.d(TAG, "准备打开图片显示界面:")
            Log.d(TAG, "  测量记录ID: ${measureLog.id}")
            Log.d(TAG, "  图片路径: ${measureLog.destImg}")
            Log.d(TAG, "  计数结果: ${measureLog.count}")
            Log.d(TAG, "  手机号: ${measureLog.phone}")
            Log.d(TAG, "  模型名称: ${measureLog.getModelName()}")

            // 检查图片文件是否存在
            val imageFile = java.io.File(measureLog.destImg!!)
            if (!imageFile.exists()) {
                Log.e(TAG, "图片文件不存在: ${measureLog.destImg}")
                showToast("图片文件不存在，可能已被删除")
                return
            }

            Log.d(TAG, "图片文件存在，大小: ${imageFile.length()} bytes")

            val intent = Intent(this, DisplayMeasureImageActivity::class.java).apply {
                putExtra(DisplayMeasureImageActivity.EXTRA_IMAGE_PATH, measureLog.destImg)
                putExtra(DisplayMeasureImageActivity.EXTRA_COUNT_VALUE, measureLog.count ?: 0)
                putExtra(DisplayMeasureImageActivity.EXTRA_TITLE, "测量结果 - ${measureLog.getModelName()}")
                putExtra(DisplayMeasureImageActivity.EXTRA_PHONE, measureLog.phone)
                putExtra(DisplayMeasureImageActivity.EXTRA_MODEL_NAME, measureLog.getModelName())
            }

            startActivity(intent)
            Log.d(TAG, "成功启动图片显示Activity")

        } catch (e: Exception) {
            Log.e(TAG, "打开图片显示界面失败: ${e.message}", e)
            showToast("打开图片失败，请重试")
        }
    }
}