package com.zhmiaobang.easydianapp.databases.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.zhmiaobang.easydianapp.databases.entity.MeasureLog

/**
 * 测量日志数据访问对象 (DAO)
 * 
 * 提供对 MeasureLog 表的完整 CRUD 操作，包括：
 * - 基本的增删改查操作
 * - 按条件查询（时间范围、电话号码、完成状态等）
 * - 统计查询（总数、完成数等）
 * - LiveData 支持实时数据更新
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Dao
interface MeasureLogDao {
    
    // ==================== 基本 CRUD 操作 ====================
    
    /**
     * 插入单个测量日志记录
     * 
     * @param measureLog 要插入的测量日志
     * @return 插入记录的 ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(measureLog: MeasureLog): Long
    
    /**
     * 插入多个测量日志记录
     * 
     * @param measureLogs 要插入的测量日志列表
     * @return 插入记录的 ID 列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(measureLogs: List<MeasureLog>): List<Long>
    
    /**
     * 更新测量日志记录
     * 
     * @param measureLog 要更新的测量日志
     * @return 受影响的行数
     */
    @Update
    suspend fun update(measureLog: MeasureLog): Int
    
    /**
     * 删除测量日志记录
     * 
     * @param measureLog 要删除的测量日志
     * @return 受影响的行数
     */
    @Delete
    suspend fun delete(measureLog: MeasureLog): Int
    
    /**
     * 根据 ID 删除测量日志记录
     * 
     * @param id 要删除的记录 ID
     * @return 受影响的行数
     */
    @Query("DELETE FROM measure_log WHERE id = :id")
    suspend fun deleteById(id: Long): Int
    
    /**
     * 清空所有测量日志记录
     * 
     * @return 受影响的行数
     */
    @Query("DELETE FROM measure_log")
    suspend fun deleteAll(): Int
    
    // ==================== 查询操作 ====================
    
    /**
     * 根据 ID 获取测量日志记录
     * 
     * @param id 记录 ID
     * @return 测量日志记录，如果不存在则返回 null
     */
    @Query("SELECT * FROM measure_log WHERE id = :id")
    suspend fun getById(id: Long): MeasureLog?
    
    /**
     * 获取所有测量日志记录（按创建时间倒序）
     * 
     * @return 所有测量日志记录的 LiveData
     */
    @Query("SELECT * FROM measure_log ORDER BY create_at DESC")
    fun getAllLiveData(): LiveData<List<MeasureLog>>
    
    /**
     * 获取所有测量日志记录（按创建时间倒序）
     * 
     * @return 所有测量日志记录
     */
    @Query("SELECT * FROM measure_log ORDER BY create_at DESC")
    suspend fun getAll(): List<MeasureLog>
    
    /**
     * 分页获取测量日志记录
     * 
     * @param limit 每页数量
     * @param offset 偏移量
     * @return 测量日志记录列表
     */
    @Query("SELECT * FROM measure_log ORDER BY create_at DESC LIMIT :limit OFFSET :offset")
    suspend fun getPage(limit: Int, offset: Int): List<MeasureLog>
    
    /**
     * 根据电话号码查询测量日志记录
     * 
     * @param phone 电话号码
     * @return 该电话号码的所有测量日志记录
     */
    @Query("SELECT * FROM measure_log WHERE phone = :phone ORDER BY create_at DESC")
    suspend fun getByPhone(phone: String): List<MeasureLog>
    
    /**
     * 根据电话号码查询测量日志记录（LiveData）
     * 
     * @param phone 电话号码
     * @return 该电话号码的所有测量日志记录的 LiveData
     */
    @Query("SELECT * FROM measure_log WHERE phone = :phone ORDER BY create_at DESC")
    fun getByPhoneLiveData(phone: String): LiveData<List<MeasureLog>>
    
    /**
     * 根据时间范围查询测量日志记录
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 指定时间范围内的测量日志记录
     */
    @Query("SELECT * FROM measure_log WHERE create_at BETWEEN :startTime AND :endTime ORDER BY create_at DESC")
    suspend fun getByTimeRange(startTime: Long, endTime: Long): List<MeasureLog>
    
    /**
     * 获取已完成的测量日志记录（有计数结果和目标图片）
     * 
     * @return 已完成的测量日志记录
     */
    @Query("SELECT * FROM measure_log WHERE count IS NOT NULL AND dest_img IS NOT NULL ORDER BY create_at DESC")
    suspend fun getCompleted(): List<MeasureLog>
    
    /**
     * 获取未完成的测量日志记录
     * 
     * @return 未完成的测量日志记录
     */
    @Query("SELECT * FROM measure_log WHERE count IS NULL OR dest_img IS NULL ORDER BY create_at DESC")
    suspend fun getIncomplete(): List<MeasureLog>
    
    // ==================== 统计查询 ====================
    
    /**
     * 获取测量日志记录总数
     * 
     * @return 记录总数
     */
    @Query("SELECT COUNT(*) FROM measure_log")
    suspend fun getCount(): Int
    
    /**
     * 获取已完成的测量日志记录数量
     * 
     * @return 已完成的记录数量
     */
    @Query("SELECT COUNT(*) FROM measure_log WHERE count IS NOT NULL AND dest_img IS NOT NULL")
    suspend fun getCompletedCount(): Int
    
    /**
     * 获取指定电话号码的测量日志记录数量
     * 
     * @param phone 电话号码
     * @return 该电话号码的记录数量
     */
    @Query("SELECT COUNT(*) FROM measure_log WHERE phone = :phone")
    suspend fun getCountByPhone(phone: String): Int
    
    /**
     * 获取指定时间范围内的测量日志记录数量
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 指定时间范围内的记录数量
     */
    @Query("SELECT COUNT(*) FROM measure_log WHERE create_at BETWEEN :startTime AND :endTime")
    suspend fun getCountByTimeRange(startTime: Long, endTime: Long): Int
    
    // ==================== 更新操作 ====================
    
    /**
     * 更新测量结果
     *
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @param count 计数结果
     * @return 受影响的行数
     */
    @Query("UPDATE measure_log SET dest_img = :destImg, count = :count WHERE id = :id")
    suspend fun updateMeasureResult(id: Long, destImg: String?, count: Int?): Int

    /**
     * 更新测量结果（包含阈值信息）
     *
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @param count 计数结果
     * @param conf 置信度阈值
     * @param nms NMS阈值
     * @return 受影响的行数
     */
    @Query("UPDATE measure_log SET dest_img = :destImg, count = :count, conf = :conf, nms = :nms WHERE id = :id")
    suspend fun updateMeasureResultWithThresholds(id: Long, destImg: String?, count: Int?, conf: Float?, nms: Float?): Int



    // ==================== 分页查询操作 ====================

    /**
     * 分页查询测量日志记录
     *
     * @param limit 每页数量
     * @param offset 偏移量
     * @return 分页的测量日志记录列表
     */
    @Query("SELECT * FROM measure_log ORDER BY create_at DESC LIMIT :limit OFFSET :offset")
    suspend fun getPagedMeasureLogs(limit: Int, offset: Int): List<MeasureLog>

    /**
     * 获取测量日志记录总数（用于分页计算）
     *
     * @return 记录总数
     */
    @Query("SELECT COUNT(*) FROM measure_log")
    suspend fun getTotalCount(): Int

    /**
     * 根据电话号码分页查询测量日志记录
     *
     * @param phone 电话号码
     * @param limit 每页数量
     * @param offset 偏移量
     * @return 该电话号码的分页测量日志记录
     */
    @Query("SELECT * FROM measure_log WHERE phone = :phone ORDER BY create_at DESC LIMIT :limit OFFSET :offset")
    suspend fun getPagedMeasureLogsByPhone(phone: String, limit: Int, offset: Int): List<MeasureLog>

    /**
     * 根据电话号码获取记录总数
     *
     * @param phone 电话号码
     * @return 该电话号码的记录总数
     */
    @Query("SELECT COUNT(*) FROM measure_log WHERE phone = :phone")
    suspend fun getTotalCountByPhone(phone: String): Int
    
    /**
     * 更新目标图片路径
     * 
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @return 受影响的行数
     */
    @Query("UPDATE measure_log SET dest_img = :destImg WHERE id = :id")
    suspend fun updateDestImg(id: Long, destImg: String?): Int
    
    /**
     * 更新计数结果
     * 
     * @param id 记录 ID
     * @param count 计数结果
     * @return 受影响的行数
     */
    @Query("UPDATE measure_log SET count = :count WHERE id = :id")
    suspend fun updateCount(id: Long, count: Int?): Int
}
