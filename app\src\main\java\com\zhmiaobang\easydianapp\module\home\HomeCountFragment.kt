package com.zhmiaobang.easydianapp.module.home

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.google.android.material.button.MaterialButton
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.FragmentHomeCountBinding
import com.zhmiaobang.easydianapp.json.count.BatchCountJson
import com.zhmiaobang.easydianapp.module.count.CountCameraActivity
import java.util.Date

class HomeCountFragment : Fragment() {

    companion object {
        fun newInstance() = HomeCountFragment()

        // 常量定义
        private val SHRIMP_TYPES = arrayOf("南美白", "班结虾", "罗氏虾")
        private val SUPPORTED_IMAGE_TYPES = arrayOf(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
        )

        // Toast消息常量
        private const val MSG_PHONE_REQUIRED = "请先输入电话号码"
        private const val MSG_CATEGORY_REQUIRED = "请先选择虾苗类型"
        private const val MSG_PERMISSION_REQUIRED = "需要存储权限才能选择图片"
        private const val MSG_IMAGE_SUCCESS = "图片选择成功"
        private const val MSG_IMAGE_PATH_ERROR = "无法获取图片路径"
        private const val MSG_CATEGORY_SELECTED = "已选择: %s"
    }

    // ViewBinding
    private var _binding: FragmentHomeCountBinding? = null
    private val binding get() = _binding!!

    // ViewModel
    private val viewModel: HomeCountViewModel by viewModels()

    // 数据存储变量
    private var batchCountJson: BatchCountJson? = null
    private var imgSrc: String = ""
    private var countCate: String = ""
    private var selectedShrimpIndex = -1

    // Activity Result Launchers
    private lateinit var permissionLauncher: ActivityResultLauncher<String>
    private lateinit var imagePickerLauncher: ActivityResultLauncher<Intent>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initializeLaunchers()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeCountBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupClickListeners()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    /**
     * 初始化Activity Result Launchers
     */
    private fun initializeLaunchers() {
        // 权限请求launcher
        permissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            if (isGranted) {
                openGallery()
            } else {
                showToast(MSG_PERMISSION_REQUIRED)
            }
        }

        // 图片选择launcher
        imagePickerLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            handleImagePickerResult(result)
        }
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        binding.countBtnGallery.setOnClickListener {
            if (validateInputs()) {
                checkPermissionAndOpenGallery()
            }
        }

        binding.txtCountSelectCategory.setOnClickListener {
            showShrimpTypeDialog()
        }

        binding.countBtnCamera.setOnClickListener {
            if (validateInputs()) {
                createBatchCountJson()
                // TODO: 实现相机功能

                val intent = Intent(<EMAIL>(), CountCameraActivity::class.java)
                intent.putExtra("batch_count",batchCountJson)
                startActivity(intent)
            }
        }
    }

    /**
     * 验证输入数据
     */
    private fun validateInputs(): Boolean {
        val phoneNumber = getPhoneNumber()

        if (phoneNumber.isNullOrEmpty()) {
            showToast(MSG_PHONE_REQUIRED)
            return false
        }

        if (countCate.isEmpty()) {
            showToast(MSG_CATEGORY_REQUIRED)
            return false
        }

        return true
    }

    /**
     * 获取电话号码
     */
    private fun getPhoneNumber(): String? {
        return binding.edCountPhone.text?.toString()?.trim()
    }

    /**
     * 创建BatchCountJson对象
     */
    private fun createBatchCountJson() {
        val phoneNumber = getPhoneNumber() ?: return

        batchCountJson = BatchCountJson(
            phone = phoneNumber,
            batchNo = Date().time.toString(),
            create_at = Date().time,
            cate = countCate
        )

    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        context?.let { ctx ->
            Toast.makeText(ctx, message, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 检查权限并打开相册
     * 支持Android SDK 29-36
     */
    private fun checkPermissionAndOpenGallery() {
        val permission = getRequiredPermission()

        when {
            ContextCompat.checkSelfPermission(requireContext(), permission) == PackageManager.PERMISSION_GRANTED -> {
                openGallery()
            }
            else -> {
                permissionLauncher.launch(permission)
            }
        }
    }

    /**
     * 获取所需权限（根据Android版本）
     */
    private fun getRequiredPermission(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES // Android 13+
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE // Android 10-12
        }
    }

    /**
     * 打开系统相册 - 仅限图片
     */
    private fun openGallery() {
        createBatchCountJson()

        val intent = Intent(Intent.ACTION_PICK).apply {
            type = "image/*"
            data = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            putExtra(Intent.EXTRA_MIME_TYPES, SUPPORTED_IMAGE_TYPES)
        }
        imagePickerLauncher.launch(intent)
    }

    /**
     * 处理图片选择结果
     */
    private fun handleImagePickerResult(result: androidx.activity.result.ActivityResult) {
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                val imagePath = getImagePath(uri)
                if (imagePath != null) {
                    imgSrc = imagePath
                    batchCountJson?.imgSrc = imagePath
                    showToast("$MSG_IMAGE_SUCCESS: $imgSrc")
                } else {
                    showToast(MSG_IMAGE_PATH_ERROR)
                }
            }
        }
    }

    /**
     * 获取图片的绝对路径
     * 兼容不同Android版本的存储访问方式
     */
    private fun getImagePath(uri: Uri): String? {
        return try {
            val projection = arrayOf(MediaStore.Images.Media.DATA)
            requireContext().contentResolver.query(
                uri, projection, null, null, null
            )?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
                    cursor.getString(columnIndex)
                } else null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 显示虾苗类型选择对话框
     */
    private fun showShrimpTypeDialog() {
        context?.let { ctx ->
            AlertDialog.Builder(ctx)
                .setTitle("选择虾苗类型")
                .setSingleChoiceItems(SHRIMP_TYPES, selectedShrimpIndex) { dialog, which ->
                    selectedShrimpIndex = which
                    countCate = SHRIMP_TYPES[which]

                    // 更新界面显示
                    binding.txtCountSelectCategory.text = countCate

                    // 显示选择结果
                    showToast(MSG_CATEGORY_SELECTED.format(countCate))

                    dialog.dismiss()
                }
                .setNegativeButton("取消", null)
                .show()
        }
    }
}