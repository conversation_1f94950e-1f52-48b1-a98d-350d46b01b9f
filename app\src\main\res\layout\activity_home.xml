<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/home_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".module.home.HomeActivity">

    <!-- Material Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/tbarHome"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:titleTextColor="@android:color/white"
        app:titleCentered="true"
        app:title="@string/app_name"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar" />

    <!-- Navigation Host Fragment -->
    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/home_nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/tbarHome"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/home_bottom_navigation"
        app:navGraph="@navigation/nav_home"
        app:defaultNavHost="true" />

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/home_bottom_navigation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/home_nav_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:menu="@menu/menu_home_bottom"
        app:itemIconTint="@color/home_bottom_nav_color"
        app:itemTextColor="@color/home_bottom_nav_color"
        app:itemIconSize="24dp"
        app:labelVisibilityMode="labeled" />

</androidx.constraintlayout.widget.ConstraintLayout>