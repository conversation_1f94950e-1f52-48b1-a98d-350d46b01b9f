package com.zhmiaobang.easydianapp.module.miacochang.employee

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.adapter.EmployeeAdapter
import com.zhmiaobang.easydianapp.databinding.ActivityEmployeeListBinding
import com.zhmiaobang.easydianapp.json.user.UserJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.viewmodel.user.EmployeeViewModel
import kotlin.collections.isNotEmpty

/**
 * 员工列表Activity
 *
 * 功能特性：
 * - 展示员工列表数据
 * - 点击头像/姓名跳转到编辑页面
 * - 支持下拉刷新
 * - 完整的加载状态和错误处理
 *
 * <AUTHOR> 4.0 sonnet
 */
class EmployeeListActivity : BaseActivity() {

    companion object {
        private const val TAG = "EmployeeListActivity"
        private const val REQUEST_CODE_EDIT = 1001
    }

    // ViewBinding
    private val binding: ActivityEmployeeListBinding by lazy {
        ActivityEmployeeListBinding.inflate(layoutInflater)
    }

    // ViewModel
    private val viewModel: EmployeeViewModel by viewModels()

    // Adapter
    private lateinit var employeeAdapter: EmployeeAdapter

    // 当前选中的员工（用于二维码生成）
    private var currentSelectedEmployee: UserJson? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initializeUI()
        setupObservers()

        Log.d(TAG, "EmployeeListActivity创建完成，观察者已设置")
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()
        setupToolbar()
        setupRecyclerView()
        setupSwipeRefresh()

        loadEmployeeData()
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()
        Log.d(TAG, "开始设置观察者")

        // 观察分页数据
        viewModel.paginatedDataObserver.observe(this) { paginatedData ->
            Log.d(TAG, "收到分页数据更新: items=${paginatedData.items.size}, state=${paginatedData.paginationState}")

            try {
                val state = paginatedData.paginationState
                val items = paginatedData.items

                // 处理加载状态
                when (state.loadingState) {
                    LoadingState.LOADING_FIRST -> {
                        if (items.isEmpty()) {
                            showLoading()
                        }
                        binding.listEmployeeSwipeRefreshLayout.isRefreshing = false
                    }
                    LoadingState.ERROR -> {
                        hideLoading()
                        binding.listEmployeeSwipeRefreshLayout.isRefreshing = false
                        if (items.isEmpty()) {
                            showError(state.errorMessage ?: "加载失败")
                        } else {
                            employeeAdapter.showLoadError(state.errorMessage ?: "加载失败")
                        }
                    }
                    else -> {
                        hideLoading()
                        binding.listEmployeeSwipeRefreshLayout.isRefreshing = false
                    }
                }

                // 更新数据显示
                if (items.isNotEmpty()) {
                    showEmployeeList(items)
                    Log.d(TAG, "显示员工列表，总数量: ${items.size}")
                } else if (state.loadingState != LoadingState.LOADING_FIRST) {
                    showEmptyState()
                }

            } catch (e: Exception) {
                Log.e(TAG, "处理分页数据失败: ${e.message}", e)
                hideLoading()
                binding.listEmployeeSwipeRefreshLayout.isRefreshing = false
                showError("数据处理失败，请重试")
            }
        }

        // 观察错误信息
        viewModel.errorObserver.observe(this) { errorMessage ->
            Log.e(TAG, "ViewModel错误: $errorMessage")
            hideLoading()
            showError(errorMessage)
        }

        // 观察二维码获取结果
        viewModel.getLoginCodeObserver.observe(this) { response ->
            hideLoading()

            try {
                when (response.code) {
                    200 -> {
                        Log.d(TAG, "获取员工登录二维码成功")
                        val loginCodeData = response.results
                        val employee = currentSelectedEmployee

                        if (employee != null) {
                            navigateToQrCodeActivity(loginCodeData, employee)
                        } else {
                            showToast("员工信息异常")
                        }
                    }
                    else -> {
                        Log.w(TAG, "获取员工登录二维码失败: ${response.msg}")
                        showToast("获取二维码失败: ${response.msg}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理二维码获取结果失败: ${e.message}", e)
                showToast("获取二维码失败，请重试")
            }
        }

        Log.d(TAG, "观察者设置完成")
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        initToolbar(
            toolbar = binding.listEmployeeToolbar,
            title = "员工管理",
            showBack = true
        )
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        employeeAdapter = EmployeeAdapter()
        val layoutManager = LinearLayoutManager(this)

        binding.listEmployeeRecyclerView.apply {
            this.layoutManager = layoutManager
            adapter = employeeAdapter
            setHasFixedSize(true)
        }

        // 设置点击事件监听器
        employeeAdapter.setOnItemClickListener(object : EmployeeAdapter.OnItemClickListener {
            override fun onItemClick(employee: UserJson) {
                Log.d(TAG, "点击员工: ${employee.nickname}")
                navigateToEditActivity(employee)
            }

            override fun onQrCodeClick(employee: UserJson) {
                Log.d(TAG, "点击员工二维码: ${employee.nickname}")
                generateEmployeeQrCode(employee)
            }

            override fun onRetryClick() {
                Log.d(TAG, "点击重试按钮")
                viewModel.retryLoad()
            }
        })
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.listEmployeeSwipeRefreshLayout.setOnRefreshListener {
            Log.d(TAG, "下拉刷新触发")
            refreshData()
        }

        // 设置刷新指示器颜色
        binding.listEmployeeSwipeRefreshLayout.setColorSchemeResources(
            R.color.primary_color,
            R.color.hIndex_green,
            R.color.hIndex_blue
        )
    }

    /**
     * 加载员工数据
     */
    private fun loadEmployeeData() {
        Log.d(TAG, "开始加载员工数据")

        // 检查JWT Token
        val token = com.zhmiaobang.easydianapp.init.ConfigTools.getJwtToken()
        if (token == null) {
            Log.e(TAG, "JWT Token为空，无法发送请求")
            showError("用户未登录，请重新登录")
            return
        }

        // 检查用户信息
        val user = com.zhmiaobang.easydianapp.init.ConfigTools.getUser()
        if (user == null) {
            Log.e(TAG, "用户信息为空")
            showError("用户信息异常，请重新登录")
            return
        }

        try {
            Log.d(TAG, "开始加载第一页员工数据...")
            viewModel.loadFirstPage()
            Log.d(TAG, "员工数据请求已发送")
        } catch (e: Exception) {
            hideLoading()
            binding.listEmployeeSwipeRefreshLayout.isRefreshing = false
            val errorMessage = ExceptionUtil.catchException(e)
            showError(errorMessage)
            Log.e(TAG, "加载员工数据失败: ${e.message}", e)
        }
    }

    /**
     * 显示员工列表
     */
    private fun showEmployeeList(employees: List<UserJson>) {
        Log.d(TAG, "显示员工列表，数量: ${employees.size}")

        binding.listEmployeeRecyclerView.visibility = View.VISIBLE
        binding.listEmployeeEmptyStateLayout.visibility = View.GONE

        employeeAdapter.updateData(employees)
    }

    /**
     * 显示空状态
     */
    private fun showEmptyState() {
        Log.d(TAG, "显示空状态")

        binding.listEmployeeRecyclerView.visibility = View.GONE
        binding.listEmployeeEmptyStateLayout.visibility = View.VISIBLE
    }

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.listEmployeeProgressIndicator.visibility = View.VISIBLE
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.listEmployeeProgressIndicator.visibility = View.GONE
    }

    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        showToast(message)
        showEmptyState()
    }

    /**
     * 跳转到编辑员工页面
     */
    private fun navigateToEditActivity(employee: UserJson?) {
        val intent = Intent(this, EditEmployeeActivity::class.java)

        employee?.let {
            intent.putExtra("employee", it)
        }

        // 使用 startActivityForResult 以便接收返回结果
        startActivityForResult(intent, REQUEST_CODE_EDIT)
    }

    /**
     * 处理Activity返回结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_CODE_EDIT && resultCode == Activity.RESULT_OK) {
            // 编辑成功，刷新列表
            refreshData()
        }
    }

    /**
     * 刷新数据
     */
    private fun refreshData() {
        Log.d(TAG, "刷新员工数据")
        viewModel.refresh()
    }

    /**
     * 生成员工登录二维码
     */
    private fun generateEmployeeQrCode(employee: UserJson) {

        if(employee.nickname.isNullOrBlank() ){
            showToast("昵称不能为空")
            return
        }

        if (employee.is_active==false){
            showToast("请开通")
            return
        }


        Log.d(TAG, "开始生成员工登录二维码: ${employee.nickname}")

        // 检查JWT Token
        val token = com.zhmiaobang.easydianapp.init.ConfigTools.getJwtToken()
        if (token == null) {
            Log.e(TAG, "JWT Token为空，无法发送请求")
            showToast("用户未登录，请重新登录")
            return
        }

        // 保存当前选中的员工
        currentSelectedEmployee = employee

        try {
            showLoading()
            Log.d(TAG, "开始请求员工登录二维码...")
            viewModel.get_login_qrcode(employee)
            Log.d(TAG, "员工登录二维码请求已发送")
        } catch (e: Exception) {
            hideLoading()
            val errorMessage = ExceptionUtil.catchException(e)
            showToast("获取二维码失败: $errorMessage")
            Log.e(TAG, "生成员工登录二维码失败: ${e.message}", e)
        }
    }

    /**
     * 跳转到二维码显示页面
     */
    private fun navigateToQrCodeActivity(loginCodeData: com.zhmiaobang.easydianapp.json.employee.EmployeeLoginCodeJson, employee: UserJson) {
        try {
            val intent = Intent(this, EmployeeQrCodeActivity::class.java)
            intent.putExtra("loginCodeData", loginCodeData)
            intent.putExtra("employee", employee)
            startActivity(intent)
            Log.d(TAG, "成功跳转到员工二维码显示页面")
        } catch (e: Exception) {
            Log.e(TAG, "跳转到员工二维码显示页面失败: ${e.message}", e)
            showToast("页面跳转失败")
        }
    }
}