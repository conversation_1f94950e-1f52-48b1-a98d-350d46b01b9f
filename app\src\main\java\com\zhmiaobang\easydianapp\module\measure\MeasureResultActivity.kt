package com.zhmiaobang.easydianapp.module.measure

import android.app.AlertDialog
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.exifinterface.media.ExifInterface
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.button.MaterialButton
import com.google.android.material.progressindicator.LinearProgressIndicator
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databases.MeasureLogManager
import com.zhmiaobang.easydianapp.databases.entity.MeasureLog
import com.zhmiaobang.easydianapp.onnx.OnnxModelManager
import com.zhmiaobang.easydianapp.onnx.YoloV8Detector
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream

/**
 * 测量结果Activity
 *
 * 用于显示和处理测量结果，包括：
 * - 显示原始图片
 * - 执行ONNX模型推理
 * - 显示测量结果
 * - 保存处理后的图片
 *
 * <AUTHOR> 4.0 sonnet
 */
class MeasureResultActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MeasureResultActivity"

        // Intent 传递的参数键
        const val EXTRA_MEASURE_LOG_ID = "measure_log_id"
        const val EXTRA_IMAGE_PATH = "image_path"
    }

    // 传递的参数
    private var measureLogId: Long = -1
    private var imagePath: String? = null

    // 数据管理器
    private lateinit var measureLogManager: MeasureLogManager
    private lateinit var onnxModelManager: OnnxModelManager
    private lateinit var yoloDetector: YoloV8Detector

    // UI 组件
    private lateinit var toolbar: MaterialToolbar
    private lateinit var imageResult: ImageView
    private lateinit var progressAnalysis: LinearProgressIndicator
    private lateinit var tvAnalysisStatus: TextView
    private lateinit var cardAnalysisStatus: CardView
    private lateinit var cardCountResult: CardView
    private lateinit var cardErrorInfo: CardView
    private lateinit var tvCountResult: TextView
    private lateinit var tvAnalysisTime: TextView
    private lateinit var tvErrorMessage: TextView
    private lateinit var btnRetry: MaterialButton

    // 检测结果卡片内的组件
    private lateinit var layoutLoadingState: androidx.constraintlayout.widget.ConstraintLayout
    private lateinit var layoutResultState: androidx.constraintlayout.widget.ConstraintLayout
    private lateinit var progressDetection: com.google.android.material.progressindicator.CircularProgressIndicator
    private lateinit var tvLoadingStatus: TextView

    // 检测设置对话框组件
    private var detectionSettingsDialog: AlertDialog? = null

    // 数据
    private var currentMeasureLog: MeasureLog? = null
    private var originalBitmap: Bitmap? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_measure_result)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        // 获取传递的参数
        getIntentData()

        // 初始化组件
        initializeComponents()
        initializeUI()

        // 开始分析流程
        startAnalysisProcess()

        Log.d(TAG, "MeasureResultActivity 创建完成，MeasureLog ID: $measureLogId, 图片路径: $imagePath")
    }

    /**
     * 获取Intent传递的数据
     */
    private fun getIntentData() {
        measureLogId = intent.getLongExtra(EXTRA_MEASURE_LOG_ID, -1)
        imagePath = intent.getStringExtra(EXTRA_IMAGE_PATH)

        Log.d(TAG, "获取Intent数据: measureLogId=$measureLogId, imagePath=$imagePath")

        if (measureLogId == -1L) {
            Log.e(TAG, "未获取到有效的 MeasureLog ID")
            finish()
            return
        }

        if (imagePath.isNullOrEmpty()) {
            Log.e(TAG, "未获取到有效的图片路径")
            finish()
            return
        }
    }

    /**
     * 初始化组件
     */
    private fun initializeComponents() {
        measureLogManager = MeasureLogManager.getInstance(this)
        onnxModelManager = OnnxModelManager.getInstance(this)
        yoloDetector = YoloV8Detector.getInstance(this)

        Log.d(TAG, "组件初始化完成")
    }

    /**
     * 初始化UI
     */
    private fun initializeUI() {
        // 初始化UI组件
        toolbar = findViewById(R.id.measure_result_toolbar)
        imageResult = findViewById(R.id.imageResult)
        progressAnalysis = findViewById(R.id.progress_analysis)
        tvAnalysisStatus = findViewById(R.id.tv_analysis_status)
        cardAnalysisStatus = findViewById(R.id.card_analysis_status)
        cardCountResult = findViewById(R.id.card_count_result)
        cardErrorInfo = findViewById(R.id.card_error_info)
        tvCountResult = findViewById(R.id.tv_count_result)
        tvAnalysisTime = findViewById(R.id.tv_analysis_time)
        tvErrorMessage = findViewById(R.id.tv_error_message)
        btnRetry = findViewById(R.id.btn_retry)

        // 检测结果卡片内的组件
        layoutLoadingState = findViewById(R.id.layout_loading_state)
        layoutResultState = findViewById(R.id.layout_result_state)
        progressDetection = findViewById(R.id.progress_detection)
        tvLoadingStatus = findViewById(R.id.tv_loading_status)

        // 设置Toolbar
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        toolbar.setNavigationOnClickListener { finish() }

        // 设置重试按钮点击事件
        btnRetry.setOnClickListener {
            hideErrorCard()
            startAnalysisProcess()
        }

        // 设置图片长宽比适配
        setupImageAspectRatio()

        Log.d(TAG, "UI初始化完成")
    }

    /**
     * 显示检测加载状态
     */
    private fun showDetectionLoading(status: String) {
        runOnUiThread {
            // 显示检测结果卡片
            cardCountResult.visibility = View.VISIBLE

            // 显示加载状态，隐藏结果状态
            layoutLoadingState.visibility = View.VISIBLE
            layoutResultState.visibility = View.GONE

            // 更新加载状态文本
            tvLoadingStatus.text = status

            // 启动加载动画
            progressDetection.visibility = View.VISIBLE

            Log.d(TAG, "显示检测加载状态: $status")
        }
    }

    /**
     * 显示检测结果
     */
    private fun showDetectionResult(count: Int, analysisTime: Long) {
        runOnUiThread {
            // 隐藏加载状态，显示结果状态
            layoutLoadingState.visibility = View.GONE
            layoutResultState.visibility = View.VISIBLE

            // 设置结果数据
            tvCountResult.text = count.toString()
            tvAnalysisTime.text = "分析耗时：${analysisTime / 1000.0}秒"

            // 添加结果显示的动画效果
            layoutResultState.alpha = 0f
            layoutResultState.animate()
                .alpha(1f)
                .setDuration(300)
                .start()

            Log.d(TAG, "显示检测结果: $count 个，耗时 ${analysisTime}ms")
        }
    }

    /**
     * 隐藏检测结果卡片
     */
    private fun hideDetectionResult() {
        runOnUiThread {
            cardCountResult.visibility = View.GONE
            Log.d(TAG, "隐藏检测结果卡片")
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_measure_result, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_detection_settings -> {
                showDetectionSettingsDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * 开始分析流程
     */
    private fun startAnalysisProcess() {
        Log.d(TAG, "开始分析流程")

        lifecycleScope.launch {
            try {
                // 步骤1：加载MeasureLog数据
                updateStatus("正在加载测量记录...")
                loadMeasureLogData()

                // 步骤2：加载和显示图片
                updateStatus("正在加载图片...")
                loadAndDisplayImage()

                // 步骤3：检查和下载模型文件
                updateStatus("正在检查模型文件...")
                showDetectionLoading("正在检查模型文件...")
                ensureModelFileReady()

                // 步骤4：初始化ONNX模型
                updateStatus("正在初始化模型...")
                showDetectionLoading("正在初始化模型...")
                initializeOnnxModel()

                // 步骤5：执行目标检测
                updateStatus("正在执行目标检测...")
                showDetectionLoading("正在执行目标检测...")
                performObjectDetection()

            } catch (e: Exception) {
                Log.e(TAG, "分析流程失败: ${e.message}", e)
                showError("分析失败: ${e.message}")
            }
        }
    }

    /**
     * 加载MeasureLog数据
     */
    private suspend fun loadMeasureLogData() {
        currentMeasureLog = measureLogManager.getMeasureLogById(measureLogId)

        if (currentMeasureLog == null) {
            throw Exception("无法找到测量记录")
        }

        // 更新Toolbar标题显示模型信息
        runOnUiThread {
            val model = currentMeasureLog!!.onnxModelJson
            toolbar.title = model.name
            toolbar.subtitle = "${model.shrimpCate.name} • 版本 ${model.version}"
        }

        Log.d(TAG, "MeasureLog数据加载完成: ${currentMeasureLog!!.getModelName()}")
    }

    /**
     * 加载和显示图片
     */
    private suspend fun loadAndDisplayImage() {
        val imageFile = File(imagePath!!)
        if (!imageFile.exists()) {
            throw Exception("图片文件不存在: $imagePath")
        }

        // 加载图片并修正旋转方向
        originalBitmap = loadAndCorrectBitmap(imageFile)
        if (originalBitmap == null) {
            throw Exception("无法加载图片文件")
        }

        // 显示图片
        runOnUiThread {
            imageResult.setImageBitmap(originalBitmap)
        }

        Log.d(TAG, "图片加载完成: ${originalBitmap!!.width}x${originalBitmap!!.height}")
    }

    /**
     * 加载图片并修正旋转方向
     */
    private fun loadAndCorrectBitmap(photoFile: File): Bitmap? {
        return try {
            // 首先获取图片的EXIF信息
            val exif = ExifInterface(photoFile.absolutePath)
            val orientation = exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )

            // 加载原始图片
            val originalBitmap = BitmapFactory.decodeFile(photoFile.absolutePath)
            if (originalBitmap == null) {
                Log.e(TAG, "无法解码图片文件: ${photoFile.absolutePath}")
                return null
            }

            // 根据EXIF信息计算需要旋转的角度
            val rotationAngle = when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> 90f
                ExifInterface.ORIENTATION_ROTATE_180 -> 180f
                ExifInterface.ORIENTATION_ROTATE_270 -> 270f
                ExifInterface.ORIENTATION_FLIP_HORIZONTAL -> 0f // 水平翻转，暂不处理
                ExifInterface.ORIENTATION_FLIP_VERTICAL -> 0f // 垂直翻转，暂不处理
                ExifInterface.ORIENTATION_TRANSPOSE -> 0f // 复杂变换，暂不处理
                ExifInterface.ORIENTATION_TRANSVERSE -> 0f // 复杂变换，暂不处理
                else -> 0f // ORIENTATION_NORMAL 或未知
            }

            // 如果不需要旋转，直接返回原图
            if (rotationAngle == 0f) {
                Log.d(TAG, "图片无需旋转，EXIF方向: $orientation")
                return originalBitmap
            }

            // 创建旋转矩阵
            val matrix = Matrix()
            matrix.postRotate(rotationAngle)

            // 应用旋转
            val rotatedBitmap = Bitmap.createBitmap(
                originalBitmap,
                0,
                0,
                originalBitmap.width,
                originalBitmap.height,
                matrix,
                true
            )

            // 释放原始图片内存
            if (rotatedBitmap != originalBitmap) {
                originalBitmap.recycle()
            }

            Log.d(TAG, "图片旋转完成: ${rotationAngle}度, EXIF方向: $orientation")
            rotatedBitmap

        } catch (e: Exception) {
            Log.e(TAG, "处理图片旋转失败: ${e.message}", e)
            // 如果处理失败，尝试直接加载原图
            BitmapFactory.decodeFile(photoFile.absolutePath)
        }
    }

    /**
     * 确保模型文件准备就绪
     */
    private suspend fun ensureModelFileReady() {
        val model = currentMeasureLog!!.onnxModelJson

        // 检查模型文件是否存在
        if (!onnxModelManager.isModelFileExists(model)) {
            Log.d(TAG, "模型文件不存在，开始下载: ${model.name}")

            // 下载模型文件
            val downloadResult = onnxModelManager.downloadModelFile(model) { progress ->
                runOnUiThread {
                    updateStatus("正在下载模型文件... $progress%")
                    showDetectionLoading("正在下载模型文件... $progress%")
                }
            }

            if (downloadResult.isFailure) {
                throw Exception("模型文件下载失败: ${downloadResult.exceptionOrNull()?.message}")
            }

            Log.d(TAG, "模型文件下载完成: ${downloadResult.getOrNull()}")
        }

        // 验证模型文件
        if (!onnxModelManager.validateModelFile(model)) {
            throw Exception("模型文件验证失败")
        }

        Log.d(TAG, "模型文件准备就绪")
    }

    /**
     * 初始化ONNX模型
     */
    private suspend fun initializeOnnxModel() {
        val model = currentMeasureLog!!.onnxModelJson
        val modelPath = onnxModelManager.getModelFilePath(model)

        val initResult = yoloDetector.initializeModel(modelPath, model.classes)
        if (initResult.isFailure) {
            throw Exception("ONNX模型初始化失败: ${initResult.exceptionOrNull()?.message}")
        }

        Log.d(TAG, "ONNX模型初始化完成")
        Log.d(TAG, "检测器配置信息:\n${yoloDetector.getDetectorInfo()}")

        // 测试坐标转换逻辑
        if (originalBitmap != null) {
            yoloDetector.testCoordinateTransform(originalBitmap!!.width, originalBitmap!!.height)
        }

        // 执行测试推理
        updateStatus("正在测试模型...")
        val testResult = yoloDetector.testInference()
        if (testResult.isSuccess) {
            Log.d(TAG, "模型测试成功: ${testResult.getOrNull()}")
        } else {
            Log.w(TAG, "模型测试失败: ${testResult.exceptionOrNull()?.message}")
        }
    }

    /**
     * 执行目标检测
     */
    private suspend fun performObjectDetection() {
        if (originalBitmap == null) {
            throw Exception("原始图片为空")
        }

        val startTime = System.currentTimeMillis()

        // 执行检测
        val detectionResult = yoloDetector.detectObjects(originalBitmap!!)
        if (detectionResult.isFailure) {
            throw Exception("目标检测失败: ${detectionResult.exceptionOrNull()?.message}")
        }

        val analysisResult = detectionResult.getOrNull()!!
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime

        Log.d(TAG, "目标检测完成: 检测到${analysisResult.count}个对象, 总耗时${totalTime}ms")

        // 保存结果到数据库
        saveAnalysisResult(analysisResult, totalTime)

        // 显示结果
        showAnalysisResult(analysisResult, totalTime)
    }

    /**
     * 保存分析结果到数据库
     */
    private suspend fun saveAnalysisResult(result: YoloV8Detector.AnalysisResult, totalTime: Long) {
        try {
            // 保存处理后的图片
            val destImagePath = saveProcessedImage(result.processedBitmap)

            // 获取当前检测器的阈值设置
            val currentConf = yoloDetector.getConfidenceThreshold()
            val currentNms = yoloDetector.getNmsThreshold()

            // 计算检测框的尺寸数据
            val dimensionLengths = calculateDimensionLengths(result.detections)
            val diagonalLengths = calculateDiagonalLengths(result.detections)

            // 更新MeasureLog（包含阈值信息和尺寸数据）
            val success = measureLogManager.completeMeasureWithDimensions(
                id = measureLogId,
                destImg = destImagePath,
                count = result.count,
                conf = currentConf,
                nms = currentNms,
                dimensionLengths = dimensionLengths,
                diagonalLengths = diagonalLengths
            )

            if (success) {
                Log.d(TAG, "分析结果保存成功，置信度阈值: $currentConf, NMS阈值: $currentNms")
                Log.d(TAG, "检测框尺寸数据: ${dimensionLengths?.contentToString()}")
                Log.d(TAG, "对角线长度数据: ${diagonalLengths?.contentToString()}")
            } else {
                Log.w(TAG, "分析结果保存失败")
            }

        } catch (e: Exception) {
            Log.e(TAG, "保存分析结果失败: ${e.message}", e)
        }
    }

    /**
     * 计算检测框的长宽尺寸
     *
     * @param detections 检测结果列表
     * @return FloatArray，格式为 [width1, height1, width2, height2, ...]
     */
    private fun calculateDimensionLengths(detections: List<YoloV8Detector.Detection>): FloatArray? {
        if (detections.isEmpty()) {
            Log.d(TAG, "没有检测结果，跳过尺寸计算")
            return null
        }

        val dimensions = mutableListOf<Float>()

        detections.forEachIndexed { index, detection ->
            val bbox = detection.bbox
            val width = bbox.width()
            val height = bbox.height()

            dimensions.add(width)
            dimensions.add(height)

            Log.d(TAG, "检测框[$index] 尺寸: 宽度=${String.format("%.2f", width)}, 高度=${String.format("%.2f", height)}")
        }

        Log.d(TAG, "计算完成，共${detections.size}个检测框的尺寸数据")
        return dimensions.toFloatArray()
    }

    /**
     * 计算检测框的对角线长度
     *
     * @param detections 检测结果列表
     * @return FloatArray，格式为 [diagonal1, diagonal2, diagonal3, ...]
     */
    private fun calculateDiagonalLengths(detections: List<YoloV8Detector.Detection>): FloatArray? {
        if (detections.isEmpty()) {
            Log.d(TAG, "没有检测结果，跳过对角线计算")
            return null
        }

        val diagonals = mutableListOf<Float>()

        detections.forEachIndexed { index, detection ->
            val bbox = detection.bbox
            val width = bbox.width()
            val height = bbox.height()

            // 使用勾股定理计算对角线长度：√(width² + height²)
            val diagonal = kotlin.math.sqrt(width * width + height * height)

            diagonals.add(diagonal)

            Log.d(TAG, "检测框[$index] 对角线长度: ${String.format("%.2f", diagonal)} (基于 ${String.format("%.2f", width)}×${String.format("%.2f", height)})")
        }

        Log.d(TAG, "计算完成，共${detections.size}个检测框的对角线数据")
        return diagonals.toFloatArray()
    }

    /**
     * 保存处理后的图片
     */
    private fun saveProcessedImage(bitmap: Bitmap): String {
        val fileName = "measure_result_${System.currentTimeMillis()}.jpg"
        val file = File(filesDir, fileName)

        FileOutputStream(file).use { out ->
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
        }

        Log.d(TAG, "处理后的图片保存完成: ${file.absolutePath}")
        return file.absolutePath
    }

    /**
     * 显示分析结果
     */
    private fun showAnalysisResult(result: YoloV8Detector.AnalysisResult, totalTime: Long) {
        runOnUiThread {
            // 隐藏状态卡片
            cardAnalysisStatus.visibility = View.GONE

            // 显示处理后的图片
            imageResult.setImageBitmap(result.processedBitmap)

            // 显示检测结果
            showDetectionResult(result.count, totalTime)

            Log.d(TAG, "分析结果显示完成")
        }
    }

    /**
     * 显示检测设置对话框
     */
    private fun showDetectionSettingsDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_detection_settings, null)

        // 获取对话框中的UI组件
        val seekbarConfidence = dialogView.findViewById<SeekBar>(R.id.seekbar_confidence)
        val seekbarNms = dialogView.findViewById<SeekBar>(R.id.seekbar_nms)
        val tvConfidenceValue = dialogView.findViewById<TextView>(R.id.tv_confidence_value)
        val tvNmsValue = dialogView.findViewById<TextView>(R.id.tv_nms_value)
        val btnCancel = dialogView.findViewById<MaterialButton>(R.id.btn_cancel)
        val btnApply = dialogView.findViewById<MaterialButton>(R.id.btn_apply)

        // 初始化当前阈值
        val currentConfThreshold = yoloDetector.getConfidenceThreshold()
        val currentNmsThreshold = yoloDetector.getNmsThreshold()

        seekbarConfidence.progress = (currentConfThreshold * 100).toInt()
        seekbarNms.progress = (currentNmsThreshold * 100).toInt()
        tvConfidenceValue.text = String.format("%.2f", currentConfThreshold)
        tvNmsValue.text = String.format("%.2f", currentNmsThreshold)

        // 设置SeekBar监听器
        seekbarConfidence.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val threshold = progress / 100.0f
                    tvConfidenceValue.text = String.format("%.2f", threshold)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        seekbarNms.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val threshold = progress / 100.0f
                    tvNmsValue.text = String.format("%.2f", threshold)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 创建对话框
        detectionSettingsDialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // 设置按钮点击事件
        btnCancel.setOnClickListener {
            detectionSettingsDialog?.dismiss()
        }

        btnApply.setOnClickListener {
            val newConfThreshold = seekbarConfidence.progress / 100.0f
            val newNmsThreshold = seekbarNms.progress / 100.0f

            // 应用新阈值
            yoloDetector.setConfidenceThreshold(newConfThreshold)
            yoloDetector.setNmsThreshold(newNmsThreshold)

            Log.d(TAG, "应用新阈值: conf=$newConfThreshold, nms=$newNmsThreshold")

            // 关闭对话框
            detectionSettingsDialog?.dismiss()

            // 重新检测
            redetectWithNewThresholds()
        }

        detectionSettingsDialog?.show()
    }

    /**
     * 使用新阈值重新检测
     */
    private fun redetectWithNewThresholds() {
        if (originalBitmap == null) {
            Log.w(TAG, "原始图片为空，无法重新检测")
            return
        }

        lifecycleScope.launch {
            try {
                // 显示重新检测状态
                runOnUiThread {
                    cardAnalysisStatus.visibility = View.VISIBLE
                    updateStatus("正在使用新阈值重新检测...")
                }

                // 显示检测加载状态
                showDetectionLoading("正在使用新阈值重新检测...")

                // 记录重新检测前的阈值
                Log.d(TAG, "重新检测开始 - 当前阈值: conf=${yoloDetector.getConfidenceThreshold()}, nms=${yoloDetector.getNmsThreshold()}")
                Log.d(TAG, "原始图片信息: ${originalBitmap!!.width}x${originalBitmap!!.height}")

                // 验证检测器状态
                if (!yoloDetector.validateDetectorState()) {
                    throw Exception("检测器状态无效，无法进行重新检测")
                }

                // 执行检测
                val detectionResult = yoloDetector.detectObjects(originalBitmap!!)
                if (detectionResult.isFailure) {
                    throw Exception("重新检测失败: ${detectionResult.exceptionOrNull()?.message}")
                }

                val analysisResult = detectionResult.getOrNull()!!

                Log.d(TAG, "重新检测完成: 检测到${analysisResult.count}个对象")

                // 显示新结果
                showAnalysisResult(analysisResult, analysisResult.inferenceTime)

            } catch (e: Exception) {
                Log.e(TAG, "重新检测失败: ${e.message}", e)
                runOnUiThread {
                    showError("重新检测失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 更新状态显示
     */
    private fun updateStatus(status: String) {
        runOnUiThread {
            tvAnalysisStatus.text = status
            Log.d(TAG, "状态更新: $status")
        }
    }

    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        runOnUiThread {
            // 隐藏状态卡片
            cardAnalysisStatus.visibility = View.GONE

            // 隐藏检测结果卡片
            hideDetectionResult()

            // 显示错误卡片
            tvErrorMessage.text = message
            cardErrorInfo.visibility = View.VISIBLE

            Log.e(TAG, "显示错误: $message")
        }
    }

    /**
     * 隐藏错误卡片
     */
    private fun hideErrorCard() {
        cardErrorInfo.visibility = View.GONE
        hideDetectionResult()
        cardAnalysisStatus.visibility = View.VISIBLE
    }



    /**
     * 设置图片长宽比适配
     */
    private fun setupImageAspectRatio() {
        // 当图片加载完成后，动态调整ImageView的长宽比
        imageResult.viewTreeObserver.addOnGlobalLayoutListener {
            val drawable = imageResult.drawable
            if (drawable != null) {
                val imageWidth = drawable.intrinsicWidth
                val imageHeight = drawable.intrinsicHeight
                if (imageWidth > 0 && imageHeight > 0) {
                    val aspectRatio = imageWidth.toFloat() / imageHeight.toFloat()

                    // 更新ConstraintLayout的dimensionRatio
                    val layoutParams = imageResult.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
                    layoutParams.dimensionRatio = "H,$aspectRatio:1"
                    imageResult.layoutParams = layoutParams

                    Log.d(TAG, "图片长宽比设置完成: $aspectRatio ($imageWidth x $imageHeight)")
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 清理对话框
        detectionSettingsDialog?.dismiss()
        detectionSettingsDialog = null

        // 清理ONNX资源
        yoloDetector.cleanup()

        // 清理Bitmap资源
        originalBitmap?.recycle()
        originalBitmap = null

        Log.d(TAG, "资源清理完成")
    }
}