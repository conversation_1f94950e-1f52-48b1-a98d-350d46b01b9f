package com.zhmiaobang.easydianapp.module.miacochang.preseed

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityGetPreSeedQrCodeBinding
import com.zhmiaobang.easydianapp.json.preseed.PreSeedJson
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.viewmodel.preseed.PreSeedViewModel
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * 预播种二维码显示Activity
 *
 * 功能特性：
 * - 显示苗场名称和有效期
 * - 显示微信二维码
 * - 提供使用说明
 * - 完整的错误处理和加载状态
 *
 * <AUTHOR> 4.0 sonnet
 */
class GetPreSeedQrCodeActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "GetPreSeedQrCodeActivity"
    }

    private lateinit var binding: ActivityGetPreSeedQrCodeBinding
    private val viewModel: PreSeedViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 初始化ViewBinding
        binding = ActivityGetPreSeedQrCodeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 设置系统栏适配
        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        // 初始化UI
        setupUI()

        // 设置观察者
        setupObservers()

        // 加载数据
        loadQrCodeData()
    }

    /**
     * 初始化UI
     */
    private fun setupUI() {
        // 设置Toolbar
        binding.preseedQrcodeToolbar.setNavigationOnClickListener {
            finish()
        }

        // 设置重试按钮
        binding.preseedQrcodeBtnRetry.setOnClickListener {
            loadQrCodeData()
        }

        Log.d(TAG, "UI初始化完成")
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察二维码数据
        viewModel.qrCodeObserver.observe(this) { response ->
            try {
                when (response.code) {
                    200 -> {
                        Log.d(TAG, "二维码数据加载成功")
                        showQrCodeData(response.results)
                        showLoadingState(false)
                        showErrorState(false)
                    }
                    else -> {
                        Log.w(TAG, "二维码数据加载失败: ${response.msg}")
                        showErrorState(true, response.msg)
                        showLoadingState(false)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理二维码数据异常: ${e.message}", e)
                val errorMessage = ExceptionUtil.catchException(e)
                showErrorState(true, errorMessage)
                showLoadingState(false)
            }
        }

        viewModel.completeObserver.observe(this) {

        }
        viewModel.errorObserver.observe(this){
            
        }
        Log.d(TAG, "观察者设置完成")
    }

    /**
     * 加载二维码数据
     */
    private fun loadQrCodeData() {
        Log.d(TAG, "开始加载二维码数据")
        showLoadingState(true)
        showErrorState(false)

        lifecycleScope.launch {
            try {
                viewModel.get_preseed_qrcode()
            } catch (e: Exception) {
                Log.e(TAG, "加载二维码数据异常: ${e.message}", e)
                val errorMessage = ExceptionUtil.catchException(e)
                showErrorState(true, errorMessage)
                showLoadingState(false)
            }
        }
    }

    /**
     * 显示二维码数据
     */
    private fun showQrCodeData(preSeedData: PreSeedJson) {
        try {
            Log.d(TAG, "显示二维码数据: 苗场=${preSeedData.miaochang.name}, 有效期=${preSeedData.expiredTime}")

            // 显示苗场名称
            binding.preseedQrcodeTvMiaochangName.text = preSeedData.miaochang.name

            // 显示有效期
            val expiredTime = formatDateTime(preSeedData.expiredTime)
            binding.preseedQrcodeTvExpiredTime.text = expiredTime

            // 加载微信二维码
            loadWechatQrCode(preSeedData.wechatQrCode)

            Log.d(TAG, "二维码数据显示完成")

        } catch (e: Exception) {
            Log.e(TAG, "显示二维码数据异常: ${e.message}", e)
            showErrorState(true, "数据显示失败")
        }
    }

    /**
     * 加载微信二维码图片
     */
    private fun loadWechatQrCode(qrCodeUrl: String) {
        Log.d(TAG, "开始加载微信二维码: ${qrCodeUrl.take(50)}...")

        if (qrCodeUrl.isBlank()) {
            Log.w(TAG, "二维码URL为空")
            binding.preseedQrcodeIvQrCode.setImageResource(R.drawable.ic_qr_code)
            return
        }

        // 使用Coil加载二维码图片
        binding.preseedQrcodeIvQrCode.load(qrCodeUrl) {
            crossfade(300)
            placeholder(R.drawable.ic_qr_code)
            error(R.drawable.ic_qr_code)
        }

        Log.d(TAG, "微信二维码加载完成")
    }

    /**
     * 显示加载状态
     */
    private fun showLoadingState(isLoading: Boolean) {
        // 控制顶部进度条
        binding.preseedQrcodeProgressBar.visibility = if (isLoading) View.VISIBLE else View.GONE

        // 控制主要内容区域
        binding.preseedQrcodeScrollView.visibility = if (isLoading) View.GONE else View.VISIBLE

        // 控制二维码区域内的小进度指示器
        binding.preseedQrcodeProgressLoading.visibility = if (isLoading) View.VISIBLE else View.GONE

        Log.d(TAG, "加载状态更新: isLoading=$isLoading")
    }

    /**
     * 显示错误状态
     */
    private fun showErrorState(isError: Boolean, errorMessage: String = "加载失败，请重试") {
        if (isError) {
            // 隐藏进度条
            binding.preseedQrcodeProgressBar.visibility = View.GONE
            // 显示主要内容区域以显示错误卡片
            binding.preseedQrcodeScrollView.visibility = View.VISIBLE
            // 显示错误卡片
            binding.preseedQrcodeCardError.visibility = View.VISIBLE
            // 隐藏其他内容卡片
            binding.preseedQrcodeCardInfo.visibility = View.GONE
            binding.preseedQrcodeCardQr.visibility = View.GONE
            binding.preseedQrcodeCardInstructions.visibility = View.GONE
            // 设置错误消息
            binding.preseedQrcodeTvErrorMessage.text = errorMessage
        } else {
            // 隐藏错误卡片
            binding.preseedQrcodeCardError.visibility = View.GONE
            // 显示其他内容卡片
            binding.preseedQrcodeCardInfo.visibility = View.VISIBLE
            binding.preseedQrcodeCardQr.visibility = View.VISIBLE
            binding.preseedQrcodeCardInstructions.visibility = View.VISIBLE
        }

        Log.d(TAG, "错误状态更新: isError=$isError, message=$errorMessage")
    }

    /**
     * 格式化日期时间
     */
    private fun formatDateTime(dateTimeString: String?): String {
        return try {
            if (dateTimeString.isNullOrBlank()) {
                "未设置"
            } else {
                // 尝试解析ISO格式的日期时间
                val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", Locale.getDefault())
                val outputFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

                val date = inputFormat.parse(dateTimeString)
                date?.let { outputFormat.format(it) } ?: dateTimeString
            }
        } catch (e: Exception) {
            Log.w(TAG, "日期格式化失败: $dateTimeString, 错误: ${e.message}")
            // 如果解析失败，尝试其他格式或直接返回原字符串
            dateTimeString ?: "未知"
        }
    }
}