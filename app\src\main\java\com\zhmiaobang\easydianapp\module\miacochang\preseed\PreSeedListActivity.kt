package com.zhmiaobang.easydianapp.module.miacochang.preseed

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.adapter.PreSeedAdapter
import com.zhmiaobang.easydianapp.databinding.ActivityPreSeedListBinding
import com.zhmiaobang.easydianapp.json.preseed.PreSeedJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.utils.PaginationScrollListener
import com.zhmiaobang.easydianapp.viewmodel.preseed.PreSeedViewModel

/**
 * 预播种列表页面
 *
 * 功能特性：
 * - 展示预播种列表（头像、名称、数量、创建时间）
 * - 支持分页加载
 * - 支持下拉刷新
 * - 完整的加载状态管理
 * - Material Design 风格
 *
 * <AUTHOR> 4.0 sonnet
 */
class PreSeedListActivity : BaseActivity() {

    companion object {
        private const val TAG = "PreSeedListActivity"
    }

    private lateinit var binding: ActivityPreSeedListBinding
    private lateinit var preSeedAdapter: PreSeedAdapter
    private val viewModel: PreSeedViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        binding = ActivityPreSeedListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        setupToolbar()
        setupRecyclerView()
        setupSwipeRefresh()
        setupFab()
        setupObservers()

        // 初始加载数据
        viewModel.loadFirstPage()
    }

    /**
     * 设置工具栏
     */
    private fun setupToolbar() {
        setSupportActionBar(binding.preSeedToolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
        }

        binding.preSeedToolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        preSeedAdapter = PreSeedAdapter()
        val layoutManager = LinearLayoutManager(this)

        binding.preSeedRecyclerView.apply {
            this.layoutManager = layoutManager
            adapter = preSeedAdapter
            setHasFixedSize(true)
        }

        // 设置点击事件监听器
        preSeedAdapter.setOnItemClickListener(object : PreSeedAdapter.OnItemClickListener {
            override fun onItemClick(preSeed: PreSeedJson) {
                Log.d(TAG, "点击预播种: ${preSeed.user.nickname}")
                // TODO: 跳转到详情页面
                showToast("点击了预播种: ${preSeed.user.nickname}")
            }

            override fun onRetryClick() {
                Log.d(TAG, "点击重试按钮")
                viewModel.retryLoad()
            }
        })

        // 设置分页滚动监听器
        val scrollListener = object : PaginationScrollListener(layoutManager) {
            override fun isLoading(): Boolean {
                return viewModel.getCurrentPaginationState().isLoading
            }

            override fun isLastPage(): Boolean {
                return !viewModel.getCurrentPaginationState().hasMoreData
            }

            override fun loadMoreItems() {
                Log.d(TAG, "滚动触发加载更多")
                viewModel.loadNextPage()
            }
        }

        binding.preSeedRecyclerView.addOnScrollListener(scrollListener)
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.preSeedSwipeRefreshLayout.setOnRefreshListener {
            Log.d(TAG, "下拉刷新触发")
            viewModel.refresh()
        }

        // 设置刷新指示器颜色
        binding.preSeedSwipeRefreshLayout.setColorSchemeResources(
            R.color.primary_color,
            R.color.secondary_color
        )
    }

    /**
     * 设置FAB按钮
     */
    private fun setupFab() {
        binding.preSeedFabQrCode.setOnClickListener {
            Log.d(TAG, "点击预播种二维码FAB按钮")
            navigateToQrCodeActivity()
        }
    }

    /**
     * 跳转到预播种二维码显示页面
     */
    private fun navigateToQrCodeActivity() {
        try {
            val intent = Intent(this, GetPreSeedQrCodeActivity::class.java)
            startActivity(intent)
            Log.d(TAG, "成功跳转到预播种二维码显示页面")
        } catch (e: Exception) {
            Log.e(TAG, "跳转到预播种二维码显示页面失败: ${e.message}", e)
            showToast("页面跳转失败")
        }
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        // 观察分页数据
        viewModel.paginatedDataObserver.observe(this) { paginatedData ->
            Log.d(TAG, "收到分页数据更新: items=${paginatedData.items.size}, state=${paginatedData.paginationState.loadingState}")

            try {
                val items = paginatedData.items
                val state = paginatedData.paginationState

                // 停止下拉刷新动画
                binding.preSeedSwipeRefreshLayout.isRefreshing = false

                // 根据加载状态更新UI
                when (state.loadingState) {
                    LoadingState.LOADING_FIRST -> {
                        // 首次加载，显示主加载指示器
                        showLoading()
                    }
                    LoadingState.LOADING_MORE -> {
                        // 加载更多，在adapter中显示
                        hideLoading()
                        preSeedAdapter.showLoadingMore()
                    }
                    LoadingState.ERROR -> {
                        // 加载错误
                        hideLoading()
                        if (items.isEmpty()) {
                            showError(state.errorMessage ?: "加载失败")
                        } else {
                            preSeedAdapter.showLoadError(state.errorMessage ?: "加载失败")
                        }
                    }
                    LoadingState.NO_MORE -> {
                        // 没有更多数据
                        hideLoading()
                        preSeedAdapter.showNoMoreData()
                    }
                    LoadingState.IDLE -> {
                        // 加载完成
                        hideLoading()
                        preSeedAdapter.hideLoadingState()
                    }
                }

                // 更新数据
                if (state.currentPage <= 2) {
                    // 首页或刷新，替换所有数据
                    preSeedAdapter.updateData(items)
                } else {
                    // 分页加载，追加数据
                    val newItems = items.drop(preSeedAdapter.itemCount - if (state.loadingState != LoadingState.IDLE) 1 else 0)
                    if (newItems.isNotEmpty()) {
                        preSeedAdapter.addData(newItems)
                    }
                }

                // 显示空状态
                showEmptyStateIfNeeded(items.isEmpty() && state.loadingState == LoadingState.IDLE)

            } catch (e: Exception) {
                Log.e(TAG, "处理分页数据时出错: ${e.message}", e)
                hideLoading()
                showError("数据处理失败")
            }
        }

        // 观察错误
        viewModel.errorObserver.observe(this) { errorMessage ->
            Log.e(TAG, "ViewModel错误: $errorMessage")
            hideLoading()
            binding.preSeedSwipeRefreshLayout.isRefreshing = false
            showError(errorMessage)
        }
    }

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.preSeedProgressIndicator.visibility = View.VISIBLE
        binding.preSeedEmptyStateLayout.visibility = View.GONE
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.preSeedProgressIndicator.visibility = View.GONE
    }

    /**
     * 显示错误状态
     */
    private fun showError(message: String) {
        showToast(message)
        binding.preSeedEmptyStateLayout.visibility = View.GONE
    }

    /**
     * 显示空状态
     */
    private fun showEmptyStateIfNeeded(isEmpty: Boolean) {
        binding.preSeedEmptyStateLayout.visibility = if (isEmpty) View.VISIBLE else View.GONE
    }

    /**
     * 显示Toast消息
     */

}