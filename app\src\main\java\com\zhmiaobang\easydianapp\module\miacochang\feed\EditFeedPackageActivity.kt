package com.zhmiaobang.easydianapp.module.miacochang.feed

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Base64
import android.util.Log
import android.view.View
import android.widget.ArrayAdapter
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityEditFeedPackageBinding
import com.zhmiaobang.easydianapp.json.feed.FeedPackageJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.viewmodel.feed.FeedPackageViewModel
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.RoundedCornersTransformation
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream

/**
 * 饲料包编辑Activity
 *
 * 功能特性：
 * - 新建和编辑饲料包信息
 * - 表单验证和数据保存
 * - 封面图片选择和上传
 * - 支持拍照和相册选择
 * - 完整的错误处理和用户反馈
 * - 限制只能编辑 cover, name, status 字段
 *
 * <AUTHOR> 4.0 sonnet
 */
class EditFeedPackageActivity : BaseActivity() {

    companion object {
        private const val TAG = "EditFeedPackageActivity"
        private const val EXTRA_FEED_PACKAGE = "feed_package"
        private const val IMAGE_QUALITY = 80
        private const val MAX_BASE64_SIZE = 512 * 1024 // 512KB
        private val SUPPORTED_IMAGE_TYPES = arrayOf("image/jpeg", "image/png")
    }

    // ViewBinding
    private val binding: ActivityEditFeedPackageBinding by lazy {
        ActivityEditFeedPackageBinding.inflate(layoutInflater)
    }

    // ViewModel
    private val viewModel: FeedPackageViewModel by viewModels()

    // 数据
    private var feedPackage: FeedPackageJson? = null
    private var isEditMode: Boolean = false
    private var selectedImageBase64: String? = null

    // 状态选项
    private val statusOptions = arrayOf("有效", "无效")
    private val statusValues = arrayOf(1, 0)

    // 图片选择器
    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { handleImageSelection(it) }
    }

    // 相机拍照
    private val cameraLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicturePreview()
    ) { bitmap: Bitmap? ->
        bitmap?.let { handleCameraCapture(it) }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initializeUI()
        setupObservers()

        Log.d(TAG, "EditFeedPackageActivity创建完成")
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()

        // 获取传入的数据
        feedPackage = intent.getParcelableExtra(EXTRA_FEED_PACKAGE)
        isEditMode = feedPackage != null

        setupToolbar()
        setupStatusDropdown()
        setupImagePickers()
        setupClickListeners()

        // 如果是编辑模式，绑定数据
        if (isEditMode) {
            bindDataToUI()
        }
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()

        // 观察编辑结果
        viewModel.packageEditObserver.observe(this) { response ->
            hideLoading()

            try {
                when (response.code) {
                    200 -> {
                        Log.d(TAG, "饲料包保存成功")
                        showToast("保存成功")

                        // 返回上一页
                        setResult(Activity.RESULT_OK)
                        finish()
                    }
                    else -> {
                        Log.w(TAG, "饲料包保存失败: ${response.msg}")
                        showToast("保存失败: ${response.msg}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理保存结果失败: ${e.message}", e)
                showToast("保存失败，请重试")
            }
        }
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        val title = if (isEditMode) "编辑饲料包" else "新建饲料包"
        initToolbar(
            toolbar = binding.edFeedpackageToolbar,
            title = title,
            showBack = true
        )
    }

    /**
     * 设置状态下拉选择
     */
    private fun setupStatusDropdown() {
        val adapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, statusOptions)
        binding.edFeedpackageActStatus.setAdapter(adapter)

        // 设置默认选择
        if (!isEditMode) {
            binding.edFeedpackageActStatus.setText(statusOptions[0], false) // 默认选择"有效"
        }
    }

    /**
     * 设置图片选择器
     */
    private fun setupImagePickers() {
        // 图片选择按钮点击事件
        binding.edFeedpackageBtnSelectCover.setOnClickListener {
            showImagePickerDialog()
        }

        // 图片点击事件（也可以选择图片）
        binding.edFeedpackageIvCover.setOnClickListener {
            showImagePickerDialog()
        }
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 保存按钮
        binding.edFeedpackageBtnSave.setOnClickListener {
            saveFeedPackage()
        }
    }

    /**
     * 绑定数据到UI
     */
    private fun bindDataToUI() {
        feedPackage?.let { pkg ->
            // 基本信息
            binding.edFeedpackageEtNo.setText(pkg.no)
            binding.edFeedpackageEtName.setText(pkg.name)

            // 状态
            val statusIndex = statusValues.indexOf(pkg.status)
            if (statusIndex >= 0) {
                binding.edFeedpackageActStatus.setText(statusOptions[statusIndex], false)
            }

            // 只读信息
            binding.edFeedpackageEtMiaochang.setText(pkg.miaochang.toString())
            binding.edFeedpackageEtCreateTime.setText(pkg.createTime ?: "")
            binding.edFeedpackageEtUpdateTime.setText(pkg.updateTime ?: "")

            // 封面图片
            loadCoverImage(pkg.cover)
        }
    }

    /**
     * 加载封面图片
     */
    private fun loadCoverImage(imageUrl: String?) {
        binding.edFeedpackageIvCover.load(imageUrl) {
            crossfade(300)
            placeholder(R.drawable.ic_image_placeholder)
            error(R.drawable.ic_image_placeholder)
            transformations(RoundedCornersTransformation(8f))
        }
    }

    /**
     * 显示图片选择对话框
     */
    private fun showImagePickerDialog() {
        val options = arrayOf("从相册选择", "拍照")

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("选择图片")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> imagePickerLauncher.launch("image/*")
                    1 -> cameraLauncher.launch(null)
                }
            }
            .show()
    }

    /**
     * 处理图片选择
     */
    private fun handleImageSelection(uri: Uri) {
        try {
            val bitmap = MediaStore.Images.Media.getBitmap(contentResolver, uri)
            processSelectedImage(bitmap)
        } catch (e: Exception) {
            Log.e(TAG, "处理选择的图片失败: ${e.message}", e)
            showToast("图片处理失败，请重试")
        }
    }

    /**
     * 处理相机拍照
     */
    private fun handleCameraCapture(bitmap: Bitmap) {
        processSelectedImage(bitmap)
    }

    /**
     * 处理选择的图片
     */
    private fun processSelectedImage(bitmap: Bitmap) {
        lifecycleScope.launch {
            try {
                // 压缩图片
                val compressedBitmap = compressBitmap(bitmap)

                // 转换为Base64
                val base64String = bitmapToBase64(compressedBitmap)

                // 检查大小
                if (base64String.length > MAX_BASE64_SIZE) {
                    showToast("图片过大，请选择较小的图片")
                    return@launch
                }

                // 保存Base64字符串
                selectedImageBase64 = base64String

                // 显示图片
                binding.edFeedpackageIvCover.setImageBitmap(compressedBitmap)

                Log.d(TAG, "图片处理成功，Base64长度: ${base64String.length}")

            } catch (e: Exception) {
                Log.e(TAG, "图片处理失败: ${e.message}", e)
                showToast("图片处理失败，请重试")
            }
        }
    }

    /**
     * 压缩图片
     */
    private fun compressBitmap(bitmap: Bitmap): Bitmap {
        val maxWidth = 800
        val maxHeight = 800

        val width = bitmap.width
        val height = bitmap.height

        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }

        val ratio = minOf(maxWidth.toFloat() / width, maxHeight.toFloat() / height)
        val newWidth = (width * ratio).toInt()
        val newHeight = (height * ratio).toInt()

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * 将Bitmap转换为Base64字符串
     */
    private fun bitmapToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, byteArrayOutputStream)
        val byteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.DEFAULT)
    }

    /**
     * 保存饲料包信息
     */
    private fun saveFeedPackage() {
        // 表单验证
        val name = binding.edFeedpackageEtName.text.toString().trim()
        if (name.isEmpty()) {
            binding.edFeedpackageTilName.error = "请输入饲料包名称"
            return
        } else {
            binding.edFeedpackageTilName.error = null
        }

        val statusText = binding.edFeedpackageActStatus.text.toString()
        val statusIndex = statusOptions.indexOf(statusText)
        if (statusIndex < 0) {
            binding.edFeedpackageTilStatus.error = "请选择状态"
            return
        } else {
            binding.edFeedpackageTilStatus.error = null
        }

        val status = statusValues[statusIndex]

        try {
            showLoading()

            // 构建要保存的数据（只包含可编辑字段）
            val packageToSave = if (isEditMode) {
                feedPackage!!.copy(
                    name = name,
                    status = status,
                    cover = selectedImageBase64 ?: feedPackage!!.cover
                )
            } else {
                // 新建模式（实际项目中可能需要后端生成ID等）
                FeedPackageJson(
                    id = 0, // 新建时ID由后端生成
                    no = "", // 编号由后端生成
                    name = name,
                    status = status,
                    cover = selectedImageBase64,
                    miaochang = 1, // 默认值，实际应该从用户信息获取
                    createTime = null,
                    updateTime = null,
                    description = null,
                    remark_admin = null
                )
            }

            Log.d(TAG, "开始保存饲料包: ${packageToSave.name}")
            viewModel.edit_feed_package(packageToSave)

        } catch (e: Exception) {
            hideLoading()
            val errorMessage = ExceptionUtil.catchException(e)
            showToast("保存失败: $errorMessage")
            Log.e(TAG, "保存饲料包失败: ${e.message}", e)
        }
    }

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.edFeedpackageProgressIndicator.visibility = View.VISIBLE
        binding.edFeedpackageBtnSave.isEnabled = false
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.edFeedpackageProgressIndicator.visibility = View.GONE
        binding.edFeedpackageBtnSave.isEnabled = true
    }
}