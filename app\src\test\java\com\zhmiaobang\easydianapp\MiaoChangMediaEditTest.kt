package com.zhmiaobang.easydianapp

import com.zhmiaobang.easydianapp.json.user.MiaoChangMedia
import com.zhmiaobang.easydianapp.json.user.MiaoChangMediaUpdateRequest
import org.junit.Test
import org.junit.Assert.*

/**
 * 苗场媒体编辑功能单元测试
 * 
 * <AUTHOR> 4.0 sonnet
 */
class MiaoChangMediaEditTest {

    @Test
    fun testMiaoChangMediaUpdateRequest_creation() {
        val request = MiaoChangMediaUpdateRequest(
            id = 1,
            contactPerson = "张三",
            contactPhone = "13800138000",
            logo = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
            douyinQrCode = "https://example.com/douyin.jpg",
            wechatQrCode = null
        )
        
        assertEquals(1, request.id)
        assertEquals("张三", request.contactPerson)
        assertEquals("13800138000", request.contactPhone)
        assertNotNull(request.logo)
        assertNotNull(request.douyinQrCode)
        assertNull(request.wechatQrCode)
    }

    @Test
    fun testMiaoChangMedia_dataBinding() {
        val media = MiaoChangMedia(
            id = 1,
            contactPerson = "李四",
            contactPhone = "13900139000",
            logo = "https://example.com/logo.png",
            douyinQrCode = null,
            wechatQrCode = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
            createTime = "2025-01-16 10:00:00",
            updateTime = "2025-01-16 12:00:00",
            resetTime = null
        )
        
        assertEquals("李四", media.contactPerson)
        assertEquals("13900139000", media.contactPhone)
        assertTrue(media.logo?.startsWith("https://") == true)
        assertNull(media.douyinQrCode)
        assertTrue(media.wechatQrCode?.startsWith("data:image/") == true)
    }

    @Test
    fun testPhoneNumberValidation() {
        val validPhones = listOf(
            "13800138000",
            "15912345678",
            "18612345678"
        )
        
        val invalidPhones = listOf(
            "1380013800",  // 太短
            "138001380001", // 太长
            "abc12345678",  // 包含字母
            ""              // 空字符串
        )
        
        validPhones.forEach { phone ->
            assertTrue("$phone should be valid", isValidPhoneNumber(phone))
        }
        
        invalidPhones.forEach { phone ->
            assertFalse("$phone should be invalid", isValidPhoneNumber(phone))
        }
    }

    @Test
    fun testBase64ImageValidation() {
        val validBase64 = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
        val invalidBase64 = "not-a-base64-string"
        val urlImage = "https://example.com/image.jpg"
        
        assertTrue(isBase64Image(validBase64))
        assertFalse(isBase64Image(invalidBase64))
        assertFalse(isBase64Image(urlImage))
    }

    // 辅助方法
    private fun isValidPhoneNumber(phone: String): Boolean {
        return phone.matches(Regex("^1[3-9]\\d{9}$"))
    }

    private fun isBase64Image(data: String): Boolean {
        return data.startsWith("data:image/") && data.contains("base64,")
    }
}
