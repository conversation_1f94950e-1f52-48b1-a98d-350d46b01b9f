package com.zhmiaobang.easydianapp.module.miacochang.feed

import android.content.ContentValues
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.core.content.FileProvider
import com.huawei.hms.hmsscankit.ScanUtil
import com.huawei.hms.ml.scan.HmsBuildBitmapOption
import com.huawei.hms.ml.scan.HmsScan

import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityShowFeedPointQrCodeBinding
import com.zhmiaobang.easydianapp.json.feed.FeedPointJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 投料点二维码显示Activity - 集成华为ScanKit二维码生成功能
 * 优化版本 by Claude 4.0 sonnet
 */
class ShowFeedPointQrCodeActivity : BaseActivity() {

    companion object {
        private const val TAG = "ShowFeedPointQrCodeActivity"
        private const val EXTRA_FEED_POINT = "show_feed_point_qrcode"

        // 二维码生成参数
        private const val QR_CODE_SIZE = 500
        private const val QR_CODE_MARGIN = 1
    }

    private lateinit var binding: ActivityShowFeedPointQrCodeBinding
    private var feedPoint: FeedPointJson? = null
    private var qrCodeBitmap: Bitmap? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityShowFeedPointQrCodeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 获取传入的投料点数据
        feedPoint = intent.getParcelableExtra(EXTRA_FEED_POINT)

        if (feedPoint == null) {
            Log.e(TAG, "缺少投料点数据参数")
            showToast("数据异常，请重试")
            finish()
            return
        }

        initializeUI()
    }

    override fun initializeUI() {
        super.initializeUI()

        setupToolbar()
        bindDataToUI()
        setupClickListeners()
        generateQrCode()
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        initToolbar(
            toolbar = binding.feedQrToolbar,
            title = "投料点二维码",
            showBack = true
        )
    }

    /**
     * 绑定数据到UI
     */
    private fun bindDataToUI() {
        val feedPointData = feedPoint!!

        // 显示投料点基本信息
        binding.tvFeedPointName.text = feedPointData.name
        binding.tvFeedPointNo.text = feedPointData.no
        binding.tvFeedPeriod.text = formatFeedPeriod(feedPointData.feedPeriod)
        binding.tvStatus.text = formatStatus(feedPointData.status)
        binding.tvDescription.text = feedPointData.description ?: "暂无描述"

        // 显示二维码内容
        binding.tvQrCodeContent.text = feedPointData.qrcode

        Log.d(TAG, "数据绑定完成，投料点: ${feedPointData.name}, 二维码: ${feedPointData.qrcode}")
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 保存二维码按钮
        binding.btnSaveQrCode.setOnClickListener {
            saveQrCodeToGallery()
        }

        // 分享二维码按钮
        binding.btnShareQrCode.setOnClickListener {
            shareQrCode()
        }
    }

    /**
     * 生成二维码
     */
    private fun generateQrCode() {
        val feedPointData = feedPoint!!
        val qrContent = feedPointData.qrcode

        if (qrContent.isBlank()) {
            Log.w(TAG, "二维码内容为空")
            showToast("二维码内容为空")
            return
        }

        try {
            Log.d(TAG, "开始生成二维码: $qrContent")

            // 显示加载指示器
            binding.progressQrGeneration.visibility = View.VISIBLE
            binding.ivQrCode.visibility = View.GONE

            // 配置二维码生成选项
            val options = HmsBuildBitmapOption.Creator()
                .setBitmapMargin(QR_CODE_MARGIN)
                .setBitmapColor(android.graphics.Color.BLACK)
                .setBitmapBackgroundColor(android.graphics.Color.WHITE)
                .create()

            // 生成二维码
            val bitmap = ScanUtil.buildBitmap(
                qrContent,
                HmsScan.QRCODE_SCAN_TYPE,
                QR_CODE_SIZE,
                QR_CODE_SIZE,
                options
            )

            if (bitmap != null) {
                qrCodeBitmap = bitmap
                binding.ivQrCode.setImageBitmap(bitmap)
                binding.ivQrCode.visibility = View.VISIBLE
                Log.d(TAG, "二维码生成成功")
            } else {
                Log.e(TAG, "二维码生成失败：返回null")
                showToast("二维码生成失败")
                binding.ivQrCode.setImageResource(R.drawable.ic_qr_code)
                binding.ivQrCode.visibility = View.VISIBLE
            }

        } catch (e: Exception) {
            Log.e(TAG, "生成二维码失败: ${e.message}", e)
            showToast("生成二维码失败：${e.message}")
            binding.ivQrCode.setImageResource(R.drawable.ic_qr_code)
            binding.ivQrCode.visibility = View.VISIBLE
        } finally {
            // 隐藏加载指示器
            binding.progressQrGeneration.visibility = View.GONE
        }
    }

    /**
     * 保存二维码到相册（包含投料点信息）
     */
    private fun saveQrCodeToGallery() {
        if (qrCodeBitmap == null) {
            showToast("请先生成二维码")
            return
        }

        try {
            val feedPointData = feedPoint!!
            val fileName = "投料点二维码_${feedPointData.name}_${System.currentTimeMillis()}.jpg"

            // 创建包含投料点信息的复合图片
            val compositeBitmap = createCompositeImage(qrCodeBitmap!!, feedPointData)

            // Android 10+ 使用 MediaStore
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, fileName)
                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/EasyDianApp")
            }

            val uri = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
            if (uri != null) {
                contentResolver.openOutputStream(uri)?.use { outputStream ->
                    compositeBitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                }
                showToast("投料点信息卡片已保存到相册")
                Log.d(TAG, "投料点信息卡片保存成功: $fileName")
            } else {
                showToast("保存失败，请重试")
                Log.e(TAG, "创建媒体文件失败")
            }

        } catch (e: Exception) {
            Log.e(TAG, "保存投料点信息卡片失败: ${e.message}", e)
            showToast("保存失败：${e.message}")
        }
    }

    /**
     * 分享投料点信息卡片
     */
    private fun shareQrCode() {
        if (qrCodeBitmap == null) {
            showToast("请先生成二维码")
            return
        }

        try {
            val feedPointData = feedPoint!!
            val fileName = "投料点信息卡片_${feedPointData.name}.jpg"

            // 创建包含投料点信息的复合图片
            val compositeBitmap = createCompositeImage(qrCodeBitmap!!, feedPointData)

            // 保存到临时文件
            val cachePath = File(cacheDir, "images")
            cachePath.mkdirs()
            val file = File(cachePath, fileName)

            FileOutputStream(file).use { outputStream ->
                compositeBitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            }

            // 获取文件URI
            val imageUri = FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                file
            )

            // 创建分享Intent
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "image/jpeg"
                putExtra(Intent.EXTRA_STREAM, imageUri)
                putExtra(Intent.EXTRA_TEXT,
                    "投料点信息卡片\n" +
                    "名称：${feedPointData.name}\n" +
                    "编号：${feedPointData.no}\n" +
                    "喂养周期：${formatFeedPeriod(feedPointData.feedPeriod)}\n" +
                    "状态：${formatStatus(feedPointData.status)}"
                )
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            startActivity(Intent.createChooser(shareIntent, "分享投料点信息卡片"))
            Log.d(TAG, "启动分享: $fileName")

        } catch (e: Exception) {
            Log.e(TAG, "分享投料点信息卡片失败: ${e.message}", e)
            showToast("分享失败：${e.message}")
        }
    }

    /**
     * 创建包含投料点信息的复合图片
     */
    private fun createCompositeImage(qrBitmap: Bitmap, feedPointData: FeedPointJson): Bitmap {
        // 图片尺寸配置
        val imageWidth = 800
        val imageHeight = 1200  // 增加高度以容纳更多信息
        val padding = 40
        val qrSize = 400

        // 创建画布
        val compositeBitmap = Bitmap.createBitmap(imageWidth, imageHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(compositeBitmap)

        // 背景色
        canvas.drawColor(Color.WHITE)

        // 配置画笔
        val titlePaint = Paint().apply {
            color = Color.parseColor("#2196F3") // hIndex_blue
            textSize = 48f
            typeface = Typeface.DEFAULT_BOLD
            isAntiAlias = true
        }

        val labelPaint = Paint().apply {
            color = Color.parseColor("#666666") // hIndex_text_secondary
            textSize = 32f
            typeface = Typeface.DEFAULT
            isAntiAlias = true
        }

        val valuePaint = Paint().apply {
            color = Color.parseColor("#333333") // hIndex_text_primary
            textSize = 36f
            typeface = Typeface.DEFAULT_BOLD
            isAntiAlias = true
        }

        val statusPaint = Paint().apply {
            color = Color.parseColor("#4CAF50") // hIndex_green
            textSize = 32f
            typeface = Typeface.DEFAULT_BOLD
            isAntiAlias = true
        }

        var currentY = padding + 60f

        // 标题
        val title = "投料点信息卡片"
        val titleBounds = Rect()
        titlePaint.getTextBounds(title, 0, title.length, titleBounds)
        val titleX = (imageWidth - titleBounds.width()) / 2f
        canvas.drawText(title, titleX, currentY, titlePaint)
        currentY += 80f

        // 投料点名称
        canvas.drawText("投料点名称：", padding.toFloat(), currentY, labelPaint)
        currentY += 50f
        canvas.drawText(feedPointData.name, padding.toFloat() + 20f, currentY, valuePaint)
        currentY += 70f

        // 投料点编号
        canvas.drawText("编号：${feedPointData.no}", padding.toFloat(), currentY, valuePaint)
        currentY += 60f

        // 喂养周期
        val feedPeriodText = "喂养周期：${formatFeedPeriod(feedPointData.feedPeriod)}"
        canvas.drawText(feedPeriodText, padding.toFloat(), currentY, valuePaint)
        currentY += 60f

        // 状态
        val statusText = "状态：${formatStatus(feedPointData.status)}"
        canvas.drawText(statusText, padding.toFloat(), currentY, statusPaint)
        currentY += 60f

        // 描述信息（如果有）
        if (!feedPointData.description.isNullOrBlank()) {
            canvas.drawText("描述：", padding.toFloat(), currentY, labelPaint)
            currentY += 45f

            // 处理长描述，分行显示
            val description = feedPointData.description!!
            val maxLineLength = 25 // 每行最大字符数
            val lines = if (description.length > maxLineLength) {
                listOf(
                    description.substring(0, minOf(maxLineLength, description.length)),
                    if (description.length > maxLineLength)
                        description.substring(maxLineLength, minOf(maxLineLength * 2, description.length)) +
                        if (description.length > maxLineLength * 2) "..." else ""
                    else ""
                ).filter { it.isNotBlank() }
            } else {
                listOf(description)
            }

            lines.forEach { line ->
                canvas.drawText(line, padding.toFloat() + 20f, currentY, valuePaint)
                currentY += 45f
            }
            currentY += 20f
        } else {
            currentY += 20f
        }

        // 二维码
        val qrX = (imageWidth - qrSize) / 2f
        val qrY = currentY
        val scaledQrBitmap = Bitmap.createScaledBitmap(qrBitmap, qrSize, qrSize, true)
        canvas.drawBitmap(scaledQrBitmap, qrX, qrY, null)
        currentY += qrSize + 40f

        // 二维码说明
        val qrLabel = "扫描二维码获取投料点信息"
        val qrLabelBounds = Rect()
        labelPaint.getTextBounds(qrLabel, 0, qrLabel.length, qrLabelBounds)
        val qrLabelX = (imageWidth - qrLabelBounds.width()) / 2f
        canvas.drawText(qrLabel, qrLabelX, currentY, labelPaint)
        currentY += 50f

        // 二维码内容
        val qrContent = feedPointData.qrcode
        val maxContentLength = 30
        val displayContent = if (qrContent.length > maxContentLength) {
            qrContent.substring(0, maxContentLength) + "..."
        } else {
            qrContent
        }

        val qrContentBounds = Rect()
        labelPaint.textSize = 24f
        labelPaint.getTextBounds(displayContent, 0, displayContent.length, qrContentBounds)
        val qrContentX = (imageWidth - qrContentBounds.width()) / 2f
        canvas.drawText(displayContent, qrContentX, currentY, labelPaint)
        currentY += 60f

        // 生成时间
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val currentTime = dateFormat.format(Date())
        val timeText = "生成时间：$currentTime"

        labelPaint.textSize = 28f
        labelPaint.color = Color.parseColor("#999999") // hIndex_text_hint
        val timeBounds = Rect()
        labelPaint.getTextBounds(timeText, 0, timeText.length, timeBounds)
        val timeX = (imageWidth - timeBounds.width()) / 2f
        canvas.drawText(timeText, timeX, currentY, labelPaint)

        return compositeBitmap
    }

    // ==================== 工具方法 ====================

    /**
     * 格式化喂养周期
     */
    private fun formatFeedPeriod(period: Int): String {
        return when {
            period <= 0 -> "未设置"
            period == 1 -> "每日1次"
            period <= 10 -> "每日${period}次"
            else -> "${period}次/周期"
        }
    }

    /**
     * 格式化状态
     */
    private fun formatStatus(status: Int): String {
        return when (status) {
            1 -> "有效"
            2 -> "无效"
            0 -> "待激活"
            else -> "未知状态"
        }
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
}