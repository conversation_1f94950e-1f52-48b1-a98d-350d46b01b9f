package com.zhmiaobang.easydianapp.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ItemLoadErrorBinding
import com.zhmiaobang.easydianapp.databinding.ItemLoadingMoreBinding
import com.zhmiaobang.easydianapp.databinding.ItemNoMoreDataBinding
import com.zhmiaobang.easydianapp.databinding.ItemPreSeedBinding
import com.zhmiaobang.easydianapp.json.preseed.PreSeedJson
import com.zhmiaobang.easydianapp.utils.LoadingState
import java.text.SimpleDateFormat
import java.util.*

/**
 * 预播种列表适配器 - 支持分页加载
 * 用于在 PreSeedListActivity 中展示预播种列表
 *
 * 功能特性：
 * - 展示预播种基本信息（头像、名称、数量、创建时间）
 * - 点击头像/名称跳转到详情页面
 * - 使用Coil加载头像图片
 * - 支持分页加载和加载状态显示
 *
 * <AUTHOR> 4.0 sonnet
 */
class PreSeedAdapter(
    private var preSeeds: MutableList<PreSeedJson> = mutableListOf()
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "PreSeedAdapter"

        // ViewType常量
        private const val TYPE_PRE_SEED = 0
        private const val TYPE_LOADING = 1
        private const val TYPE_ERROR = 2
        private const val TYPE_NO_MORE = 3
    }

    // 加载状态
    private var loadingState: LoadingState = LoadingState.IDLE
    private var errorMessage: String = ""

    // 点击事件监听器
    private var itemClickListener: OnItemClickListener? = null

    // 日期格式化器
    private val dateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    private val inputDateFormatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", Locale.getDefault())

    /**
     * 点击事件接口
     */
    interface OnItemClickListener {
        fun onItemClick(preSeed: PreSeedJson)
        fun onRetryClick()
    }

    /**
     * 设置点击事件监听器
     */
    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.itemClickListener = listener
    }

    /**
     * 更新数据（首次加载或刷新）
     */
    fun updateData(newPreSeeds: List<PreSeedJson>) {
        preSeeds.clear()
        preSeeds.addAll(newPreSeeds)
        loadingState = LoadingState.IDLE
        notifyDataSetChanged()
    }

    /**
     * 追加数据（分页加载）
     */
    fun addData(newPreSeeds: List<PreSeedJson>) {
        val startPosition = preSeeds.size
        preSeeds.addAll(newPreSeeds)
        loadingState = LoadingState.IDLE
        notifyItemRangeInserted(startPosition, newPreSeeds.size)
    }

    /**
     * 显示加载更多状态
     */
    fun showLoadingMore() {
        if (loadingState != LoadingState.LOADING_MORE) {
            loadingState = LoadingState.LOADING_MORE
            notifyItemChanged(itemCount - 1)
        }
    }

    /**
     * 显示加载错误状态
     */
    fun showLoadError(message: String) {
        errorMessage = message
        loadingState = LoadingState.ERROR
        notifyItemChanged(itemCount - 1)
    }

    /**
     * 显示没有更多数据状态
     */
    fun showNoMoreData() {
        if (loadingState != LoadingState.NO_MORE) {
            loadingState = LoadingState.NO_MORE
            notifyItemChanged(itemCount - 1)
        }
    }

    /**
     * 隐藏加载状态
     */
    fun hideLoadingState() {
        if (loadingState != LoadingState.IDLE) {
            loadingState = LoadingState.IDLE
            notifyItemChanged(itemCount - 1)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < preSeeds.size) {
            TYPE_PRE_SEED
        } else {
            when (loadingState) {
                LoadingState.LOADING_MORE -> TYPE_LOADING
                LoadingState.ERROR -> TYPE_ERROR
                LoadingState.NO_MORE -> TYPE_NO_MORE
                else -> TYPE_PRE_SEED
            }
        }
    }

    override fun getItemCount(): Int {
        return preSeeds.size + if (loadingState != LoadingState.IDLE) 1 else 0
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_PRE_SEED -> {
                val binding = ItemPreSeedBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                PreSeedViewHolder(binding)
            }
            TYPE_LOADING -> {
                val binding = ItemLoadingMoreBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                LoadingViewHolder(binding)
            }
            TYPE_ERROR -> {
                val binding = ItemLoadErrorBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ErrorViewHolder(binding)
            }
            TYPE_NO_MORE -> {
                val binding = ItemNoMoreDataBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                NoMoreViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is PreSeedViewHolder -> {
                if (position < preSeeds.size) {
                    holder.bind(preSeeds[position])
                }
            }
            is ErrorViewHolder -> {
                holder.bind(errorMessage)
            }
        }
    }

    /**
     * 预播种ViewHolder
     */
    inner class PreSeedViewHolder(
        private val binding: ItemPreSeedBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(preSeed: PreSeedJson) {
            with(binding) {
                // 绑定基本信息
                tvPreSeedName.text = preSeed.user.nickname ?: "未设置姓名"
                tvPreSeedQuantity.text = "${preSeed.quantity}株"
                
                // 格式化创建时间
                tvPreSeedCreateTime.text = formatDate(preSeed.createTime)

                // 加载头像
                loadAvatar(preSeed.user.avatar)

                // 设置点击事件
                setupClickListeners(preSeed)
            }
        }

        /**
         * 加载头像图片
         */
        private fun loadAvatar(avatarUrl: String?) {
            binding.ivPreSeedAvatar.load(avatarUrl) {
                crossfade(300)
                placeholder(R.drawable.ic_person)
                error(R.drawable.ic_person)
                transformations(CircleCropTransformation())
            }
        }

        /**
         * 格式化日期
         */
        private fun formatDate(dateString: String): String {
            return try {
                val date = inputDateFormatter.parse(dateString)
                date?.let { dateFormatter.format(it) } ?: dateString
            } catch (e: Exception) {
                dateString
            }
        }

        /**
         * 设置点击事件监听器
         */
        private fun setupClickListeners(preSeed: PreSeedJson) {
            binding.root.setOnClickListener {
                itemClickListener?.onItemClick(preSeed)
            }
        }
    }

    /**
     * 加载更多ViewHolder
     */
    inner class LoadingViewHolder(
        private val binding: ItemLoadingMoreBinding
    ) : RecyclerView.ViewHolder(binding.root)

    /**
     * 错误ViewHolder
     */
    inner class ErrorViewHolder(
        private val binding: ItemLoadErrorBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(message: String) {
            binding.tvErrorMessage.text = message
            binding.btnRetry.setOnClickListener {
                itemClickListener?.onRetryClick()
            }
        }
    }

    /**
     * 没有更多数据ViewHolder
     */
    inner class NoMoreViewHolder(
        private val binding: ItemNoMoreDataBinding
    ) : RecyclerView.ViewHolder(binding.root)
}
