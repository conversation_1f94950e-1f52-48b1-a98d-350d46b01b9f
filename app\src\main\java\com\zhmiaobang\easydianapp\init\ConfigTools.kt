package com.zhmiaobang.easydianapp.init

import android.util.Log
import com.tencent.mmkv.MMKV
import com.zhmiaobang.easydianapp.json.jwt.JwtTokenJson
import com.zhmiaobang.easydianapp.json.user.UserJson
import java.util.Calendar
import java.util.UUID

object ConfigTools {
    private const val TAG = "ConfigTools"
    private val mmkv = MMKV.defaultMMKV()

    private const val KEY_LIC = "KEY_LIC"

    fun setLicenseAgree() {
        mmkv.encode(KEY_LIC, true)
    }

    fun getLicense(): Boolean {
        return mmkv.decodeBool(KEY_LIC, false)
    }

    private const val K_UUID = "K_UUID"

    // jwt check time
    private const val KEY_JWT_TIME = "K_JWT_TIME"

    /**
     * 设置JWT Token保存时间
     */
    fun setJwtTime() {
        val current = Calendar.getInstance().timeInMillis
        mmkv.encode(KEY_JWT_TIME, current)
        Log.d(TAG, "JWT Token时间已更新: $current")
    }

    /**
     * 检查JWT Token是否需要刷新
     * @param minutes 刷新阈值（分钟）
     * @return true表示需要刷新，false表示不需要刷新
     */
    fun checkJwtTime(minutes: Int = 1): Boolean {
        val jwtTime = mmkv.decodeLong(KEY_JWT_TIME, 0)
        val current = Calendar.getInstance().timeInMillis
        val diffMinutes = (current - jwtTime) / (1000 * 60)

        val needsRefresh = diffMinutes > minutes

        Log.d(TAG, "JWT Token检查 - 保存时间: $jwtTime, 当前时间: $current")
        Log.d(TAG, "JWT Token检查 - 时间差: ${diffMinutes}分钟, 阈值: ${minutes}分钟, 需要刷新: $needsRefresh")

        return needsRefresh
    }

    /**
     * 获取JWT Token的保存时间
     */
    fun getJwtTime(): Long {
        return mmkv.decodeLong(KEY_JWT_TIME, 0)
    }

    fun getUUID(): String {
        var uuid = mmkv.decodeString(K_UUID)
        if (uuid == null) {
            uuid = UUID.randomUUID().toString().replace("-", "")
            mmkv.encode(K_UUID, uuid)
        }
        return uuid
    }

    fun checkUUID(): String? {
        return mmkv.decodeString(K_UUID)
    }

    /**
     * 移除UUID（危险操作，已修复）
     * 注意：之前这个方法会清除所有MMKV数据，现在只清除UUID
     */
    fun removeUUID() {
        Log.w(TAG, "移除UUID，但保留其他数据")
        mmkv.remove(K_UUID)
        // 移除了 mmkv.clearAll() 调用，避免清除所有数据
    }

    private const val KEY_JWT = "KEY_JWT"

    /**
     * 获取JWT Token
     */
    fun getJwtToken(): JwtTokenJson? {
        val token = mmkv.decodeParcelable(KEY_JWT, JwtTokenJson::class.java)
        if (token != null) {
            Log.d(TAG, "获取JWT Token成功: ${token.token.take(20)}...")
        } else {
            Log.d(TAG, "JWT Token不存在")
        }
        return token
    }

    /**
     * 保存JWT Token
     */
    fun setJwtToken(jwtTokenJson: JwtTokenJson) {
        mmkv.encode(KEY_JWT, jwtTokenJson)
        Log.d(TAG, "JWT Token已保存: ${jwtTokenJson.token.take(20)}...")
    }

    /**
     * 清除JWT Token
     */
    fun clearJwtToken() {
        mmkv.remove(KEY_JWT)
        mmkv.remove(KEY_JWT_TIME)
        Log.d(TAG, "JWT Token已清除")
    }

    /**
     * 清除所有MMKV数据（危险操作，仅在必要时使用）
     * 建议使用更精确的清除方法，如 clearJwtToken()
     */
    fun clearAllMMKV() {
        Log.w(TAG, "警告：正在清除所有MMKV数据")
        mmkv.clearAll()
    }

    /**
     * 安全的登出清理方法
     * 只清除认证相关数据，保留设备信息和用户偏好
     */
    fun clearAuthData() {
        Log.d(TAG, "清除认证相关数据")
        mmkv.remove(KEY_JWT)
        mmkv.remove(KEY_JWT_TIME)
        mmkv.remove(KEY_USER)
        clearScanData()
    }


    private const val KEY_USER="KEY_USER"
    /**
     * 保存用户信息
     */
    fun setUser(userJson: UserJson) {
        if(true){
            mmkv.encode(KEY_USER, userJson)
            Log.d(TAG, "用户信息已保存: ${userJson.nickname ?: "未知用户"}")
        }

    }

    /**
     * 获取用户信息
     */
    fun getUser(): UserJson? {
        val user = mmkv.decodeParcelable<UserJson>(KEY_USER, UserJson::class.java)
        if (user != null) {
            Log.d(TAG, "获取用户信息成功: ${user.nickname ?: "未知用户"}")
        } else {
            Log.d(TAG, "用户信息不存在")
        }
        return user
    }

    /**
     * 验证数据完整性
     */
    fun validateDataIntegrity(): Boolean {
        val hasJwt = getJwtToken() != null
//        val hasUser = getUser() != null
        val hasJwtTime = getJwtTime() > 0

        Log.d(TAG, "数据完整性检查 - JWT: $hasJwt,  JwtTime: $hasJwtTime")

        return hasJwt  && hasJwtTime
    }

    // 扫码相关数据管理
    private const val SCAN_RESULT_KEY = "scan_result"
    private const val SCAN_TIMESTAMP_KEY = "scan_timestamp"
    private const val EMPLOYEE_CODE_KEY = "employee_code"

    /**
     * 保存扫码结果和时间戳
     */
    fun setScanResult(scanResult: String) {
        mmkv.encode(SCAN_RESULT_KEY, scanResult)
        mmkv.encode(SCAN_TIMESTAMP_KEY, System.currentTimeMillis())
    }

    /**
     * 获取扫码结果
     */
    fun getScanResult(): String? {
        return mmkv.decodeString(SCAN_RESULT_KEY)
    }

    /**
     * 获取扫码时间戳
     */
    fun getScanTimestamp(): Long {
        return mmkv.decodeLong(SCAN_TIMESTAMP_KEY, 0)
    }

    /**
     * 保存员工代码
     */
    fun setEmployeeCode(code: String) {
        mmkv.encode(EMPLOYEE_CODE_KEY, code)
    }

    /**
     * 获取员工代码
     */
    fun getEmployeeCode(): String? {
        return mmkv.decodeString(EMPLOYEE_CODE_KEY)
    }

    /**
     * 清除员工代码
     */
    fun clearEmployeeCode() {
        mmkv.remove(EMPLOYEE_CODE_KEY)
    }

    /**
     * 清除所有扫码相关数据
     */
    fun clearScanData() {
        mmkv.remove(SCAN_RESULT_KEY)
        mmkv.remove(SCAN_TIMESTAMP_KEY)
        mmkv.remove(EMPLOYEE_CODE_KEY)
    }

}