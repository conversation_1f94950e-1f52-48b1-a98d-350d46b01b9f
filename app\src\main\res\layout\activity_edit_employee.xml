<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.miacochang.employee.EditEmployeeActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/edEmployee_toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_color"
            app:title="编辑员工"
            app:titleTextColor="@color/white"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Avatar Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="头像"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/edEmployee_iv_avatar"
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="12dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_person"
                        app:shapeAppearanceOverlay="@style/CircleImageView"
                        tools:src="@drawable/ic_person" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/edEmployee_btn_select_avatar"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="选择头像"
                        app:icon="@drawable/ic_person" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Basic Info Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="基本信息"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Employee Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edEmployee_til_name"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="员工姓名"
                        app:endIconMode="clear_text">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edEmployee_et_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1"
                            tools:text="张师傅" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Employee Phone -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edEmployee_til_phone"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="员工电话"
                        app:endIconMode="clear_text">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edEmployee_et_phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="phone"
                            android:maxLines="1"
                            tools:text="13800138000" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Status Toggle -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edEmployee_til_status"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="员工状态">

                        <AutoCompleteTextView
                            android:id="@+id/edEmployee_act_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            tools:text="在职" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Permissions Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="权限设置"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Permission: Account -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/edEmployee_switch_can_account"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="账户管理权限"
                        android:textSize="14sp" />

                    <!-- Permission: Count -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/edEmployee_switch_can_count"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="计数管理权限"
                        android:textSize="14sp" />

                    <!-- Permission: Docs -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/edEmployee_switch_can_docs"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="文档管理权限"
                        android:textSize="14sp" />

                    <!-- Permission: Feed -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/edEmployee_switch_can_feed"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="投料管理权限"
                        android:textSize="14sp" />

                    <!-- Permission: Measure -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/edEmployee_switch_can_measure"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="测量管理权限"
                        android:textSize="14sp" />

                    <!-- Permission: PreSeed -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/edEmployee_switch_can_pre_seed"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="预苗管理权限"
                        android:textSize="14sp" />

                    <!-- Permission: Sample -->
                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/edEmployee_switch_can_sample"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="样本管理权限"
                        android:textSize="14sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Read Only Info Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="系统信息（只读）"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Employee ID -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edEmployee_til_id"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="员工ID"
                        app:endIconMode="none">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edEmployee_et_id"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:enabled="false"
                            android:inputType="none"
                            tools:text="1001" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Employee Role -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edEmployee_til_role"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="员工角色"
                        app:endIconMode="none">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edEmployee_et_role"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:enabled="false"
                            android:inputType="none"
                            tools:text="A区管理员" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Save Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/edEmployee_btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                android:padding="16dp"
                android:text="保存"
                android:textSize="16sp"
                app:cornerRadius="8dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Loading Progress -->
    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/edEmployee_progress_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminate="true"
        android:visibility="gone"
        app:indicatorColor="@color/primary_color"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>