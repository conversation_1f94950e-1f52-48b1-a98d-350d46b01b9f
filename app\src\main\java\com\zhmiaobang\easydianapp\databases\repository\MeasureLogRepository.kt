package com.zhmiaobang.easydianapp.databases.repository

import android.content.Context
import androidx.lifecycle.LiveData
import com.zhmiaobang.easydianapp.databases.MyDb
import com.zhmiaobang.easydianapp.databases.dao.MeasureLogDao
import com.zhmiaobang.easydianapp.databases.entity.MeasureLog
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 测量日志仓库类
 * 
 * 提供对测量日志数据的统一访问接口，封装数据库操作
 * 使用 Repository 模式，为 ViewModel 提供数据访问服务
 * 
 * <AUTHOR> 4.0 sonnet
 */
class MeasureLogRepository private constructor(context: Context) {
    
    private val measureLogDao: MeasureLogDao = MyDb.getDatabase(context).measureLogDao()
    
    companion object {
        @Volatile
        private var INSTANCE: MeasureLogRepository? = null
        
        /**
         * 获取仓库实例（单例模式）
         * 
         * @param context 应用程序上下文
         * @return 仓库实例
         */
        fun getInstance(context: Context): MeasureLogRepository {
            return INSTANCE ?: synchronized(this) {
                val instance = MeasureLogRepository(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    // ==================== 基本 CRUD 操作 ====================
    
    /**
     * 创建新的测量日志记录
     * 
     * @param onnxModelJson 选中的ONNX模型
     * @param phone 电话号码
     * @param imgSrc 源图片路径
     * @return 插入记录的 ID
     */
    suspend fun createMeasureLog(
        onnxModelJson: OnnxModelJson,
        phone: String,
        imgSrc: String
    ): Long = withContext(Dispatchers.IO) {
        val measureLog = MeasureLog.create(
            onnxModelJson = onnxModelJson,
            phone = phone,
            imgSrc = imgSrc
        )
        measureLogDao.insert(measureLog)
    }
    
    /**
     * 插入测量日志记录
     * 
     * @param measureLog 要插入的测量日志
     * @return 插入记录的 ID
     */
    suspend fun insertMeasureLog(measureLog: MeasureLog): Long = withContext(Dispatchers.IO) {
        measureLogDao.insert(measureLog)
    }
    
    /**
     * 更新测量日志记录
     * 
     * @param measureLog 要更新的测量日志
     * @return 受影响的行数
     */
    suspend fun updateMeasureLog(measureLog: MeasureLog): Int = withContext(Dispatchers.IO) {
        measureLogDao.update(measureLog)
    }
    
    /**
     * 删除测量日志记录
     * 
     * @param measureLog 要删除的测量日志
     * @return 受影响的行数
     */
    suspend fun deleteMeasureLog(measureLog: MeasureLog): Int = withContext(Dispatchers.IO) {
        measureLogDao.delete(measureLog)
    }
    
    /**
     * 根据 ID 删除测量日志记录
     * 
     * @param id 要删除的记录 ID
     * @return 受影响的行数
     */
    suspend fun deleteMeasureLogById(id: Long): Int = withContext(Dispatchers.IO) {
        measureLogDao.deleteById(id)
    }
    
    // ==================== 查询操作 ====================
    
    /**
     * 根据 ID 获取测量日志记录
     * 
     * @param id 记录 ID
     * @return 测量日志记录，如果不存在则返回 null
     */
    suspend fun getMeasureLogById(id: Long): MeasureLog? = withContext(Dispatchers.IO) {
        measureLogDao.getById(id)
    }
    
    /**
     * 获取所有测量日志记录（LiveData）
     * 
     * @return 所有测量日志记录的 LiveData
     */
    fun getAllMeasureLogsLiveData(): LiveData<List<MeasureLog>> {
        return measureLogDao.getAllLiveData()
    }
    
    /**
     * 获取所有测量日志记录
     * 
     * @return 所有测量日志记录
     */
    suspend fun getAllMeasureLogs(): List<MeasureLog> = withContext(Dispatchers.IO) {
        measureLogDao.getAll()
    }
    
    /**
     * 分页获取测量日志记录
     * 
     * @param page 页码（从0开始）
     * @param pageSize 每页数量
     * @return 测量日志记录列表
     */
    suspend fun getMeasureLogsByPage(page: Int, pageSize: Int): List<MeasureLog> = withContext(Dispatchers.IO) {
        val offset = page * pageSize
        measureLogDao.getPage(pageSize, offset)
    }
    
    /**
     * 根据电话号码查询测量日志记录
     * 
     * @param phone 电话号码
     * @return 该电话号码的所有测量日志记录
     */
    suspend fun getMeasureLogsByPhone(phone: String): List<MeasureLog> = withContext(Dispatchers.IO) {
        measureLogDao.getByPhone(phone)
    }
    
    /**
     * 根据电话号码查询测量日志记录（LiveData）
     * 
     * @param phone 电话号码
     * @return 该电话号码的所有测量日志记录的 LiveData
     */
    fun getMeasureLogsByPhoneLiveData(phone: String): LiveData<List<MeasureLog>> {
        return measureLogDao.getByPhoneLiveData(phone)
    }
    
    /**
     * 根据时间范围查询测量日志记录
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 指定时间范围内的测量日志记录
     */
    suspend fun getMeasureLogsByTimeRange(startTime: Long, endTime: Long): List<MeasureLog> = withContext(Dispatchers.IO) {
        measureLogDao.getByTimeRange(startTime, endTime)
    }
    
    /**
     * 获取已完成的测量日志记录
     * 
     * @return 已完成的测量日志记录
     */
    suspend fun getCompletedMeasureLogs(): List<MeasureLog> = withContext(Dispatchers.IO) {
        measureLogDao.getCompleted()
    }
    
    /**
     * 获取未完成的测量日志记录
     * 
     * @return 未完成的测量日志记录
     */
    suspend fun getIncompleteMeasureLogs(): List<MeasureLog> = withContext(Dispatchers.IO) {
        measureLogDao.getIncomplete()
    }
    
    // ==================== 统计查询 ====================
    
    /**
     * 获取测量日志记录总数
     * 
     * @return 记录总数
     */
    suspend fun getMeasureLogCount(): Int = withContext(Dispatchers.IO) {
        measureLogDao.getCount()
    }
    
    /**
     * 获取已完成的测量日志记录数量
     * 
     * @return 已完成的记录数量
     */
    suspend fun getCompletedMeasureLogCount(): Int = withContext(Dispatchers.IO) {
        measureLogDao.getCompletedCount()
    }
    
    /**
     * 获取指定电话号码的测量日志记录数量
     * 
     * @param phone 电话号码
     * @return 该电话号码的记录数量
     */
    suspend fun getMeasureLogCountByPhone(phone: String): Int = withContext(Dispatchers.IO) {
        measureLogDao.getCountByPhone(phone)
    }
    
    // ==================== 更新操作 ====================
    
    /**
     * 更新测量结果
     *
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @param count 计数结果
     * @return 受影响的行数
     */
    suspend fun updateMeasureResult(id: Long, destImg: String?, count: Int?): Int = withContext(Dispatchers.IO) {
        measureLogDao.updateMeasureResult(id, destImg, count)
    }

    /**
     * 更新测量结果（包含阈值信息）
     *
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @param count 计数结果
     * @param conf 置信度阈值
     * @param nms NMS阈值
     * @return 受影响的行数
     */
    suspend fun updateMeasureResultWithThresholds(
        id: Long,
        destImg: String?,
        count: Int?,
        conf: Float?,
        nms: Float?
    ): Int = withContext(Dispatchers.IO) {
        measureLogDao.updateMeasureResultWithThresholds(id, destImg, count, conf, nms)
    }

    /**
     * 更新测量结果（包含阈值信息和检测坐标）
     *
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @param count 计数结果
     * @param conf 置信度阈值
     * @param nms NMS阈值
     * @param detectionCoordinates 有效检测点坐标数据
     * @return 受影响的行数
     */
    suspend fun updateMeasureResultWithCoordinates(
        id: Long,
        destImg: String?,
        count: Int?,
        conf: Float?,
        nms: Float?,
        detectionCoordinates: FloatArray?
    ): Int = withContext(Dispatchers.IO) {
        // 使用 FloatArrayConverter 将 FloatArray 转换为 JSON 字符串
        val converter = com.zhmiaobang.easydianapp.databases.converter.FloatArrayConverter()
        val coordinatesJson = converter.fromFloatArray(detectionCoordinates)

        measureLogDao.updateMeasureResultWithCoordinates(
            id, destImg, count, conf, nms, coordinatesJson
        )
    }

    // ==================== 分页查询操作 ====================

    /**
     * 分页查询测量日志记录
     *
     * @param page 页码（从1开始）
     * @param pageSize 每页数量
     * @return 分页的测量日志记录列表
     */
    suspend fun getPagedMeasureLogs(page: Int, pageSize: Int = 20): List<MeasureLog> = withContext(Dispatchers.IO) {
        val offset = (page - 1) * pageSize
        measureLogDao.getPagedMeasureLogs(pageSize, offset)
    }

    /**
     * 获取测量日志记录总数
     *
     * @return 记录总数
     */
    suspend fun getTotalCount(): Int = withContext(Dispatchers.IO) {
        measureLogDao.getTotalCount()
    }

    /**
     * 根据电话号码分页查询测量日志记录
     *
     * @param phone 电话号码
     * @param page 页码（从1开始）
     * @param pageSize 每页数量
     * @return 该电话号码的分页测量日志记录
     */
    suspend fun getPagedMeasureLogsByPhone(phone: String, page: Int, pageSize: Int = 20): List<MeasureLog> = withContext(Dispatchers.IO) {
        val offset = (page - 1) * pageSize
        measureLogDao.getPagedMeasureLogsByPhone(phone, pageSize, offset)
    }

    /**
     * 根据电话号码获取记录总数
     *
     * @param phone 电话号码
     * @return 该电话号码的记录总数
     */
    suspend fun getTotalCountByPhone(phone: String): Int = withContext(Dispatchers.IO) {
        measureLogDao.getTotalCountByPhone(phone)
    }
    
    /**
     * 更新目标图片路径
     * 
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @return 受影响的行数
     */
    suspend fun updateDestImg(id: Long, destImg: String?): Int = withContext(Dispatchers.IO) {
        measureLogDao.updateDestImg(id, destImg)
    }
    
    /**
     * 更新计数结果
     * 
     * @param id 记录 ID
     * @param count 计数结果
     * @return 受影响的行数
     */
    suspend fun updateCount(id: Long, count: Int?): Int = withContext(Dispatchers.IO) {
        measureLogDao.updateCount(id, count)
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 清空所有测量日志记录
     * 
     * @return 受影响的行数
     */
    suspend fun clearAllMeasureLogs(): Int = withContext(Dispatchers.IO) {
        measureLogDao.deleteAll()
    }
}
