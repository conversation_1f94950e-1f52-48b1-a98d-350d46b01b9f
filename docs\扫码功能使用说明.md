# 扫码登录功能使用说明

## 功能概述

本功能集成了华为HMS ScanKit，为苗邦登录界面提供二维码扫描登录能力。

## 技术实现

### 核心技术栈
- **华为ScanKit 2.12.0.301** - 二维码扫描引擎
- **MMKV 2.2.2** - 本地数据存储
- **BaseActivity权限管理** - 统一权限处理

### 主要功能
1. **权限检查** - 自动检查和请求相机权限
2. **扫码识别** - 使用华为ScanKit进行二维码识别
3. **结果保存** - 将扫码结果保存到本地存储
4. **错误处理** - 完善的错误处理和用户反馈

## 使用流程

### 1. 用户操作流程
```
点击"启动扫码登录"按钮 
    ↓
检查相机权限
    ↓
启动华为ScanKit扫码界面
    ↓
扫描员工二维码
    ↓
验证二维码内容
    ↓
保存结果并跳转到主界面
```

### 2. 权限处理
- **首次使用**: 自动请求相机权限
- **权限被拒绝**: 显示提示信息，引导用户手动授权
- **权限已授予**: 直接启动扫码功能

### 3. 扫码结果处理
- **成功扫码**: 验证内容格式，保存到MMKV，跳转主界面
- **扫码失败**: 显示错误提示，允许重新扫码
- **用户取消**: 显示取消提示，返回登录界面

## 数据存储

### MMKV存储键值
- `scan_result`: 扫码结果内容
- `scan_timestamp`: 扫码时间戳

### 数据格式
```kotlin
// 保存扫码结果
mmkv.putString("scan_result", scanResult)
mmkv.putLong("scan_timestamp", System.currentTimeMillis())

// 读取扫码结果
val result = mmkv.getString("scan_result", "")
val timestamp = mmkv.getLong("scan_timestamp", 0)
```

## 二维码格式支持

### 当前支持的格式
- 简单文本格式: `EMP001`, `EMPLOYEE_12345`
- 结构化格式: `USER:张三:ROLE:管理员`
- JSON格式: `{"userId":"001","name":"张三","role":"员工"}`

### 验证规则
- 内容不能为空
- 最小长度为3个字符
- 可扩展自定义验证逻辑

## 错误处理

### 常见错误及解决方案
1. **相机权限被拒绝**
   - 提示: "需要相机权限才能使用扫码功能"
   - 解决: 引导用户到设置中手动授权

2. **扫码失败**
   - 提示: "扫码失败，请重试"
   - 解决: 检查光线条件，重新扫码

3. **无效二维码**
   - 提示: "无效的员工二维码，请重新扫描"
   - 解决: 确认二维码格式正确

## 测试说明

### 单元测试
- 文件位置: `app/src/test/java/com/zhmiaobang/easydianapp/module/login/LoginActivityTest.kt`
- 测试内容: 二维码验证逻辑、边界条件处理

### 运行测试
```bash
./gradlew test
```

## 扩展功能

### 可扩展的验证逻辑
在 `validateQRCode()` 方法中可以添加：
- 服务器端验证
- 复杂格式解析
- 加密内容解密
- 用户权限验证

### 自定义扫码界面
如需自定义扫码界面，可以：
1. 修改 `HmsScanAnalyzerOptions` 配置
2. 实现自定义扫码Activity
3. 集成自定义UI组件

## 注意事项

1. **权限要求**: 必须获得相机权限才能使用扫码功能
2. **网络要求**: 当前版本为离线验证，如需在线验证需要网络连接
3. **设备兼容性**: 支持华为HMS服务的设备有更好的体验
4. **性能优化**: 扫码过程中避免频繁操作，等待扫码完成

---

*文档更新时间: 2025-07-16*  
*技术支持: Claude 4.0 sonnet*
