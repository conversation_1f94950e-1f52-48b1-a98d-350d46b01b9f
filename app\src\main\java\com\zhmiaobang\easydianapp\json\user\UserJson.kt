package com.zhmiaobang.easydianapp.json.user

import android.os.Parcelable
import com.zhmiaobang.easydianapp.json.deviceJson.DeviceJson
import kotlinx.parcelize.Parcelize

@Parcelize
data class UserJson(
    val avatar: String?=null,
    val boss: Int?=null,
    val canAccount: <PERSON><PERSON><PERSON>,
    val canCount: <PERSON><PERSON><PERSON>,
    val canDocs: <PERSON><PERSON><PERSON>,
    val canFeed: <PERSON><PERSON>an,
    val canMeasure: <PERSON><PERSON>an,
    val canPreSeed: <PERSON><PERSON>an,
    val canSample: <PERSON><PERSON><PERSON>,
    val employeePhone: String?=null,
    val id: Int,
    val is_active: <PERSON><PERSON>an,
    val last_avatar_time: String?=null,
    val last_ip: String?=null,
//    val last_login_device: DeviceJson?=null,
    val miaochang: Miaochang,
    val nickname: String?=null,
    val openid: String?=null,
    val phone: String?=null,
    val role: Role,
    val uuid: String?=null
): Parcelable