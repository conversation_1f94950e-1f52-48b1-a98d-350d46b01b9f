package com.zhmiaobang.easydianapp.utils

/**
 * 分页加载状态枚举
 * 
 * <AUTHOR> 4.0 sonnet
 */
enum class LoadingState {
    IDLE,           // 空闲状态
    LOADING_FIRST,  // 首次加载
    LOADING_MORE,   // 加载更多
    ERROR,          // 加载错误
    NO_MORE         // 没有更多数据
}

/**
 * 分页数据管理类
 * 
 * <AUTHOR> 4.0 sonnet
 */
data class PaginationState(
    val currentPage: Int = 1,
    val totalCount: Int = 0,
    val hasMoreData: Boolean = true,
    val loadingState: LoadingState = LoadingState.IDLE,
    val errorMessage: String? = null
) {
    
    /**
     * 是否正在加载中
     */
    val isLoading: Boolean
        get() = loadingState == LoadingState.LOADING_FIRST || loadingState == LoadingState.LOADING_MORE
    
    /**
     * 是否可以加载更多
     */
    val canLoadMore: Boolean
        get() = hasMoreData && !isLoading && loadingState != LoadingState.ERROR
    
    /**
     * 开始首次加载
     */
    fun startFirstLoading(): PaginationState {
        return copy(
            currentPage = 1,
            loadingState = LoadingState.LOADING_FIRST,
            errorMessage = null
        )
    }
    
    /**
     * 开始加载更多
     */
    fun startLoadingMore(): PaginationState {
        return copy(
            loadingState = LoadingState.LOADING_MORE,
            errorMessage = null
        )
    }
    
    /**
     * 加载成功
     */
    fun loadSuccess(hasNext: Boolean, totalCount: Int = this.totalCount): PaginationState {
        return copy(
            currentPage = currentPage + 1,
            totalCount = totalCount,
            hasMoreData = hasNext,
            loadingState = if (hasNext) LoadingState.IDLE else LoadingState.NO_MORE,
            errorMessage = null
        )
    }
    
    /**
     * 加载失败
     */
    fun loadError(errorMessage: String): PaginationState {
        return copy(
            loadingState = LoadingState.ERROR,
            errorMessage = errorMessage
        )
    }
    
    /**
     * 重置状态
     */
    fun reset(): PaginationState {
        return PaginationState()
    }
}

/**
 * 分页数据容器
 * 
 * <AUTHOR> 4.0 sonnet
 */
data class PaginatedData<T>(
    val items: List<T>,
    val paginationState: PaginationState
)
