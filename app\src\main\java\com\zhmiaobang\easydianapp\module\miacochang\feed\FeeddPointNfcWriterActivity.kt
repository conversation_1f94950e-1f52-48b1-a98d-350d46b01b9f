package com.zhmiaobang.easydianapp.module.miacochang.feed

import android.app.PendingIntent
import android.content.Intent
import android.content.IntentFilter
import android.nfc.NdefMessage
import android.nfc.NdefRecord
import android.nfc.NfcAdapter
import android.nfc.Tag
import android.nfc.tech.Ndef
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityFeeddPointNfcWriterBinding
import com.zhmiaobang.easydianapp.json.feed.FeedPointJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import java.io.IOException
import java.nio.charset.Charset

/**
 * 投料点NFC写入Activity - 集成Android NFC API写入功能
 * 优化版本 by Claude 4.0 sonnet
 */
class FeeddPointNfcWriterActivity : BaseActivity() {

    companion object {
        private const val TAG = "FeeddPointNfcWriterActivity"
        private const val EXTRA_FEED_POINT = "write_feed_point_nfc"

        // NFC写入状态
        private const val NFC_STATE_DISABLED = 0
        private const val NFC_STATE_ENABLED = 1
        private const val NFC_STATE_WRITING = 2
        private const val NFC_STATE_SUCCESS = 3
        private const val NFC_STATE_ERROR = 4
    }

    private lateinit var binding: ActivityFeeddPointNfcWriterBinding
    private var feedPoint: FeedPointJson? = null

    // NFC相关
    private var nfcAdapter: NfcAdapter? = null
    private var pendingIntent: PendingIntent? = null
    private var intentFilters: Array<IntentFilter>? = null
    private var techLists: Array<Array<String>>? = null
    private var isWriteMode = false
    private var currentNfcState = NFC_STATE_DISABLED

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFeeddPointNfcWriterBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 获取传入的投料点数据
        feedPoint = intent.getParcelableExtra(EXTRA_FEED_POINT)

        if (feedPoint == null) {
            Log.e(TAG, "缺少投料点数据参数")
            showToast("数据异常，请重试")
            finish()
            return
        }

        initializeUI()
    }

    override fun initializeUI() {
        super.initializeUI()

        setupToolbar()
        bindDataToUI()
        setupClickListeners()
        initializeNFC()
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        initToolbar(
            toolbar = binding.nfcWriterToolbar,
            title = "NFC写入",
            showBack = true
        )
    }

    /**
     * 绑定数据到UI
     */
    private fun bindDataToUI() {
        val feedPointData = feedPoint!!

        // 显示投料点基本信息
        binding.tvFeedPointName.text = feedPointData.name
        binding.tvFeedPointNo.text = feedPointData.no
        binding.tvFeedPeriod.text = formatFeedPeriod(feedPointData.feedPeriod)
        binding.tvStatus.text = formatStatus(feedPointData.status)
        binding.tvDescription.text = feedPointData.description ?: "暂无描述"
        binding.tvNfcCode.text = feedPointData.nfccode

        Log.d(TAG, "数据绑定完成，投料点: ${feedPointData.name}, NFC码: ${feedPointData.nfccode}")
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 写入NFC按钮
        binding.btnWriteNfc.setOnClickListener {
            startNfcWriteMode()
        }

        // NFC设置按钮
        binding.btnNfcSettings.setOnClickListener {
            openNfcSettings()
        }
    }

    /**
     * 初始化NFC
     */
    private fun initializeNFC() {
        try {
            // 获取NFC适配器
            nfcAdapter = NfcAdapter.getDefaultAdapter(this)

            if (nfcAdapter == null) {
                // 设备不支持NFC
                updateNfcStatus("设备不支持NFC功能", NFC_STATE_ERROR)
                binding.btnWriteNfc.isEnabled = false
                binding.btnNfcSettings.visibility = View.GONE
                Log.w(TAG, "设备不支持NFC")
                return
            }

            // 设置前台调度
            setupForegroundDispatch()

            // 检查NFC状态
            checkNfcStatus()

        } catch (e: Exception) {
            Log.e(TAG, "初始化NFC失败: ${e.message}", e)
            updateNfcStatus("NFC初始化失败", NFC_STATE_ERROR)
            binding.btnWriteNfc.isEnabled = false
        }
    }

    /**
     * 设置前台调度
     */
    private fun setupForegroundDispatch() {
        try {
            // 创建PendingIntent
            val intent = Intent(this, javaClass).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            pendingIntent = PendingIntent.getActivity(
                this, 0, intent,
                PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )

            // 设置IntentFilter
            val ndefFilter = IntentFilter(NfcAdapter.ACTION_NDEF_DISCOVERED)
            val tagFilter = IntentFilter(NfcAdapter.ACTION_TAG_DISCOVERED)
            val techFilter = IntentFilter(NfcAdapter.ACTION_TECH_DISCOVERED)

            intentFilters = arrayOf(ndefFilter, tagFilter, techFilter)

            // 设置技术列表
            techLists = arrayOf(
                arrayOf(Ndef::class.java.name)
            )

            Log.d(TAG, "前台调度设置完成")

        } catch (e: Exception) {
            Log.e(TAG, "设置前台调度失败: ${e.message}", e)
        }
    }

    /**
     * 检查NFC状态
     */
    private fun checkNfcStatus() {
        if (nfcAdapter?.isEnabled == true) {
            updateNfcStatus("NFC已启用，点击按钮开始写入", NFC_STATE_ENABLED)
            binding.btnWriteNfc.isEnabled = true
            binding.btnNfcSettings.visibility = View.GONE
        } else {
            updateNfcStatus("NFC未启用，请先开启NFC功能", NFC_STATE_DISABLED)
            binding.btnWriteNfc.isEnabled = false
            binding.btnNfcSettings.visibility = View.VISIBLE
        }
    }

    /**
     * 更新NFC状态显示
     */
    private fun updateNfcStatus(message: String, state: Int) {
        currentNfcState = state
        binding.tvNfcStatus.text = message

        when (state) {
            NFC_STATE_DISABLED -> {
                binding.ivNfcIcon.setColorFilter(resources.getColor(R.color.hIndex_text_hint, null))
                binding.progressNfcOperation.visibility = View.GONE
            }
            NFC_STATE_ENABLED -> {
                binding.ivNfcIcon.setColorFilter(resources.getColor(R.color.hIndex_blue, null))
                binding.progressNfcOperation.visibility = View.GONE
            }
            NFC_STATE_WRITING -> {
                binding.ivNfcIcon.setColorFilter(resources.getColor(R.color.hIndex_blue, null))
                binding.progressNfcOperation.visibility = View.VISIBLE
            }
            NFC_STATE_SUCCESS -> {
                binding.ivNfcIcon.setColorFilter(resources.getColor(R.color.hIndex_green, null))
                binding.progressNfcOperation.visibility = View.GONE
            }
            NFC_STATE_ERROR -> {
                binding.ivNfcIcon.setColorFilter(resources.getColor(R.color.hIndex_red, null))
                binding.progressNfcOperation.visibility = View.GONE
            }
        }
    }

    // ==================== 生命周期方法 ====================

    override fun onResume() {
        super.onResume()

        // 启用前台调度
        if (nfcAdapter != null && pendingIntent != null && intentFilters != null && techLists != null) {
            try {
                nfcAdapter!!.enableForegroundDispatch(this, pendingIntent, intentFilters, techLists)
                Log.d(TAG, "前台调度已启用")
            } catch (e: Exception) {
                Log.e(TAG, "启用前台调度失败: ${e.message}", e)
            }
        }

        // 重新检查NFC状态
        checkNfcStatus()
    }

    override fun onPause() {
        super.onPause()

        // 禁用前台调度
        if (nfcAdapter != null) {
            try {
                nfcAdapter!!.disableForegroundDispatch(this)
                Log.d(TAG, "前台调度已禁用")
            } catch (e: Exception) {
                Log.e(TAG, "禁用前台调度失败: ${e.message}", e)
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)

        if (isWriteMode) {
            handleNfcIntent(intent)
        }
    }

    // ==================== NFC写入功能 ====================

    /**
     * 开始NFC写入模式
     */
    private fun startNfcWriteMode() {
        val feedPointData = feedPoint!!

        if (feedPointData.nfccode.isBlank()) {
            showToast("NFC码为空，无法写入")
            return
        }

        if (nfcAdapter?.isEnabled != true) {
            showToast("请先开启NFC功能")
            return
        }

        isWriteMode = true
        updateNfcStatus("请将NFC卡片靠近设备背面", NFC_STATE_WRITING)
        binding.btnWriteNfc.text = "等待NFC卡片..."
        binding.btnWriteNfc.isEnabled = false

        Log.d(TAG, "开始NFC写入模式，等待卡片: ${feedPointData.nfccode}")
    }

    /**
     * 处理NFC Intent
     */
    private fun handleNfcIntent(intent: Intent) {
        val action = intent.action
        Log.d(TAG, "收到NFC Intent: $action")

        if (NfcAdapter.ACTION_NDEF_DISCOVERED == action ||
            NfcAdapter.ACTION_TAG_DISCOVERED == action ||
            NfcAdapter.ACTION_TECH_DISCOVERED == action) {

            val tag = intent.getParcelableExtra<Tag>(NfcAdapter.EXTRA_TAG)
            if (tag != null) {
                processNfcTag(tag)
            } else {
                Log.w(TAG, "未检测到有效的NFC标签")
                showToast("未检测到有效的NFC卡片")
                resetWriteMode()
            }
        }
    }

    /**
     * 处理NFC标签
     */
    private fun processNfcTag(tag: Tag) {
        try {
            Log.d(TAG, "检测到NFC标签，开始处理")
            updateNfcStatus("检测到NFC卡片，正在读取...", NFC_STATE_WRITING)

            val ndef = Ndef.get(tag)
            if (ndef == null) {
                Log.w(TAG, "NFC标签不支持NDEF格式")
                showToast("此NFC卡片不支持数据写入")
                resetWriteMode()
                return
            }

            ndef.connect()

            // 检查卡片是否已有数据
            val existingMessage = ndef.ndefMessage
            if (existingMessage != null && existingMessage.records.isNotEmpty()) {
                ndef.close()
                showOverwriteConfirmDialog(tag)
            } else {
                // 直接写入
                writeNfcData(ndef)
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理NFC标签失败: ${e.message}", e)
            showToast("读取NFC卡片失败：${e.message}")
            resetWriteMode()
        }
    }

    /**
     * 显示覆盖确认对话框
     */
    private fun showOverwriteConfirmDialog(tag: Tag) {
        AlertDialog.Builder(this)
            .setTitle("确认覆盖")
            .setMessage("此NFC卡片已包含数据，是否要覆盖现有数据？")
            .setPositiveButton("覆盖") { _, _ ->
                try {
                    val ndef = Ndef.get(tag)
                    ndef?.connect()
                    writeNfcData(ndef)
                } catch (e: Exception) {
                    Log.e(TAG, "重新连接NFC标签失败: ${e.message}", e)
                    showToast("连接NFC卡片失败，请重试")
                    resetWriteMode()
                }
            }
            .setNegativeButton("取消") { _, _ ->
                resetWriteMode()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 写入NFC数据
     */
    private fun writeNfcData(ndef: Ndef?) {
        try {
            if (ndef == null) {
                showToast("NFC卡片不支持数据写入")
                resetWriteMode()
                return
            }

            val feedPointData = feedPoint!!
            val nfcContent = feedPointData.nfccode

            Log.d(TAG, "开始写入NFC数据: $nfcContent")
            updateNfcStatus("正在写入数据...", NFC_STATE_WRITING)

            // 检查卡片是否可写
            if (!ndef.isWritable) {
                ndef.close()
                showToast("此NFC卡片为只读，无法写入数据")
                resetWriteMode()
                return
            }

            // 创建NDEF记录
            val textRecord = NdefRecord.createTextRecord("zh", nfcContent)
            val ndefMessage = NdefMessage(arrayOf(textRecord))

            // 检查数据大小
            val messageSize = ndefMessage.toByteArray().size
            val maxSize = ndef.maxSize

            if (messageSize > maxSize) {
                ndef.close()
                showToast("数据太大，超出NFC卡片容量限制")
                resetWriteMode()
                return
            }

            // 写入数据
            ndef.writeNdefMessage(ndefMessage)
            ndef.close()

            // 验证写入
            verifyNfcWrite(nfcContent)

        } catch (e: IOException) {
            Log.e(TAG, "写入NFC数据失败: ${e.message}", e)
            showToast("写入失败：${e.message}")
            resetWriteMode()
        } catch (e: Exception) {
            Log.e(TAG, "写入NFC数据异常: ${e.message}", e)
            showToast("写入异常：${e.message}")
            resetWriteMode()
        }
    }

    /**
     * 验证NFC写入
     */
    private fun verifyNfcWrite(expectedContent: String) {
        try {
            Log.d(TAG, "NFC数据写入成功，预期内容: $expectedContent")

            updateNfcStatus("写入成功！", NFC_STATE_SUCCESS)
            showToast("NFC数据写入成功！")

            // 延迟重置，让用户看到成功状态
            binding.root.postDelayed({
                resetWriteMode()
            }, 2000)

        } catch (e: Exception) {
            Log.e(TAG, "验证NFC写入失败: ${e.message}", e)
            showToast("验证写入失败：${e.message}")
            resetWriteMode()
        }
    }

    /**
     * 重置写入模式
     */
    private fun resetWriteMode() {
        isWriteMode = false
        binding.btnWriteNfc.text = "开始写入NFC"
        binding.btnWriteNfc.isEnabled = true

        // 恢复正常状态
        if (nfcAdapter?.isEnabled == true) {
            updateNfcStatus("NFC已启用，点击按钮开始写入", NFC_STATE_ENABLED)
        } else {
            updateNfcStatus("NFC未启用，请先开启NFC功能", NFC_STATE_DISABLED)
        }

        Log.d(TAG, "写入模式已重置")
    }

    /**
     * 打开NFC设置
     */
    private fun openNfcSettings() {
        try {
            val intent = Intent(Settings.ACTION_NFC_SETTINGS)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开NFC设置失败: ${e.message}", e)
            showToast("无法打开NFC设置")
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 格式化喂养周期
     */
    private fun formatFeedPeriod(period: Int): String {
        return when {
            period <= 0 -> "未设置"
            period == 1 -> "每日1次"
            period <= 10 -> "每日${period}次"
            else -> "${period}次/周期"
        }
    }

    /**
     * 格式化状态
     */
    private fun formatStatus(status: Int): String {
        return when (status) {
            1 -> "有效"
            2 -> "无效"
            0 -> "待激活"
            else -> "未知状态"
        }
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
}