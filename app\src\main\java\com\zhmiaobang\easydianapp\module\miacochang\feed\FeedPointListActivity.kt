package com.zhmiaobang.easydianapp.module.miacochang.feed

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.adapter.FeedPointAdapter
import com.zhmiaobang.easydianapp.databinding.ActivityFeedPointListBinding
import com.zhmiaobang.easydianapp.json.feed.FeedPointJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.utils.PaginationScrollListener
import com.zhmiaobang.easydianapp.viewmodel.feed.FeedViewModel
import kotlin.collections.isNotEmpty

/**
 * 投料点列表Activity
 *
 * 功能特性：
 * - 展示投料点列表数据
 * - 支持QR码和NFC码操作
 * - 点击投料点跳转到编辑页面
 * - 支持添加新投料点
 * - 完整的加载状态和错误处理
 *
 * <AUTHOR> 4.0 sonnet
 */
class FeedPointListActivity : BaseActivity() {

    companion object {
        private const val TAG = "FeedPointListActivity"
        private const val REQUEST_CODE_EDIT = 1001
    }

    // ViewBinding
    private val binding: ActivityFeedPointListBinding by lazy {
        ActivityFeedPointListBinding.inflate(layoutInflater)
    }

    // ViewModel
    private val viewModel: FeedViewModel by viewModels()

    // Adapter
    private lateinit var feedPointAdapter: FeedPointAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initializeUI()
        // 手动调用setupObservers，因为BaseActivity的单参数onCreate不会自动调用
        setupObservers()

        Log.d(TAG, "FeedPointListActivity创建完成，观察者已设置")
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()
        setupToolbar()
        setupRecyclerView()
        setupSwipeRefresh()

        // 添加网络测试
//        testNetworkConnection()

        loadFeedPointData()
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()
        Log.d(TAG, "开始设置观察者")

        // 观察分页数据
        viewModel.paginatedDataObserver.observe(this) { paginatedData ->
            Log.d(TAG, "收到分页数据: items=${paginatedData.items.size}, state=${paginatedData.paginationState}")

            try {
                val items = paginatedData.items
                val state = paginatedData.paginationState

                // 停止下拉刷新动画
                binding.swipeRefreshLayout.isRefreshing = false

                // 根据加载状态更新UI
                when (state.loadingState) {
                    LoadingState.LOADING_FIRST -> {
                        // 首次加载，显示主加载指示器
                        showLoading()
                    }
                    LoadingState.LOADING_MORE -> {
                        // 加载更多，在adapter中显示
                        hideLoading()
                        feedPointAdapter.showLoadingMore()
                    }
                    LoadingState.ERROR -> {
                        // 加载错误
                        hideLoading()
                        if (items.isEmpty()) {
                            showError(state.errorMessage ?: "加载失败")
                        } else {
                            feedPointAdapter.showLoadError(state.errorMessage ?: "加载失败")
                        }
                    }
                    LoadingState.NO_MORE -> {
                        // 没有更多数据
                        hideLoading()
                        feedPointAdapter.showNoMoreData()
                    }
                    LoadingState.IDLE -> {
                        // 加载完成
                        hideLoading()
                        feedPointAdapter.hideLoadingState()
                    }
                }

                // 更新数据显示
                if (items.isNotEmpty()) {
                    showFeedPointList(items)
                    Log.d(TAG, "显示投料点列表，总数量: ${items.size}")
                } else if (state.loadingState != LoadingState.LOADING_FIRST) {
                    showEmptyState()
                }

            } catch (e: Exception) {
                Log.e(TAG, "处理分页数据失败: ${e.message}", e)
                hideLoading()
                binding.swipeRefreshLayout.isRefreshing = false
                showError("数据处理失败，请重试")
            }
        }

        // 观察错误信息
        viewModel.errorObserver.observe(this) { errorMessage ->
            Log.e(TAG, "ViewModel错误: $errorMessage")
            hideLoading()
            showError(errorMessage)
        }

        Log.d(TAG, "观察者设置完成")
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        initToolbar(
            toolbar = binding.toolbar,
            title = "投料点管理",
            showBack = true
        )
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        feedPointAdapter = FeedPointAdapter()
        val layoutManager = LinearLayoutManager(this)

        binding.recyclerView.apply {
            this.layoutManager = layoutManager
            adapter = feedPointAdapter
            setHasFixedSize(true)
        }

        // 设置点击事件监听器
        feedPointAdapter.setOnItemClickListener(object : FeedPointAdapter.OnItemClickListener {
            override fun onItemClick(feedPoint: FeedPointJson) {
                Log.d(TAG, "点击投料点: ${feedPoint.name}")
                navigateToEditActivity(feedPoint)
            }

            override fun onQrCodeClick(feedPoint: FeedPointJson) {
                Log.d(TAG, "点击QR码: ${feedPoint.name}")
                handleQrCodeAction(feedPoint)
            }

            override fun onNfcCodeClick(feedPoint: FeedPointJson) {
                Log.d(TAG, "点击NFC码: ${feedPoint.name}")
                handleNfcCodeAction(feedPoint)
            }

            override fun onRetryClick() {
                Log.d(TAG, "点击重试按钮")
                viewModel.retryLoad()
            }
        })

        // 设置分页滚动监听器
        val scrollListener = object : PaginationScrollListener(layoutManager) {
            override fun isLoading(): Boolean {
                return viewModel.getCurrentPaginationState().isLoading
            }

            override fun isLastPage(): Boolean {
                return !viewModel.getCurrentPaginationState().hasMoreData
            }

            override fun loadMoreItems() {
                Log.d(TAG, "滚动触发加载更多")
                viewModel.loadNextPage()
            }
        }

        binding.recyclerView.addOnScrollListener(scrollListener)
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setOnRefreshListener {
            Log.d(TAG, "下拉刷新触发")
            viewModel.refresh()
        }

        // 设置刷新指示器颜色
        binding.swipeRefreshLayout.setColorSchemeResources(
            R.color.primary_color,
            R.color.secondary_color
        )
    }

    /**
     * 设置悬浮按钮
     */


    /**
     * 测试网络连接
     */
    private fun testNetworkConnection() {
        Log.d(TAG, "开始网络连接测试...")

        // 检查网络权限
        val hasInternetPermission = checkSelfPermission(android.Manifest.permission.INTERNET) ==
            android.content.pm.PackageManager.PERMISSION_GRANTED
        Log.d(TAG, "网络权限状态: $hasInternetPermission")

        // 检查网络状态
        val connectivityManager = getSystemService(android.content.Context.CONNECTIVITY_SERVICE) as
            android.net.ConnectivityManager
        val networkInfo = connectivityManager.activeNetworkInfo
        val isConnected = networkInfo?.isConnected == true
        Log.d(TAG, "网络连接状态: $isConnected, 网络类型: ${networkInfo?.typeName}")

        if (!isConnected) {
            Log.w(TAG, "设备未连接到网络")
            showToast("网络连接异常，请检查网络设置")
        }
    }

    /**
     * 加载投料点数据
     */
    private fun loadFeedPointData() {
        Log.d(TAG, "开始加载投料点数据")

        // 检查JWT Token
        val token = com.zhmiaobang.easydianapp.init.ConfigTools.getJwtToken()
        if (token == null) {
            Log.e(TAG, "JWT Token为空，无法发送请求")
            showError("用户未登录，请重新登录")
            return
        }

        // 检查用户信息
        val user = com.zhmiaobang.easydianapp.init.ConfigTools.getUser()
        if (user == null) {
            Log.e(TAG, "用户信息为空")
            showError("用户信息异常，请重新登录")
            return
        }

        try {
            Log.d(TAG, "开始加载第一页投料点数据...")
            viewModel.loadFirstPage()
            Log.d(TAG, "投料点数据请求已发送")
        } catch (e: Exception) {
            hideLoading()
            binding.swipeRefreshLayout.isRefreshing = false
            val errorMessage = ExceptionUtil.catchException(e)
            showError(errorMessage)
            Log.e(TAG, "加载投料点数据失败: ${e.message}", e)
        }
    }

    /**
     * 显示投料点列表
     */
    private fun showFeedPointList(feedPoints: List<FeedPointJson>) {
        Log.d(TAG, "显示投料点列表，数量: ${feedPoints.size}")

        binding.recyclerView.visibility = View.VISIBLE
        binding.emptyStateLayout.visibility = View.GONE

        feedPointAdapter.updateData(feedPoints)
    }

    /**
     * 显示空状态
     */
    private fun showEmptyState() {
        Log.d(TAG, "显示空状态")

        binding.recyclerView.visibility = View.GONE
        binding.emptyStateLayout.visibility = View.VISIBLE
    }

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.progressIndicator.visibility = View.VISIBLE
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.progressIndicator.visibility = View.GONE
    }

    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        showToast(message)
        showEmptyState()
    }

    /**
     * 跳转到编辑投料点页面
     */
    private fun navigateToEditActivity(feedPoint: FeedPointJson?) {
        val intent = Intent(this, EditFeedpointActivity::class.java)

        feedPoint?.let {
            intent.putExtra("feed_point", it)
        }

        // 使用 startActivityForResult 以便接收返回结果
        startActivityForResult(intent, REQUEST_CODE_EDIT)

    }

    /**
     * 处理Activity返回结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_CODE_EDIT && resultCode == Activity.RESULT_OK) {
            // 编辑成功，刷新列表
            refreshData()
        }
    }

    /**
     * 处理QR码操作
     */
    private fun handleQrCodeAction(feedPoint: FeedPointJson) {
        Log.d(TAG, "QR码操作，投料点: ${feedPoint.name}, QR码: ${feedPoint.qrcode}")

        // TODO: 实现QR码相关操作
        // 可以是生成QR码、扫描QR码或显示QR码信息
//        showToast("QR码功能：${feedPoint.name}")
        val intent = Intent(this, ShowFeedPointQrCodeActivity::class.java)
        intent.putExtra("show_feed_point_qrcode",feedPoint)
        startActivity(intent)
        // 示例：可以启动二维码扫描或显示二维码
        // 参考 LoginActivity 中的扫码实现
    }

    /**
     * 处理NFC码操作
     */
    private fun handleNfcCodeAction(feedPoint: FeedPointJson) {
        Log.d(TAG, "NFC码操作，投料点: ${feedPoint.name}, NFC码: ${feedPoint.nfccode}")

        // TODO: 实现NFC相关操作
        // 可以是读取NFC、写入NFC或显示NFC信息
        val intent = Intent(this, FeeddPointNfcWriterActivity::class.java)
        intent.putExtra("write_feed_point_nfc",feedPoint)
        startActivity(intent)

        // 示例：可以启动NFC读写操作
    }

    /**
     * 刷新数据
     */
    private fun refreshData() {
        Log.d(TAG, "刷新投料点数据")
        loadFeedPointData()
    }

    /**
     * Activity结果处理
     */
    override fun onResume() {
        super.onResume()
        // 注释掉自动刷新，避免重复请求
        // 只有从编辑页面返回时才需要刷新，通过onActivityResult处理
        Log.d(TAG, "Activity恢复，不自动刷新数据")
    }
}