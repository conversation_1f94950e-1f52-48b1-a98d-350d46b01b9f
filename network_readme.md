# 🌐 RetrofitClient 网络请求库使用指南

## 📋 概述

RetrofitClient 是本项目的核心网络请求组件，基于 Retrofit2 + OkHttp3 构建，提供了完整的 HTTP 请求解决方案，包括 JWT 认证、缓存控制、错误处理等功能。

## 🏗️ 架构设计

```
RetrofitClient (单例)
├── ApiService (接口定义)
├── OkHttpClient (HTTP 客户端)
│   ├── CacheInterceptor (缓存拦截器)
│   └── CommonInterceptor (无缓存拦截器)
├── Gson (JSON 转换器)
└── ExceptionUtil (异常处理工具)
```

## 🚀 快速开始

### 1. 基本使用

```kotlin
// 获取 API 服务实例
val apiService = RetrofitClient.apiService

// 在协程中发起请求
lifecycleScope.launch {
    try {
        val response = apiService.someApiMethod()
        // 处理成功响应
        handleSuccess(response)
    } catch (e: Exception) {
        // 处理异常
        val errorMessage = ExceptionUtil.catchException(e)
        showError(errorMessage)
    }
}
```

### 2. 缓存控制

```kotlin
// 启用缓存（默认）
RetrofitClient.setUseCache(true)

// 禁用缓存
RetrofitClient.setUseCache(false)
```

## 🔧 核心功能

### 1. JWT 认证

系统会自动从 `ConfigTools.getJwtToken()` 获取 JWT Token 并添加到请求头：

```http
Authorization: Bearer {token}
```

**相关配置：**
- Token 存储：通过 MMKV 持久化存储
- 自动添加：所有请求自动携带 Authorization 头
- 过期检查：支持 Token 过期时间验证

### 2. 缓存策略

#### 启用缓存模式
- **缓存大小**：100MB
- **缓存位置**：应用缓存目录
- **缓存控制**：支持 HTTP 标准缓存头

#### 无缓存模式
- **强制不缓存**：添加 `Cache-Control: no-store`
- **清除缓存头**：移除 `Expires`、`Last-Modified`、`ETag`

### 3. 网络配置

```kotlin
// 当前配置
private const val BASE_URL = "https://9945-116-7-168-167.ngrok-free.app"
private const val CACHE_SIZE: Long = 100 * 1024 * 1024 // 100 MiB
private const val CALL_TIMEOUT = 30 // 秒
```

## 📝 API 接口定义

### 创建 API 接口

在 `ApiService.kt` 中定义你的 API 接口：

```kotlin
interface ApiService {
    
    @GET("api/user/profile")
    suspend fun getUserProfile(): Response<UserProfile>
    
    @POST("api/auth/login")
    suspend fun login(@Body loginRequest: LoginRequest): Response<LoginResponse>
    
    @POST("api/shrimp/count")
    suspend fun uploadShrimpCount(
        @Body countData: BatchCountJson
    ): Response<CountResponse>
    
    @GET("api/data/statistics")
    suspend fun getStatistics(
        @Query("startDate") startDate: String,
        @Query("endDate") endDate: String
    ): Response<StatisticsResponse>
}
```

## 🎯 实际使用示例

### 1. 在 Activity 中使用

```kotlin
class MainActivity : BaseActivity() {
    
    private fun loadUserData() {
        lifecycleScope.launch {
            try {
                showLoading(true)
                
                val response = RetrofitClient.apiService.getUserProfile()
                if (response.isSuccessful) {
                    val userProfile = response.body()
                    updateUI(userProfile)
                } else {
                    showError("获取用户信息失败")
                }
                
            } catch (e: Exception) {
                val errorMessage = ExceptionUtil.catchException(e)
                showError(errorMessage)
            } finally {
                showLoading(false)
            }
        }
    }
}
```

### 2. 上传数据示例

```kotlin
private fun uploadCountData(batchData: BatchCountJson) {
    lifecycleScope.launch {
        try {
            // 禁用缓存确保数据实时性
            RetrofitClient.setUseCache(false)
            
            val response = RetrofitClient.apiService.uploadShrimpCount(batchData)
            
            if (response.isSuccessful) {
                showToast("数据上传成功")
                finish()
            } else {
                showError("上传失败：${response.message()}")
            }
            
        } catch (e: Exception) {
            val errorMessage = ExceptionUtil.catchException(e)
            showError("上传异常：$errorMessage")
        } finally {
            // 恢复缓存设置
            RetrofitClient.setUseCache(true)
        }
    }
}
```

## ⚠️ 错误处理

### 使用 ExceptionUtil

```kotlin
try {
    val response = RetrofitClient.apiService.someMethod()
    // 处理响应
} catch (e: Exception) {
    val userFriendlyMessage = ExceptionUtil.catchException(e)
    showToast(userFriendlyMessage)
}
```

### 常见错误类型

| 异常类型 | 用户提示 | 说明 |
|---------|---------|------|
| `SocketTimeoutException` | "请求超时" | 网络超时 |
| `UnknownHostException` | "服务器链接失败" | DNS 解析失败 |
| `HttpException(401)` | "认证失败" | Token 过期或无效 |
| `JsonSyntaxException` | "数据解析错误" | JSON 格式错误 |

## 🔒 安全考虑

### 1. Token 管理
- 使用 MMKV 安全存储 JWT Token
- 支持 Token 过期时间检查
- 自动在请求头中添加认证信息

### 2. 网络安全
- 使用 HTTPS 协议
- 支持 Gzip 压缩减少数据传输
- 合理的超时设置防止长时间等待

## 🛠️ 配置建议

### 1. 生产环境配置

```kotlin
// 建议将 BASE_URL 配置化
object NetworkConfig {
    const val BASE_URL = BuildConfig.API_BASE_URL
    const val CACHE_SIZE = 50 * 1024 * 1024L // 50MB
    const val TIMEOUT_SECONDS = 15L
}
```

### 2. 调试配置

```kotlin
// 添加日志拦截器（仅调试模式）
if (BuildConfig.DEBUG) {
    val loggingInterceptor = HttpLoggingInterceptor()
    loggingInterceptor.level = HttpLoggingInterceptor.Level.BODY
    okHttpBuilder.addInterceptor(loggingInterceptor)
}
```

## 📊 性能优化

### 1. 缓存策略
- 对于静态数据启用缓存
- 对于实时数据禁用缓存
- 合理设置缓存大小

### 2. 请求优化
- 使用协程进行异步请求
- 合理设置超时时间
- 启用 Gzip 压缩

## 🔄 版本依赖

```gradle
implementation 'com.squareup.retrofit2:retrofit:2.11.0'
implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
implementation 'com.squareup.okhttp3:okhttp:4.12.0'
implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
implementation 'com.tencent:mmkv:2.2.2'
```

## 📞 技术支持

如有问题，请联系开发团队或查看项目文档。

---

*最后更新：2025-07-16*
*作者：Claude 4.0 sonnet* 🐾
