package com.zhmiaobang.easydianapp.databases.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson

/**
 * OnnxModelJson 类型转换器
 *
 * 用于在 Room Database 中存储和读取复杂的 OnnxModelJson 对象
 * 将对象转换为 JSON 字符串存储，读取时再转换回对象
 *
 * <AUTHOR> 4.0 sonnet
 */
class OnnxModelJsonConverter {

    private val gson = Gson()

    /**
     * 将 OnnxModelJson 对象转换为 JSON 字符串
     *
     * @param onnxModelJson 要转换的 OnnxModelJson 对象
     * @return JSON 字符串，如果输入为 null 则返回 null
     */
    @TypeConverter
    fun fromOnnxModelJson(onnxModelJson: OnnxModelJson?): String? {
        return onnxModelJson?.let { gson.toJson(it) }
    }

    /**
     * 将 JSON 字符串转换为 OnnxModelJson 对象
     *
     * @param json JSON 字符串
     * @return OnnxModelJson 对象，如果输入为 null 或空字符串则返回 null
     */
    @TypeConverter
    fun toOnnxModelJson(json: String?): OnnxModelJson? {
        return if (json.isNullOrEmpty()) {
            null
        } else {
            try {
                val type = object : TypeToken<OnnxModelJson>() {}.type
                gson.fromJson(json, type)
            } catch (e: Exception) {
                // 如果 JSON 解析失败，记录错误并返回 null
                android.util.Log.e("OnnxModelJsonConverter", "Failed to parse JSON: $json", e)
                null
            }
        }
    }
}
