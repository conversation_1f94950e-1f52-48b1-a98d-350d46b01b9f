<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/aqua_gradient_start"
    android:fillViewport="true"
    tools:context=".module.home.HomeMeasureFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 体长记录区域 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_measure_records"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardMaxElevation="12dp"
            app:cardUseCompatPadding="true"
            app:cardBackgroundColor="@android:color/transparent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp"
                android:background="@drawable/aqua_card_background_model">

                <ImageView
                    android:id="@+id/iv_records_icon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_description_24"
                    android:tint="@color/aqua_primary"
                    android:background="@drawable/circle_shape"
                    android:backgroundTint="@color/aqua_primary_light"
                    android:padding="12dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_records_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="📊 体长记录"
                    android:textColor="@color/aqua_primary_dark"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toEndOf="@id/iv_records_icon"
                    app:layout_constraintTop_toTopOf="@id/iv_records_icon"
                    app:layout_constraintEnd_toStartOf="@id/iv_records_arrow" />

                <TextView
                    android:id="@+id/tv_records_subtitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:text="查看历史测量记录"
                    android:textColor="@color/aqua_secondary"
                    android:textSize="14sp"
                    app:layout_constraintStart_toEndOf="@id/iv_records_icon"
                    app:layout_constraintTop_toBottomOf="@id/tv_records_title"
                    app:layout_constraintEnd_toStartOf="@id/iv_records_arrow" />

                <ImageView
                    android:id="@+id/iv_records_arrow"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_right"
                    android:tint="@color/aqua_primary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <!-- 测量操作区域 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_measure_operations"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardMaxElevation="12dp"
            app:cardUseCompatPadding="true"
            app:cardBackgroundColor="@android:color/transparent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/aqua_card_background_image">

                <!-- 操作标题 -->
                <TextView
                    android:id="@+id/tv_operations_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="20dp"
                    android:layout_marginBottom="12dp"
                    android:text="🔬 开始测量"
                    android:textColor="@color/aqua_primary_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

            <!-- 模型选择区域 -->
            <LinearLayout
                android:id="@+id/section_model_selection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="16dp"
                android:orientation="vertical"
                android:padding="16dp"
                android:background="@drawable/aqua_card_background_model">

                <TextView
                    android:id="@+id/tv_model_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🤖 测量模型"
                    android:textColor="@color/aqua_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_select_model"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:background="?attr/selectableItemBackground"
                    android:drawableEnd="@drawable/ic_arrow_right"
                    android:drawableTint="@color/aqua_primary"
                    android:padding="16dp"
                    android:text="点击选择测量模型"
                    android:textColor="@color/aqua_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- 选中的模型信息 -->
                <LinearLayout
                    android:id="@+id/layout_selected_model"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_selected_model_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/aqua_primary_dark"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="南美白对虾计数模型" />

                    <TextView
                        android:id="@+id/tv_selected_model_category"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/aqua_secondary"
                        android:textSize="14sp"
                        tools:text="南美白对虾" />

                </LinearLayout>

            </LinearLayout>

            <!-- 电话号码输入区域 -->
            <LinearLayout
                android:id="@+id/section_phone_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="16dp"
                android:orientation="vertical"
                android:padding="16dp"
                android:background="@drawable/aqua_card_background_phone">

                <TextView
                    android:id="@+id/tv_phone_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📞 电话号码"
                    android:textColor="@color/aqua_secondary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:hint="请输入电话号码"
                    app:boxStrokeColor="@color/aqua_secondary"
                    app:hintTextColor="@color/aqua_secondary"
                    app:boxBackgroundColor="@color/white"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/ed_measure_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone"
                        android:maxLines="1"
                        android:textColor="@color/aqua_primary_dark"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- 图片选择区域 -->
            <LinearLayout
                android:id="@+id/section_image_selection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="20dp"
                android:orientation="vertical"
                android:padding="16dp"
                android:background="@drawable/aqua_card_background_image">

                <TextView
                    android:id="@+id/tv_image_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📸 选择图片"
                    android:textColor="@color/aqua_accent_dark"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- 按钮容器 -->
                <LinearLayout
                    android:id="@+id/layout_image_buttons"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_gallery"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="相册"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:drawableTop="@drawable/ic_image_24"
                        android:drawableTint="@color/white"
                        android:background="@drawable/aqua_button_gallery"
                        android:elevation="4dp"
                        style="@style/Widget.Material3.Button" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_camera"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="拍照"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:drawableTop="@drawable/ic_camera_24"
                        android:drawableTint="@color/white"
                        android:background="@drawable/aqua_button_camera"
                        android:elevation="4dp"
                        style="@style/Widget.Material3.Button" />

                </LinearLayout>

                <!-- 选中的图片信息 -->
                <LinearLayout
                    android:id="@+id/layout_selected_image"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_selected_image_path"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/aqua_primary_dark"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:ellipsize="middle"
                        android:singleLine="true"
                        android:background="@color/aqua_gradient_start"
                        android:padding="8dp"
                        android:drawableStart="@drawable/ic_image_24"
                        android:drawableTint="@color/aqua_accent"
                        android:drawablePadding="8dp"
                        tools:text="/storage/emulated/0/DCIM/Camera/IMG_20231201_123456.jpg" />

                </LinearLayout>

            </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>