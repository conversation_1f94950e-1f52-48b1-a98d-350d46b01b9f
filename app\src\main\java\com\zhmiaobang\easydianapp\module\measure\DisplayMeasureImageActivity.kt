package com.zhmiaobang.easydianapp.module.measure

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.FileProvider
import com.github.chrisbanes.photoview.PhotoView
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.button.MaterialButton
import com.google.android.material.progressindicator.CircularProgressIndicator
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import java.io.File

/**
 * 测量图片显示Activity
 *
 * 功能特性：
 * - 支持图片缩放、平移操作
 * - 显示测量计数结果
 * - 支持图片分享和保存
 * - 处理大图片内存优化
 * - 完整的错误处理
 *
 * <AUTHOR> 4.0 sonnet
 */
class DisplayMeasureImageActivity : BaseActivity() {

    companion object {
        private const val TAG = "DisplayMeasureImageActivity"

        // Intent 参数常量
        const val EXTRA_IMAGE_PATH = "extra_image_path"
        const val EXTRA_COUNT_VALUE = "extra_count_value"
        const val EXTRA_TITLE = "extra_title"
        const val EXTRA_PHONE = "extra_phone"
        const val EXTRA_MODEL_NAME = "extra_model_name"
    }

    // UI组件
    private lateinit var toolbar: MaterialToolbar
    private lateinit var photoView: PhotoView
    private lateinit var progressIndicator: CircularProgressIndicator
    private lateinit var errorLayout: LinearLayout
    private lateinit var tvErrorMessage: TextView
    private lateinit var btnRetryLoad: MaterialButton
    private lateinit var tvCountValue: TextView
    private lateinit var btnShare: MaterialButton
    private lateinit var btnSave: MaterialButton

    // 数据
    private var imagePath: String? = null
    private var countValue: Int? = null
    private var titleText: String? = null
    private var phoneNumber: String? = null
    private var modelName: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_display_measure_image)

        initializeData()
        initializeViews()
        setupToolbar()
        setupClickListeners()

        // 延迟一点加载图片，确保UI完全初始化
        photoView.post {
            loadImage()
        }
    }

    // ==================== 初始化方法 ====================

    /**
     * 初始化数据
     */
    private fun initializeData() {
        imagePath = intent.getStringExtra(EXTRA_IMAGE_PATH)
        countValue = intent.getIntExtra(EXTRA_COUNT_VALUE, -1).takeIf { it != -1 }
        titleText = intent.getStringExtra(EXTRA_TITLE)
        phoneNumber = intent.getStringExtra(EXTRA_PHONE)
        modelName = intent.getStringExtra(EXTRA_MODEL_NAME)

        Log.d(TAG, "初始化数据:")
        Log.d(TAG, "  imagePath = $imagePath")
        Log.d(TAG, "  countValue = $countValue")
        Log.d(TAG, "  titleText = $titleText")
        Log.d(TAG, "  phoneNumber = $phoneNumber")
        Log.d(TAG, "  modelName = $modelName")

        // 检查图片路径是否有效
        if (!imagePath.isNullOrBlank()) {
            val file = File(imagePath!!)
            Log.d(TAG, "  图片文件存在: ${file.exists()}")
            Log.d(TAG, "  图片文件大小: ${file.length()} bytes")
            Log.d(TAG, "  图片文件路径: ${file.absolutePath}")
        }
    }

    /**
     * 初始化视图组件
     */
    private fun initializeViews() {
        toolbar = findViewById(R.id.toolbar)
        photoView = findViewById(R.id.photoView)
        progressIndicator = findViewById(R.id.progressIndicator)
        errorLayout = findViewById(R.id.errorLayout)
        tvErrorMessage = findViewById(R.id.tvErrorMessage)
        btnRetryLoad = findViewById(R.id.btnRetryLoad)
        tvCountValue = findViewById(R.id.tvCountValue)
        btnShare = findViewById(R.id.btnShare)
        btnSave = findViewById(R.id.btnSave)

        // 设置计数值显示
        if (countValue != null) {
            tvCountValue.text = countValue.toString()
        } else {
            tvCountValue.text = "--"
        }

        Log.d(TAG, "视图组件初始化完成")
    }

    /**
     * 设置工具栏
     */
    private fun setupToolbar() {
        // 设置标题
        val subtitle = when {
            !titleText.isNullOrBlank() -> titleText
            !phoneNumber.isNullOrBlank() -> "测量结果 - $phoneNumber"
            else -> "测量结果"
        }

        initToolbar(toolbar, modelName!!, showBack = true)
        toolbar.title=modelName
        toolbar.subtitle=subtitle
        Log.d(TAG, "工具栏设置完成: $title")
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 重试加载按钮
        btnRetryLoad.setOnClickListener {
            Log.d(TAG, "点击重试加载")
            loadImage()
        }

        // 分享按钮
        btnShare.setOnClickListener {
            Log.d(TAG, "点击分享图片")
            shareImage()
        }

        // 保存按钮
        btnSave.setOnClickListener {
            Log.d(TAG, "点击保存图片")
            saveImage()
        }

        Log.d(TAG, "点击事件监听器设置完成")
    }

    // ==================== 图片加载方法 ====================

    /**
     * 加载图片 - 直接使用 PhotoView
     */
    private fun loadImage() {
        if (imagePath.isNullOrBlank()) {
            Log.e(TAG, "图片路径为空")
            showError("图片路径无效")
            return
        }

        Log.d(TAG, "开始加载图片: $imagePath")
        showLoading()

        try {
            val imageFile = File(imagePath!!)
            if (!imageFile.exists()) {
                Log.e(TAG, "图片文件不存在: $imagePath")
                showError("图片文件不存在")
                return
            }

            // 检查文件大小，避免内存溢出
            val fileSize = imageFile.length()
            Log.d(TAG, "图片文件大小: ${fileSize / 1024 / 1024}MB")

            if (fileSize > 100 * 1024 * 1024) { // 100MB
                Log.w(TAG, "图片文件过大，可能导致内存溢出")
                showError("图片文件过大，无法显示")
                return
            }

            // 直接使用 PhotoView 加载图片
            Log.d(TAG, "使用 PhotoView 直接加载图片")

            // 方法1：使用 URI
            val imageUri = Uri.fromFile(imageFile)
            photoView.setImageURI(imageUri)

            // 延迟检查图片是否加载成功
            photoView.post {
                if (photoView.drawable != null) {
                    Log.d(TAG, "PhotoView 图片加载成功")
                    hideLoading()
                    showImage()
                } else {
                    Log.e(TAG, "PhotoView 图片加载失败，尝试备用方案")
                    // 备用方案：直接设置图片路径
                    loadImageWithPath(imageFile)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "加载图片时发生异常: ${e.message}", e)
            hideLoading()
            showError("图片加载失败: ${e.message}")
        }
    }

    /**
     * 使用文件路径直接加载图片（备用方案）
     */
    private fun loadImageWithPath(imageFile: File) {
        try {
            Log.d(TAG, "使用文件路径直接加载图片")

            // 清除之前的图片
            photoView.setImageURI(null)

            // 使用文件 URI
            val fileUri = Uri.parse("file://${imageFile.absolutePath}")
            photoView.setImageURI(fileUri)

            // 延迟检查加载结果
            photoView.postDelayed({
                if (photoView.drawable != null) {
                    Log.d(TAG, "文件路径加载图片成功")
                    hideLoading()
                    showImage()
                } else {
                    Log.e(TAG, "所有方法都无法加载图片")
                    hideLoading()
                    showError("图片格式不支持或文件损坏")
                }
            }, 500)

        } catch (e: Exception) {
            Log.e(TAG, "文件路径加载图片失败: ${e.message}", e)
            hideLoading()
            showError("图片加载失败: ${e.message}")
        }
    }

    // ==================== UI状态管理方法 ====================

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        progressIndicator.visibility = View.VISIBLE
        errorLayout.visibility = View.GONE
        photoView.visibility = View.GONE
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        progressIndicator.visibility = View.GONE
    }

    /**
     * 显示图片
     */
    private fun showImage() {
        photoView.visibility = View.VISIBLE
        errorLayout.visibility = View.GONE
    }

    /**
     * 显示错误状态
     */
    private fun showError(message: String) {
        tvErrorMessage.text = message
        errorLayout.visibility = View.VISIBLE
        photoView.visibility = View.GONE
    }

    // ==================== 图片操作方法 ====================

    /**
     * 分享图片
     */
    private fun shareImage() {
        if (imagePath.isNullOrBlank()) {
            showToast("没有可分享的图片")
            return
        }

        try {
            val imageFile = File(imagePath!!)
            if (!imageFile.exists()) {
                showToast("图片文件不存在")
                return
            }

            // 使用 FileProvider 创建安全的 URI
            val imageUri = FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                imageFile
            )

            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "image/*"
                putExtra(Intent.EXTRA_STREAM, imageUri)
                putExtra(Intent.EXTRA_TEXT, buildShareText())
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            startActivity(Intent.createChooser(shareIntent, "分享测量结果"))
            Log.d(TAG, "启动分享界面")

        } catch (e: Exception) {
            Log.e(TAG, "分享图片失败: ${e.message}", e)
            showToast("分享失败，请重试")
        }
    }

    /**
     * 保存图片到相册
     */
    private fun saveImage() {
        if (imagePath.isNullOrBlank()) {
            showToast("没有可保存的图片")
            return
        }

        try {
            // 这里可以实现保存到相册的逻辑
            // 由于涉及到权限和存储访问框架，暂时显示提示
            showToast("图片已保存在: $imagePath")
            Log.d(TAG, "图片保存功能调用")

        } catch (e: Exception) {
            Log.e(TAG, "保存图片失败: ${e.message}", e)
            showToast("保存失败，请重试")
        }
    }

    /**
     * 构建分享文本
     */
    private fun buildShareText(): String {
        return buildString {
            appendLine("🐟 测量结果分享")
            appendLine("━━━━━━━━━━━━━━━━━━━━")
            if (!phoneNumber.isNullOrBlank()) {
                appendLine("📱 手机号: $phoneNumber")
            }
            if (!modelName.isNullOrBlank()) {
                appendLine("🤖 模型: $modelName")
            }
            if (countValue != null) {
                appendLine("📊 计数结果: $countValue 个")
            }
            appendLine("⏰ 分享时间: ${System.currentTimeMillis()}")
        }
    }
}
