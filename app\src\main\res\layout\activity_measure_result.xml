<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/aqua_gradient_start"
    tools:context=".module.measure.MeasureResultActivity">

    <!-- Material Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/measure_result_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/aqua_primary"
        android:elevation="4dp"
        app:titleTextColor="@android:color/white"
        app:subtitleTextColor="@color/aqua_secondary_light"
        app:titleCentered="false"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@android:color/white"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar" />

    <!-- 主内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">



            <!-- 检测计数结果/加载动画卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_count_result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardMaxElevation="12dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@android:color/transparent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="24dp"
                    android:background="@drawable/aqua_card_background_image">

                    <!-- 加载状态布局 -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_loading_state"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="visible"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tv_loading_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="正在分析..."
                            android:textColor="@color/aqua_accent_dark"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <!-- 加载动画 -->
                        <com.google.android.material.progressindicator.CircularProgressIndicator
                            android:id="@+id/progress_detection"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:layout_marginTop="16dp"
                            android:indeterminate="true"
                            app:indicatorColor="@color/aqua_accent"
                            app:trackColor="@color/aqua_secondary_light"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tv_loading_label" />

                        <TextView
                            android:id="@+id/tv_loading_status"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:text="正在执行目标检测..."
                            android:textColor="@color/aqua_secondary"
                            android:textSize="14sp"
                            app:layout_constraintStart_toEndOf="@id/progress_detection"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/progress_detection"
                            app:layout_constraintBottom_toBottomOf="@id/progress_detection" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <!-- 结果显示布局 -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layout_result_state"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tv_count_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="检测数量"
                            android:textColor="@color/aqua_accent_dark"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_count_result"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="0"
                            android:textColor="@color/aqua_accent"
                            android:textSize="48sp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tv_count_label" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_marginBottom="8dp"
                            android:text="个"
                            android:textColor="@color/aqua_primary_dark"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toEndOf="@id/tv_count_result"
                            app:layout_constraintBottom_toBottomOf="@id/tv_count_result" />

                        <!-- 分析时间 -->
                        <TextView
                            android:id="@+id/tv_analysis_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:textColor="@color/aqua_secondary"
                            android:textSize="14sp"
                            android:gravity="center"
                            app:layout_constraintTop_toBottomOf="@id/tv_count_result"
                            tools:text="分析耗时：2.3秒" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <!-- 图片显示卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_image_display"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardMaxElevation="12dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@android:color/transparent"
                app:layout_constraintTop_toBottomOf="@id/card_count_result">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp"
                    android:background="@drawable/aqua_card_background_image">

                    <TextView
                        android:id="@+id/tv_image_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📸 分析图片"
                        android:textColor="@color/aqua_accent_dark"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- 图片显示区域 - 支持动态长宽比 -->
                    <ImageView
                        android:id="@+id/imageResult"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="12dp"
                        android:background="@color/white"
                        android:scaleType="fitCenter"
                        android:adjustViewBounds="true"
                        android:maxHeight="400dp"
                        android:minHeight="200dp"
                        android:contentDescription="分析图片"
                        app:layout_constraintTop_toBottomOf="@id/tv_image_label"
                        app:layout_constraintDimensionRatio="H,16:9"
                        tools:src="@drawable/ic_image_placeholder" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>



            <!-- 分析状态卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_analysis_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardMaxElevation="12dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@android:color/transparent"
                app:layout_constraintTop_toBottomOf="@id/card_image_display">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp"
                    android:background="@drawable/aqua_card_background_phone">

                    <TextView
                        android:id="@+id/tv_status_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⚡ 分析状态"
                        android:textColor="@color/aqua_secondary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- 进度指示器 -->
                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/progress_analysis"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:indeterminate="true"
                        android:visibility="visible"
                        app:indicatorColor="@color/aqua_secondary"
                        app:trackColor="@color/aqua_secondary_light"
                        app:layout_constraintTop_toBottomOf="@id/tv_status_label" />

                    <!-- 状态文本 -->
                    <TextView
                        android:id="@+id/tv_analysis_status"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="正在准备分析..."
                        android:textColor="@color/aqua_secondary_dark"
                        android:textSize="14sp"
                        android:gravity="center"
                        app:layout_constraintTop_toBottomOf="@id/progress_analysis" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>





            <!-- 错误信息卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_error_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:visibility="gone"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardMaxElevation="12dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@android:color/transparent"
                app:layout_constraintTop_toBottomOf="@id/card_analysis_status">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp"
                    android:background="@color/design_default_color_error">

                    <TextView
                        android:id="@+id/tv_error_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="❌ 分析失败"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_error_message"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        app:layout_constraintTop_toBottomOf="@id/tv_error_label"
                        tools:text="模型文件下载失败，请检查网络连接" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_retry"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="重试"
                        android:textColor="@color/design_default_color_error"
                        app:backgroundTint="@android:color/white"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_error_message" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>