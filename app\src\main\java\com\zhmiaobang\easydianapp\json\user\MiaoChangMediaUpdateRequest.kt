package com.zhmiaobang.easydianapp.json.user

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 苗场媒体信息更新请求数据类
 * 用于调用 api/miaochang/media/update/ 接口
 * 
 * 支持两种图片格式：
 * 1. base64编码字符串：用于新上传的图片
 * 2. URL字符串：用于已存在的图片
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Parcelize
data class MiaoChangMediaUpdateRequest(
    val id: Int? = null,
    val contactPerson: String? = null,
    val contactPhone: String? = null,
    val logo: String? = null,           // base64 或 URL
    val douyinQrCode: String? = null,   // base64 或 URL  
    val wechatQrCode: String? = null    // base64 或 URL
) : Parcelable

/**
 * 图片数据包装类
 * 用于区分base64和URL格式
 */
@Parcelize
data class ImageData(
    val type: ImageType,
    val data: String
) : Parcelable

/**
 * 图片类型枚举
 */
enum class ImageType {
    BASE64,     // base64编码的图片数据
    URL         // 图片URL地址
}
