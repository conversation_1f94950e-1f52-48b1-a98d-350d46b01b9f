<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.miacochang.feed.FeeddPointNfcWriterActivity">

    <!-- Material Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/nfc_writer_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:titleTextColor="@android:color/white"
        app:titleCentered="true"
        app:title="NFC写入"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@android:color/white" />

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Feed Point Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/hIndex_card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Feed Point Name -->
                    <TextView
                        android:id="@+id/tv_feed_point_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        tools:text="A1池-投料点01" />

                    <!-- Feed Point Details -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginTop="8dp">

                        <!-- Feed Point No -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="编号："
                                android:textColor="@color/hIndex_text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_feed_point_no"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:textColor="@color/hIndex_text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                tools:text="FP001" />

                        </LinearLayout>

                        <!-- Feed Period -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="喂养周期："
                                android:textColor="@color/hIndex_text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_feed_period"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:textColor="@color/hIndex_text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                tools:text="每日3次" />

                        </LinearLayout>

                        <!-- Status -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="状态："
                                android:textColor="@color/hIndex_text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_status"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:textColor="@color/hIndex_green"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                tools:text="有效" />

                        </LinearLayout>

                        <!-- Description -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="描述："
                                android:textColor="@color/hIndex_text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_description"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:textColor="@color/hIndex_text_primary"
                                android:textSize="14sp"
                                tools:text="主要投料点，负责A1池的日常投料" />

                        </LinearLayout>

                        <!-- NFC Code -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginTop="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="NFC码："
                                android:textColor="@color/hIndex_text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_nfc_code"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:textColor="@color/hIndex_text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:maxLines="2"
                                android:ellipsize="middle"
                                tools:text="NFC_1642780800123" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- NFC Status Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/hIndex_card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <!-- NFC Status Title -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="NFC状态"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- NFC Icon -->
                    <ImageView
                        android:id="@+id/iv_nfc_icon"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginBottom="16dp"
                        android:src="@drawable/ic_nfc_24"
                        android:contentDescription="NFC图标" />

                    <!-- NFC Status Text -->
                    <TextView
                        android:id="@+id/tv_nfc_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="14sp"
                        android:gravity="center"
                        android:maxLines="3"
                        tools:text="NFC已启用，请将NFC卡片靠近设备背面" />

                    <!-- Loading Indicator -->
                    <com.google.android.material.progressindicator.CircularProgressIndicator
                        android:id="@+id/progress_nfc_operation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        app:indicatorColor="@color/hIndex_blue" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="8dp">

                <!-- Write NFC Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_write_nfc"
                    style="@style/Widget.Material3.Button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="开始写入NFC"
                    android:textColor="@android:color/white"
                    android:backgroundTint="@color/hIndex_blue"
                    app:icon="@drawable/ic_nfc_24"
                    app:iconTint="@android:color/white" />

                <!-- Settings Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_nfc_settings"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="NFC设置"
                    android:textColor="@color/hIndex_blue"
                    android:visibility="gone"
                    app:strokeColor="@color/hIndex_blue"
                    app:icon="@drawable/ic_settings"
                    app:iconTint="@color/hIndex_blue" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>