<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.measure.SelectMeasureOnnxModelActivity">

    <!-- Material Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/select_onnx_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:titleTextColor="@android:color/white"
        app:titleCentered="true"
        app:title="选择测量模型"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@android:color/white"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar" />

    <!-- 内容区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize">

        <!-- 下拉刷新 -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="16dp"
                android:scrollbars="vertical"
                android:visibility="visible"
                tools:listitem="@layout/item_onnx_model" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <!-- 加载指示器 -->
        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="gone"
            app:indicatorColor="@color/hIndex_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Empty State Layout -->
        <LinearLayout
            android:id="@+id/empty_state_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="16dp"
                android:src="@drawable/ic_image_placeholder"
                android:alpha="0.5"
                android:contentDescription="空状态图标" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="暂无可用的测量模型"
                android:textColor="@color/hIndex_text_secondary"
                android:textSize="16sp"
                android:gravity="center" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>