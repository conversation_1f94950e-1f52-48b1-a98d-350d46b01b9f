<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/divider_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 顶部信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 手机号 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_phone_24"
                    android:tint="@color/primary_color"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tvPhone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold"
                    tools:text="13800138000" />

            </LinearLayout>

            <!-- 创建时间 -->
            <TextView
                android:id="@+id/tvCreateTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                tools:text="2024-01-15 14:30:25" />

        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginVertical="12dp"
            android:background="@color/divider_color" />

        <!-- 模型信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_model_24"
                android:tint="@color/secondary_color"
                android:layout_marginEnd="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="模型："
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tvModelName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="14sp"
                android:textColor="@color/text_primary"
                android:layout_marginStart="4dp"
                tools:text="虾苗计数模型 v1.0" />

        </LinearLayout>

        <!-- 检测参数信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/ic_settings"
                android:tint="@color/accent_color"
                android:layout_marginEnd="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="检测参数："
                android:textSize="11sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tvDetectionParams"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="11sp"
                android:textColor="@color/text_tertiary"
                android:layout_marginStart="4dp"
                tools:text="置信度: 0.25, NMS: 0.45" />

        </LinearLayout>

        <!-- 图片和计数信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical">

            <!-- 目标图片 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="处理结果："
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="4dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="60dp"
                    android:layout_height="80dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <ImageView
                        android:id="@+id/ivDestImage"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:background="@color/image_placeholder_bg"
                        tools:src="@drawable/ic_image_placeholder" />

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- 计数结果 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="16dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="计数结果"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tvCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="24sp"
                    android:textColor="@color/primary_color"
                    android:textStyle="bold"
                    tools:text="156" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="个"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

            <!-- 状态指示器 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginStart="16dp">

                <View
                    android:id="@+id/statusIndicator"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:background="@drawable/circle_shape"
                    android:backgroundTint="@color/success_color" />

                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="已完成"
                    android:textSize="10sp"
                    android:textColor="@color/success_color"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="12dp"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnViewDetails"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="查看详情"
                android:textSize="12sp"
                android:layout_marginEnd="8dp"
                style="@style/Widget.Material3.Button.TextButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="删除"
                android:textSize="12sp"
                android:textColor="@color/error_color"
                style="@style/Widget.Material3.Button.TextButton" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
