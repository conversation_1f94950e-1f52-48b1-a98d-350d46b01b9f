package com.zhmiaobang.easydianapp.json.onnx

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class OnnxModelJson(
    val classes: String,
    val createTime: String?=null,
    val description: String?=null,
    val icon: String?=null,
    val id: Int,
    val isPublic: Boolean,
    val localFilename: String,
    val miaochang: OnnxMiaochang?=null,
    val name: String,
    val primaryUrl: String,
    val secondUrl: String?=null,
    val shrimpCate: ShrimpCate,
    val status: Int,
    val updateTime: String?=null,
    val version: String,
    val versionCode: Int
): Parcelable