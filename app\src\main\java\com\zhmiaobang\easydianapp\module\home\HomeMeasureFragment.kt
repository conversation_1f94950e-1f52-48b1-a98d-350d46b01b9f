package com.zhmiaobang.easydianapp.module.home

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databases.MeasureLogManager
import com.zhmiaobang.easydianapp.databases.entity.MeasureLog
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson
import com.zhmiaobang.easydianapp.module.measure.MeasureLogListActivity
import com.zhmiaobang.easydianapp.module.measure.MeasureResultActivity
import com.zhmiaobang.easydianapp.module.measure.SelectMeasureOnnxModelActivity
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 测量模块Fragment
 *
 * <AUTHOR> 4.0 sonnet
 */
class HomeMeasureFragment : Fragment() {

    companion object {
        private const val TAG = "HomeMeasureFragment"
        private const val REQUEST_CODE_SELECT_MODEL = 1001

        // 支持的图片类型
        private val SUPPORTED_IMAGE_TYPES = arrayOf(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
        )

        // Toast消息常量
        private const val MSG_PHONE_REQUIRED = "请先输入电话号码"
        private const val MSG_MODEL_REQUIRED = "请先选择测量模型"
        private const val MSG_PERMISSION_REQUIRED = "需要权限才能访问相机或相册"
        private const val MSG_IMAGE_SUCCESS = "图片选择成功"
        private const val MSG_IMAGE_PATH_ERROR = "无法获取图片路径"

        fun newInstance() = HomeMeasureFragment()
    }

    // ViewModel
    private val viewModel: HomeMeasureViewModel by viewModels()

    // 数据库管理器
    private lateinit var measureLogManager: MeasureLogManager

    // UI组件
    private lateinit var cardMeasureRecords: androidx.cardview.widget.CardView
    private lateinit var tvSelectModel: TextView
    private lateinit var layoutSelectedModel: LinearLayout
    private lateinit var tvSelectedModelName: TextView
    private lateinit var tvSelectedModelCategory: TextView
    private lateinit var edMeasurePhone: TextInputEditText
    private lateinit var btnGallery: MaterialButton
    private lateinit var btnCamera: MaterialButton
    private lateinit var layoutSelectedImage: LinearLayout
    private lateinit var tvSelectedImagePath: TextView

    // 数据存储变量
    private var srcMeasurePath: String = ""
    private var currentPhotoFile: File? = null

    // Activity Result Launchers
    private lateinit var permissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var imagePickerLauncher: ActivityResultLauncher<Intent>
    private lateinit var cameraLauncher: ActivityResultLauncher<Uri>

    // 选择模型的ActivityResult处理器
    private val selectModelLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val selectedModel = result.data?.getParcelableExtra<OnnxModelJson>(
                SelectMeasureOnnxModelActivity.EXTRA_SELECTED_MODEL
            )

            selectedModel?.let {
                Log.d(TAG, "选中的模型: ${it.name}, 分类: ${it.shrimpCate.name}")
                viewModel.setSelectedModel(it)
                updateSelectedModelUI(it)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate")
        initializeLaunchers()
        initializeMeasureLogManager()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        Log.d(TAG, "onCreateView")
        return inflater.inflate(R.layout.fragment_home_measure, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "onViewCreated")

        // 初始化UI组件
        initializeViews(view)

        // 设置点击事件
        setupClickListeners()

        // 设置观察者
        setupObservers()

        // 恢复已选择的模型（如果有）
        restoreSelectedModel()
    }

    /**
     * 初始化测量日志管理器
     */
    private fun initializeMeasureLogManager() {
        measureLogManager = MeasureLogManager.getInstance(requireContext())
        Log.d(TAG, "MeasureLogManager 初始化完成")
    }

    /**
     * 初始化Activity Result Launchers
     */
    private fun initializeLaunchers() {
        // 权限请求launcher
        permissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            val allGranted = permissions.values.all { it }
            if (allGranted) {
                // 根据上次的操作决定打开相册还是相机
                if (::imagePickerLauncher.isInitialized) {
                    openGallery()
                } else {
                    openCamera()
                }
            } else {
                showToast(MSG_PERMISSION_REQUIRED)
            }
        }

        // 图片选择launcher
        imagePickerLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            handleImagePickerResult(result)
        }

        // 相机拍照launcher
        cameraLauncher = registerForActivityResult(
            ActivityResultContracts.TakePicture()
        ) { success ->
            handleCameraResult(success)
        }
    }

    /**
     * 初始化视图组件
     */
    private fun initializeViews(view: View) {
        cardMeasureRecords = view.findViewById(R.id.card_measure_records)
        tvSelectModel = view.findViewById(R.id.tv_select_model)
        layoutSelectedModel = view.findViewById(R.id.layout_selected_model)
        tvSelectedModelName = view.findViewById(R.id.tv_selected_model_name)
        tvSelectedModelCategory = view.findViewById(R.id.tv_selected_model_category)
        edMeasurePhone = view.findViewById(R.id.ed_measure_phone)
        btnGallery = view.findViewById(R.id.btn_gallery)
        btnCamera = view.findViewById(R.id.btn_camera)
        layoutSelectedImage = view.findViewById(R.id.layout_selected_image)
        tvSelectedImagePath = view.findViewById(R.id.tv_selected_image_path)

        Log.d(TAG, "所有视图组件初始化完成")
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 体长记录卡片点击事件
        cardMeasureRecords.setOnClickListener {
            Log.d(TAG, "点击体长记录卡片，准备跳转到 MeasureLogListActivity")
            navigateToMeasureLogList()
        }

        tvSelectModel.setOnClickListener {
            Log.d(TAG, "点击选择模型")
            navigateToModelSelection()
        }

        btnGallery.setOnClickListener {
            Log.d(TAG, "点击相册按钮")
            requestGalleryPermission()
        }

        btnCamera.setOnClickListener {
            Log.d(TAG, "点击拍照按钮")
            requestCameraPermission()
        }

        Log.d(TAG, "所有点击事件监听器设置完成")
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        viewModel.selectedModelObserver.observe(viewLifecycleOwner) { model ->
            Log.d(TAG, "观察到模型变化: ${model?.name}")
            updateSelectedModelUI(model)
        }

        viewModel.phoneNumberObserver.observe(viewLifecycleOwner) { phoneNumber ->
            Log.d(TAG, "观察到电话号码变化: $phoneNumber")
            if (!phoneNumber.isNullOrEmpty() && edMeasurePhone.text.toString() != phoneNumber) {
                edMeasurePhone.setText(phoneNumber)
            }
        }

        viewModel.srcMeasurePathObserver.observe(viewLifecycleOwner) { path ->
            Log.d(TAG, "观察到图片路径变化: $path")
            updateSelectedImageUI(path)
        }
    }

    /**
     * 恢复已选择的模型
     */
    private fun restoreSelectedModel() {
        val model = viewModel.getSelectedModel()
        if (model != null) {
            Log.d(TAG, "恢复已选择的模型: ${model.name}")
            updateSelectedModelUI(model)
        } else {
            Log.d(TAG, "没有已选择的模型")
            hideSelectedModelUI()
        }
    }

    /**
     * 导航到体长记录列表界面
     */
    private fun navigateToMeasureLogList() {
        try {
            Log.d(TAG, "开始创建 Intent 跳转到 MeasureLogListActivity")
            val intent = Intent(requireContext(), MeasureLogListActivity::class.java)
            Log.d(TAG, "Intent 创建成功，准备启动 Activity")
            startActivity(intent)
            Log.d(TAG, "成功启动 MeasureLogListActivity")
            showToast("正在打开体长记录...")
        } catch (e: Exception) {
            Log.e(TAG, "启动 MeasureLogListActivity 失败: ${e.message}", e)
            e.printStackTrace()
            showToast("打开体长记录失败，请重试")
        }
    }

    /**
     * 导航到模型选择界面
     */
    private fun navigateToModelSelection() {
        val intent = Intent(requireContext(), SelectMeasureOnnxModelActivity::class.java)
        selectModelLauncher.launch(intent)
    }

    /**
     * 更新选中模型的UI
     */
    private fun updateSelectedModelUI(model: OnnxModelJson?) {
        if (model != null) {
            tvSelectedModelName.text = model.name
            tvSelectedModelCategory.text = model.shrimpCate.name
            layoutSelectedModel.visibility = View.VISIBLE
            tvSelectModel.text = "更换测量模型"
        } else {
            hideSelectedModelUI()
        }
    }

    /**
     * 隐藏选中模型的UI
     */
    private fun hideSelectedModelUI() {
        layoutSelectedModel.visibility = View.GONE
        tvSelectModel.text = "点击选择测量模型"
    }

    // ==================== 权限处理方法 ====================

    /**
     * 请求相册权限
     */
    private fun requestGalleryPermission() {
        val permissions = getRequiredPermissionsForGallery()
        if (hasAllPermissions(permissions)) {
            openGallery()
        } else {
            permissionLauncher.launch(permissions)
        }
    }

    /**
     * 请求相机权限
     */
    private fun requestCameraPermission() {
        val permissions = getRequiredPermissionsForCamera()
        if (hasAllPermissions(permissions)) {
            openCamera()
        } else {
            permissionLauncher.launch(permissions)
        }
    }

    /**
     * 获取相册所需权限
     */
    private fun getRequiredPermissionsForGallery(): Array<String> {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                arrayOf(Manifest.permission.READ_MEDIA_IMAGES)
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            else -> {
                arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
        }
    }

    /**
     * 获取相机所需权限
     */
    private fun getRequiredPermissionsForCamera(): Array<String> {
        val permissions = mutableListOf<String>()
        permissions.add(Manifest.permission.CAMERA)

        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            else -> {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }

        return permissions.toTypedArray()
    }

    /**
     * 检查是否拥有所有权限
     */
    private fun hasAllPermissions(permissions: Array<String>): Boolean {
        return permissions.all { permission ->
            ContextCompat.checkSelfPermission(requireContext(), permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    // ==================== 图片选择方法 ====================

    /**
     * 打开相册
     */
    private fun openGallery() {
        try {
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI).apply {
                type = "image/*"
                putExtra(Intent.EXTRA_MIME_TYPES, SUPPORTED_IMAGE_TYPES)
            }
            imagePickerLauncher.launch(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开相册失败: ${e.message}", e)
            showToast("打开相册失败，请重试")
        }
    }

    /**
     * 打开相机
     */
    private fun openCamera() {
        try {
            currentPhotoFile = createImageFile()
            currentPhotoFile?.let { file ->
                val photoUri = FileProvider.getUriForFile(
                    requireContext(),
                    "${requireContext().packageName}.fileprovider",
                    file
                )
                cameraLauncher.launch(photoUri)
            }
        } catch (e: Exception) {
            Log.e(TAG, "打开相机失败: ${e.message}", e)
            showToast("打开相机失败，请重试")
        }
    }

    /**
     * 创建图片文件
     */
    private fun createImageFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFileName = "MEASURE_${timeStamp}_"
        val storageDir = File(requireContext().getExternalFilesDir(Environment.DIRECTORY_PICTURES), "Measure")

        if (!storageDir.exists()) {
            storageDir.mkdirs()
        }

        return File.createTempFile(imageFileName, ".jpg", storageDir)
    }

    // ==================== 结果处理方法 ====================

    /**
     * 处理图片选择结果
     */
    private fun handleImagePickerResult(result: androidx.activity.result.ActivityResult) {
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                try {
                    val realPath = getRealPathFromUri(uri)
                    if (realPath != null) {
                        srcMeasurePath = realPath
                        viewModel.setSrcMeasurePath(srcMeasurePath)
                        Log.d(TAG, "相册选择成功，图片路径: $srcMeasurePath")
                        showToast(MSG_IMAGE_SUCCESS)

                        // 图片选择成功后，处理测量流程
                        handleImageSelectionSuccess()
                    } else {
                        Log.e(TAG, "无法获取图片真实路径")
                        showToast(MSG_IMAGE_PATH_ERROR)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理相册图片失败: ${e.message}", e)
                    showToast("处理图片失败，请重试")
                }
            }
        }
    }

    /**
     * 处理相机拍照结果
     */
    private fun handleCameraResult(success: Boolean) {
        if (success && currentPhotoFile != null) {
            try {
                srcMeasurePath = currentPhotoFile!!.absolutePath
                viewModel.setSrcMeasurePath(srcMeasurePath)
                Log.d(TAG, "拍照成功，图片路径: $srcMeasurePath")
                showToast(MSG_IMAGE_SUCCESS)

                // 拍照成功后，处理测量流程
                handleImageSelectionSuccess()
            } catch (e: Exception) {
                Log.e(TAG, "处理拍照图片失败: ${e.message}", e)
                showToast("处理图片失败，请重试")
            }
        } else {
            Log.w(TAG, "拍照失败或被取消")
            currentPhotoFile?.delete()
            currentPhotoFile = null
        }
    }

    /**
     * 从Uri获取真实路径
     */
    private fun getRealPathFromUri(uri: Uri): String? {
        return try {
            val inputStream = requireContext().contentResolver.openInputStream(uri)
            inputStream?.use { input ->
                val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val fileName = "MEASURE_GALLERY_${timeStamp}.jpg"
                val storageDir = File(requireContext().getExternalFilesDir(Environment.DIRECTORY_PICTURES), "Measure")

                if (!storageDir.exists()) {
                    storageDir.mkdirs()
                }

                val outputFile = File(storageDir, fileName)
                outputFile.outputStream().use { output ->
                    input.copyTo(output)
                }

                outputFile.absolutePath
            }
        } catch (e: Exception) {
            Log.e(TAG, "复制图片文件失败: ${e.message}", e)
            null
        }
    }

    /**
     * 更新选中图片的UI
     */
    private fun updateSelectedImageUI(path: String?) {
        if (!path.isNullOrEmpty()) {
            tvSelectedImagePath.text = path
            layoutSelectedImage.visibility = View.VISIBLE
            srcMeasurePath = path
        } else {
            layoutSelectedImage.visibility = View.GONE
            srcMeasurePath = ""
        }
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    /**
     * 保存电话号码到ViewModel
     */
    private fun savePhoneNumber() {
        val phoneNumber = edMeasurePhone.text.toString().trim()
        if (phoneNumber.isNotEmpty()) {
            viewModel.setPhoneNumber(phoneNumber)
        }
    }

    override fun onPause() {
        super.onPause()
        // 保存电话号码
        savePhoneNumber()
    }

    // ==================== 测量流程处理方法 ====================

    /**
     * 处理图片选择成功后的测量流程
     *
     * 流程：
     * 1. 数据收集与验证
     * 2. 创建并保存 MeasureLog
     * 3. 页面跳转与数据传递
     */
    private fun handleImageSelectionSuccess() {
        Log.d(TAG, "开始处理图片选择成功后的测量流程")

        // 步骤1：数据收集与验证
        val validationResult = validateMeasureData()
        if (!validationResult.isValid) {
            Log.w(TAG, "数据验证失败: ${validationResult.errorMessage}")
            showToast(validationResult.errorMessage ?: "数据验证失败")
            return
        }

        val measureData = validationResult.data!!
        Log.d(TAG, "数据验证成功: 模型=${measureData.onnxModel.name}, 电话=${measureData.phone}, 图片=${measureData.imagePath}")

        // 步骤2：创建并保存 MeasureLog
        createAndSaveMeasureLog(measureData)
    }

    /**
     * 验证测量所需的数据
     *
     * @return 验证结果，包含是否有效和错误信息
     */
    private fun validateMeasureData(): ValidationResult {
        // 验证选中的模型
        val selectedModel = viewModel.getSelectedModel()
        if (selectedModel == null) {
            return ValidationResult(false, MSG_MODEL_REQUIRED, null)
        }

        // 验证电话号码
        val phoneNumber = edMeasurePhone.text.toString().trim()
        if (phoneNumber.isEmpty()) {
            return ValidationResult(false, MSG_PHONE_REQUIRED, null)
        }

        // 验证图片路径
        if (srcMeasurePath.isEmpty()) {
            return ValidationResult(false, MSG_IMAGE_PATH_ERROR, null)
        }

        // 验证图片文件是否存在
        val imageFile = File(srcMeasurePath)
        if (!imageFile.exists()) {
            return ValidationResult(false, "图片文件不存在，请重新选择", null)
        }

        // 所有验证通过
        val measureData = MeasureData(
            onnxModel = selectedModel,
            phone = phoneNumber,
            imagePath = srcMeasurePath
        )

        return ValidationResult(true, null, measureData)
    }

    /**
     * 创建并保存 MeasureLog 到数据库
     *
     * @param measureData 验证通过的测量数据
     */
    private fun createAndSaveMeasureLog(measureData: MeasureData) {
        Log.d(TAG, "开始创建并保存 MeasureLog")

        // 显示加载提示
        showToast("正在创建测量记录...")

        // 使用协程在后台线程创建 MeasureLog
        measureLogManager.createMeasureLogAsync(
            scope = lifecycleScope,
            onnxModelJson = measureData.onnxModel,
            phone = measureData.phone,
            imgSrc = measureData.imagePath,
            onSuccess = { logId ->
                Log.d(TAG, "MeasureLog 创建成功，ID: $logId")
                // 跳转到测量结果页面
                navigateToMeasureResult(logId)
            },
            onError = { errorMessage ->
                Log.e(TAG, "MeasureLog 创建失败: $errorMessage")
                showToast("创建测量记录失败: $errorMessage")
            }
        )
    }

    /**
     * 跳转到测量结果页面
     *
     * @param measureLogId 测量日志记录的 ID
     */
    private fun navigateToMeasureResult(measureLogId: Long) {
        Log.d(TAG, "跳转到测量结果页面，MeasureLog ID: $measureLogId")

        try {
            val intent = Intent(requireContext(), MeasureResultActivity::class.java).apply {
                putExtra(MeasureResultActivity.EXTRA_MEASURE_LOG_ID, measureLogId)
                // 也可以传递其他必要的数据
                putExtra(MeasureResultActivity.EXTRA_IMAGE_PATH, srcMeasurePath)
            }

            startActivity(intent)
            Log.d(TAG, "成功启动 MeasureResultActivity")

        } catch (e: Exception) {
            Log.e(TAG, "启动 MeasureResultActivity 失败: ${e.message}", e)
            showToast("启动测量结果页面失败，请重试")
        }
    }

    // ==================== 数据类定义 ====================

    /**
     * 验证结果数据类
     */
    private data class ValidationResult(
        val isValid: Boolean,
        val errorMessage: String?,
        val data: MeasureData?
    )

    /**
     * 测量数据类
     */
    private data class MeasureData(
        val onnxModel: OnnxModelJson,
        val phone: String,
        val imagePath: String
    )
}