<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="24dp">

    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="⚙️ 检测设置"
        android:textColor="@color/aqua_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 置信度阈值设置 -->
    <TextView
        android:id="@+id/tv_confidence_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="置信度阈值："
        android:textColor="@color/aqua_secondary_dark"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_dialog_title" />

    <TextView
        android:id="@+id/tv_confidence_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="0.25"
        android:textColor="@color/aqua_accent"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/tv_confidence_label"
        app:layout_constraintTop_toTopOf="@id/tv_confidence_label" />

    <SeekBar
        android:id="@+id/seekbar_confidence"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:max="100"
        android:progress="25"
        android:progressTint="@color/aqua_secondary"
        android:thumbTint="@color/aqua_accent"
        app:layout_constraintTop_toBottomOf="@id/tv_confidence_label" />

    <!-- NMS阈值设置 -->
    <TextView
        android:id="@+id/tv_nms_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="NMS阈值："
        android:textColor="@color/aqua_secondary_dark"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/seekbar_confidence" />

    <TextView
        android:id="@+id/tv_nms_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="0.45"
        android:textColor="@color/aqua_accent"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/tv_nms_label"
        app:layout_constraintTop_toTopOf="@id/tv_nms_label" />

    <SeekBar
        android:id="@+id/seekbar_nms"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:max="100"
        android:progress="45"
        android:progressTint="@color/aqua_secondary"
        android:thumbTint="@color/aqua_accent"
        app:layout_constraintTop_toBottomOf="@id/tv_nms_label" />

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal"
        android:gravity="end"
        app:layout_constraintTop_toBottomOf="@id/seekbar_nms">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:text="取消"
            android:textColor="@color/aqua_secondary"
            style="@style/Widget.Material3.Button.TextButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_apply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="应用并重新检测"
            android:textColor="@android:color/white"
            app:backgroundTint="@color/aqua_accent" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
