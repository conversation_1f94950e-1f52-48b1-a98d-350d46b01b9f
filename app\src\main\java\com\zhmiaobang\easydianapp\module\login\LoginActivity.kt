package com.zhmiaobang.easydianapp.module.login

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Bundle
import android.util.Base64
import android.util.Log
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import com.huawei.hms.hmsscankit.ScanUtil
import com.huawei.hms.ml.scan.HmsScan
import com.huawei.hms.ml.scan.HmsScanAnalyzerOptions
import com.zhmiaobang.easydianapp.databinding.ActivityLoginBinding
import com.zhmiaobang.easydianapp.init.ConfigTools
import com.zhmiaobang.easydianapp.init.MyApp
import com.zhmiaobang.easydianapp.json.login.LoginResultJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.module.home.HomeActivity
import com.zhmiaobang.easydianapp.viewmodel.login.LoginViewModel
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * 登录Activity - 集成华为ScanKit扫码功能
 * 优化版本 by Claude 4.0 sonnet
 */
class LoginActivity : BaseActivity() {
    private val viewModel: LoginViewModel by viewModels()
    companion object {
        private const val TAG = "LoginActivity"

        // 二维码格式常量
        private const val QR_PREFIX = "mb|login|"
        private const val QR_SUFFIX = "|end"

        // AES解密相关常量
        private const val AES_TRANSFORMATION = "AES/CBC/PKCS5Padding"
        private const val AES_ALGORITHM = "AES"
    }

    private val binding: ActivityLoginBinding by lazy { ActivityLoginBinding.inflate(layoutInflater) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        initializeUI()
        setupObservers()
    }

    override fun initializeUI() {
        super.initializeUI()

        // 设置扫码按钮点击事件
        binding.loginScanButton.setOnClickListener {
            startQRCodeScan()
        }

        // 检查是否有之前的扫码结果
        checkPreviousScanResult()
    }

    /**
     * 设置观察者监听 LoginViewModel 的结果
     */
    override fun setupObservers() {
        // 监听登录结果
        viewModel.loginResult.observe(this) { response ->
            handleLoginResponse(response)
        }

        // 监听错误
        viewModel.errorObserver.observe(this) { errorMessage ->
            handleLoginError(errorMessage)
        }

        // 监听完成状态
        viewModel.completeObserver.observe(this) { isComplete ->
            if (isComplete) {
                updateScanButtonState(false)
            }
        }
    }

    /**
     * 开始二维码扫描
     */
    private fun startQRCodeScan() {
        // 检查相机权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
            == PackageManager.PERMISSION_GRANTED) {
            launchScanActivity()
        } else {
            // 请求相机权限
            requestPermissionLauncher.launch(MyApp.permissionScan)
        }
    }

    /**
     * 启动华为ScanKit扫码界面
     */
    private fun launchScanActivity() {
        try {
            // 更新按钮状态
            updateScanButtonState(true)

            // 配置扫码选项 - 只扫描二维码
            val options = HmsScanAnalyzerOptions.Creator()
                .setHmsScanTypes(HmsScan.QRCODE_SCAN_TYPE)
                .setViewType(1) // 使用默认扫码界面
                .create()

            // 启动华为ScanKit扫码 - 使用正确的API
            ScanUtil.startScan(this, MyApp.REQUEST_CODE_SCAN_ONE, options)

        } catch (e: Exception) {
            Log.e(TAG, "启动扫码失败: ${e.message}", e)
            showToast("启动扫码失败，请重试")
            updateScanButtonState(false)
        }
    }

    /**
     * 处理Activity结果 - 华为ScanKit回调
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == MyApp.REQUEST_CODE_SCAN_ONE) {
            handleScanResult(resultCode, data)
        }
    }

    /**
     * 处理扫码结果
     */
    private fun handleScanResult(resultCode: Int, data: Intent?) {
        updateScanButtonState(false)

        when (resultCode) {
            RESULT_OK -> {
                // 扫码成功
                val hmsScan = data?.getParcelableExtra<HmsScan>(ScanUtil.RESULT)
                if (hmsScan != null && !hmsScan.originalValue.isNullOrEmpty()) {
                    processScanSuccess(hmsScan.originalValue)
                } else {
                    showToast("扫码结果为空，请重试")
                }
            }
            RESULT_CANCELED -> {
                // 用户取消扫码
                showToast("已取消扫码")
            }
            else -> {
                // 扫码失败
                showToast("扫码失败，请重试")
                Log.w(TAG, "扫码失败，结果码: $resultCode")
            }
        }
    }

    /**
     * 处理扫码成功
     */
    private fun processScanSuccess(scanResult: String) {
        try {
            Log.d(TAG, "扫码成功: $scanResult")

            // 保存原始扫码结果到MMKV
            saveScanResult(scanResult)

            // 验证并解密二维码内容
            if (validateQRCode(scanResult)) {
                // 获取解密后的员工代码
                val employeeCode = ConfigTools.getEmployeeCode()

                if (!employeeCode.isNullOrEmpty()) {
                    Log.d(TAG, "二维码解密成功，员工代码: $employeeCode，开始远程登录验证")

                    // 检查网络连接
                    if (!isNetworkAvailable()) {
                        showToast("网络连接不可用，请检查网络设置")
                        Log.w(TAG, "网络连接不可用")
                        return
                    }

                    // 显示登录中状态
                    updateScanButtonState(true, "🔐 登录验证中...")
                    showToast("正在验证登录...")

                    // 使用 LoginViewModel 向服务器发送登录请求
                    viewModel.login(employeeCode)
                } else {
                    showToast("获取员工代码失败")
                    Log.e(TAG, "员工代码为空")
                }
            } else {
                showToast("无效的员工二维码，请重新扫描")
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理扫码结果失败: ${e.message}", e)
            showToast("处理扫码结果失败，请重试")
        }
    }

    /**
     * 保存扫码结果到本地存储
     */
    private fun saveScanResult(scanResult: String) {
        try {
            ConfigTools.setScanResult(scanResult)
            Log.d(TAG, "扫码结果已保存到本地存储")
        } catch (e: Exception) {
            Log.e(TAG, "保存扫码结果失败: ${e.message}", e)
        }
    }

    /**
     * 验证并解密二维码内容
     * 格式：mb|login|{加密内容}|end
     * 解密后格式：员工代码|时间戳
     */
    private fun validateQRCode(qrContent: String): Boolean {
        try {
            Log.d(TAG, "开始验证二维码: $qrContent")

            // 1. 验证前缀和后缀
            if (!qrContent.startsWith(QR_PREFIX) || !qrContent.endsWith(QR_SUFFIX)) {
                Log.w(TAG, "二维码格式错误：前缀或后缀不匹配")
                showToast("二维码格式错误")
                return false
            }

            // 2. 提取加密内容
            val encryptedContent = qrContent.substring(
                QR_PREFIX.length,
                qrContent.length - QR_SUFFIX.length
            )

            if (encryptedContent.isBlank()) {
                Log.w(TAG, "二维码加密内容为空")
                showToast("二维码内容为空")
                return false
            }

            Log.d(TAG, "提取到加密内容: ${encryptedContent.take(20)}...")

            // 3. 解密内容
            val decryptedContent = decryptAESCBCBase64(encryptedContent, MyApp.ANDROID_ENCRYPT_KEY)
            if (decryptedContent == null) {
                Log.w(TAG, "解密失败")
                showToast("二维码解密失败")
                return false
            }

            Log.d(TAG, "解密成功: $decryptedContent")

            // 4. 解析解密后的内容：员工代码|时间戳
            val parts = decryptedContent.split("|")
            if (parts.size != 2) {
                Log.w(TAG, "解密后内容格式错误，期望格式：员工代码|时间戳")
                showToast("二维码内容格式错误")
                return false
            }

            val employeeCode = parts[0]
            val timestampStr = parts[1]

            // 5. 验证时间戳
            val timestamp = timestampStr.toLongOrNull()
            if (timestamp == null) {
                Log.w(TAG, "时间戳格式错误: $timestampStr")
                showToast("二维码时间戳格式错误")
                return false
            }

            if (!isTimestampValid(timestamp)) {
                Log.w(TAG, "二维码已过期，时间戳: $timestamp")
                showToast("二维码已过期，请重新获取")
                return false
            }

            // 6. 保存员工代码
            ConfigTools.setEmployeeCode(employeeCode)
            Log.d(TAG, "二维码验证成功，员工代码: $employeeCode")

            return true

        } catch (e: Exception) {
            Log.e(TAG, "验证解密二维码失败: ${e.message}", e)
            showToast("二维码验证失败")
            return false
        }
    }

    /**
     * AES CBC Base64 解密函数
     */
    private fun decryptAESCBCBase64(encryptedData: String, key: String): String? {
        try {
            // 1. Base64解码密钥
            val keyBytes = Base64.decode(key, Base64.DEFAULT)
            val secretKey = SecretKeySpec(keyBytes, AES_ALGORITHM)

            // 2. Base64解码加密数据
            val encryptedBytes = Base64.decode(encryptedData, Base64.DEFAULT)

            // 3. 提取IV（前16字节）和密文
            if (encryptedBytes.size < 16) {
                Log.w(TAG, "加密数据长度不足，无法提取IV")
                return null
            }

            val iv = encryptedBytes.sliceArray(0..15)
            val cipherText = encryptedBytes.sliceArray(16 until encryptedBytes.size)

            // 4. 初始化解密器
            val cipher = Cipher.getInstance(AES_TRANSFORMATION)
            val ivSpec = IvParameterSpec(iv)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec)

            // 5. 解密
            val decryptedBytes = cipher.doFinal(cipherText)
            val result = String(decryptedBytes, Charsets.UTF_8)

            Log.d(TAG, "AES解密成功")
            return result

        } catch (e: Exception) {
            Log.e(TAG, "AES解密失败: ${e.message}", e)

            // 尝试使用零IV解密（备用方案）
            return tryDecryptWithZeroIV(encryptedData, key)
        }
    }

    /**
     * 使用零IV尝试解密（备用方案）
     */
    private fun tryDecryptWithZeroIV(encryptedData: String, key: String): String? {
        try {
            Log.d(TAG, "尝试使用零IV解密")

            val keyBytes = Base64.decode(key, Base64.DEFAULT)
            val secretKey = SecretKeySpec(keyBytes, AES_ALGORITHM)
            val encryptedBytes = Base64.decode(encryptedData, Base64.DEFAULT)

            val cipher = Cipher.getInstance(AES_TRANSFORMATION)
            val zeroIV = ByteArray(16) // 全零IV
            val ivSpec = IvParameterSpec(zeroIV)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec)

            val decryptedBytes = cipher.doFinal(encryptedBytes)
            val result = String(decryptedBytes, Charsets.UTF_8)

            Log.d(TAG, "零IV解密成功")
            return result

        } catch (e: Exception) {
            Log.e(TAG, "零IV解密也失败: ${e.message}", e)
            return null
        }
    }

    /**
     * 验证时间戳是否有效（未过期）
     * 注意：timestamp是过期时间，不是生成时间
     */
    private fun isTimestampValid(timestamp: Long): Boolean {
        val currentTime = System.currentTimeMillis() / 1000 // 转换为秒

        Log.d(TAG, "时间戳验证 - 当前时间: $currentTime, 二维码过期时间: $timestamp")

        // 验证逻辑：当前时间应该小于过期时间
        // 如果当前时间 < 过期时间，则二维码有效
        // 如果当前时间 >= 过期时间，则二维码已过期
        val isValid = currentTime < timestamp

        if (isValid) {
            val remainingTime = timestamp - currentTime
            Log.d(TAG, "二维码有效，剩余有效时间: ${remainingTime}秒")
        } else {
            val expiredTime = currentTime - timestamp
            Log.d(TAG, "二维码已过期，过期时间: ${expiredTime}秒")
        }

        return isValid
    }

    /**
     * 检查之前的扫码结果
     */
    private fun checkPreviousScanResult() {
        val previousResult = ConfigTools.getScanResult()
        val previousEmployeeCode = ConfigTools.getEmployeeCode()
        if (!previousResult.isNullOrEmpty()) {
            val timestamp = ConfigTools.getScanTimestamp()
            Log.d(TAG, "发现之前的扫码结果: $previousResult, 员工代码: $previousEmployeeCode, 时间: $timestamp")
        }
    }

    /**
     * 处理登录响应结果
     */
    private fun handleLoginResponse(response: com.zhmiaobang.easydianapp.json.CommonResponseJson<LoginResultJson>) {
        try {
            Log.d(TAG, "收到登录响应: code=${response.code}, msg=${response.msg}")

            when (response.code) {
                200 -> {
                    // 登录成功
                    Log.d(TAG, "登录成功，服务器响应: ${response.results}")

                    // 保存 JWT token
                    response.results.let { loginResult ->
                        ConfigTools.setJwtToken(loginResult.jwtToken)
                        ConfigTools.setJwtTime()
                        ConfigTools.setUser(loginResult.user)
                        Log.d(TAG, "JWT Token 已保存: ${loginResult.jwtToken.token.take(20)}...")

                        // 保存用户信息
                        Log.d(TAG, "用户信息: ${loginResult.user.nickname ?: "未知用户"}")
                    }

                    showToast("登录成功！")

                    // 跳转到主界面
                    navigateToHome()
                }
                else -> {
                    // 登录失败
                    val errorMsg = response.msg.ifEmpty { "登录失败，请重试" }
                    Log.w(TAG, "登录失败: $errorMsg")
                    showToast("登录失败: $errorMsg")

                    // 清除保存的员工代码，允许重新扫码
                    ConfigTools.clearEmployeeCode()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理登录响应失败: ${e.message}", e)
            showToast("处理登录响应失败，请重试")
        }
    }

    /**
     * 处理登录错误
     */
    private fun handleLoginError(errorMessage: String) {
        Log.e(TAG, "登录请求出错: $errorMessage")
        showToast("登录请求失败: $errorMessage")

        // 清除保存的员工代码，允许重新扫码
        ConfigTools.clearEmployeeCode()
    }

    /**
     * 检查网络连接是否可用
     */
    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

        return networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
               networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
    }

    /**
     * 更新扫码按钮状态
     */
    private fun updateScanButtonState(isScanning: Boolean, customText: String? = null) {
        binding.loginScanButton.apply {
            if (isScanning) {
                text = customText ?: "🔍 扫描中..."
                isEnabled = false
            } else {
                text = "📷 启动扫码登录"
                isEnabled = true
            }
        }
    }

    /**
     * 跳转到主界面
     */
    private fun navigateToHome() {
        navigateToActivity<HomeActivity>(isFinished = true)
    }

    // ==================== BaseActivity权限回调重写 ====================

    override fun onPermissionGranted() {
        super.onPermissionGranted()
        Log.d(TAG, "相机权限已授予")
        launchScanActivity()
    }

    override fun onPermissionDenied() {
        super.onPermissionDenied()
        Log.w(TAG, "相机权限被拒绝")
        showToast("需要相机权限才能使用扫码功能")
    }
}