package com.zhmiaobang.easydianapp.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil


import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

open class BaseViewModel : ViewModel() {
    // 统一的错误处理
    val errorObserver: MutableLiveData<String> by lazy {
        MutableLiveData<String>()
    }

    // 完成状态的观察者
    val completeObserver: MutableLiveData<Boolean> by lazy {
        MutableLiveData<Boolean>()
    }

    /**
     * 启动一个协程，处理错误和完成状态。
     * @param block 协程体。
     * @param onError 错误处理回调。
     * @param onComplete 完成回调。
     */
    fun launch(
        block: suspend CoroutineScope.() -> Unit,
        onError: (e: Throwable) -> Unit = { e ->
            // 默认的错误处理：捕获异常并更新错误观察者
            ExceptionUtil.catchException(e)
            errorObserver.postValue(e.message ?: "发生未知错误")
        },
        onComplete: () -> Unit = {
            completeObserver.postValue(true)
        }
    ) {
        viewModelScope.launch(
            CoroutineExceptionHandler { _, throwable ->
                // 全局的异常处理
                onError(throwable)
            }
        ) {
            try {
                block()
            } finally {
                onComplete()
                completeObserver.postValue(true) // 发送完成信号
            }
        }
    }
}