<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:rippleColor="@color/primary_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Employee Avatar -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_employee_avatar"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_person"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            tools:src="@drawable/ic_person" />

        <!-- Employee Name -->
        <TextView
            android:id="@+id/tv_employee_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/status_indicator"
            app:layout_constraintStart_toEndOf="@+id/iv_employee_avatar"
            app:layout_constraintTop_toTopOf="@+id/iv_employee_avatar"
            tools:text="张师傅" />

        <!-- Employee Phone -->
        <TextView
            android:id="@+id/tv_employee_phone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@+id/status_indicator"
            app:layout_constraintStart_toEndOf="@+id/iv_employee_avatar"
            app:layout_constraintTop_toBottomOf="@+id/tv_employee_name"
            tools:text="13800138000" />

        <!-- Employee Role -->
        <TextView
            android:id="@+id/tv_employee_role"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_hint"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/status_indicator"
            app:layout_constraintStart_toEndOf="@+id/iv_employee_avatar"
            app:layout_constraintTop_toBottomOf="@+id/tv_employee_phone"
            tools:text="A区管理员" />

        <!-- Status Indicator -->
        <LinearLayout
            android:id="@+id/status_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_employee_avatar">

            <View
                android:id="@+id/status_dot"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:layout_marginEnd="4dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/hIndex_green"
                tools:backgroundTint="@color/hIndex_green" />

            <TextView
                android:id="@+id/tv_employee_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="在职" />

        </LinearLayout>

        <!-- Clickable Area for Avatar/Name (excluding QR code area) -->
        <View
            android:id="@+id/clickable_area"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/qr_code_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- QR Code Container (separate clickable area) -->
        <FrameLayout
            android:id="@+id/qr_code_container"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="@+id/iv_employee_avatar"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/iv_employee_qr_code"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_qr_code"
                android:contentDescription="查看员工二维码"
                app:tint="@color/primary_color" />

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
