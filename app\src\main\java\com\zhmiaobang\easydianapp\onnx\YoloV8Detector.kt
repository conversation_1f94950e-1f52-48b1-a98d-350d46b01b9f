﻿package com.zhmiaobang.easydianapp.onnx

import android.content.Context
import android.graphics.*
import android.util.Log
import ai.onnxruntime.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.nio.FloatBuffer
import androidx.core.graphics.scale

/**
 * YOLOv8 目标检测器 - 简化版本
 */
class YoloV8Detector private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "YoloV8Detector"
        private const val INPUT_SIZE = 640
        private const val DEFAULT_CONFIDENCE_THRESHOLD = 0.25f
        private const val DEFAULT_NMS_THRESHOLD = 0.45f

        // ImageNet 标准归一化参数（如果模型需要的话）
        private val IMAGENET_MEAN = floatArrayOf(0.485f, 0.456f, 0.406f)
        private val IMAGENET_STD = floatArrayOf(0.229f, 0.224f, 0.225f)

        // YOLO 通常使用简单的 0-1 归一化，但保留选项以备需要
        private const val USE_IMAGENET_NORMALIZATION = false
        
        @Volatile
        private var INSTANCE: YoloV8Detector? = null
        
        fun getInstance(context: Context): YoloV8Detector {
            return INSTANCE ?: synchronized(this) {
                val instance = YoloV8Detector(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    private var ortSession: OrtSession? = null
    private var ortEnvironment: OrtEnvironment? = null
    private var classes: Array<String> = emptyArray()

    // 可调节的阈值参数
    private var confidenceThreshold: Float = DEFAULT_CONFIDENCE_THRESHOLD
    private var nmsThreshold: Float = DEFAULT_NMS_THRESHOLD
    
    data class Detection(
        val bbox: RectF,
        val confidence: Float,
        val classId: Int,
        val className: String
    )
    
    data class AnalysisResult(
        val detections: List<Detection>,
        val count: Int,
        val processedBitmap: Bitmap,
        val inferenceTime: Long,
        val detectionCoordinates: FloatArray? = null   // [left1, top1, right1, bottom1, left2, top2, right2, bottom2, ...] 所有有效检测框坐标
    )
    
    suspend fun initializeModel(modelPath: String, classNames: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "初始化ONNX模型: $modelPath")
            classes = classNames.split(",").map { it.trim() }.toTypedArray()
            ortEnvironment = OrtEnvironment.getEnvironment()
            ortSession = ortEnvironment!!.createSession(modelPath)
            Log.d(TAG, "ONNX模型初始化成功")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "初始化失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    suspend fun testInference(): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "执行测试推理")
            Result.success("测试推理成功")
        } catch (e: Exception) {
            Log.e(TAG, "测试推理失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    // 用于存储 letterbox 预处理的参数
    private data class LetterboxInfo(
        val scale: Float,
        val padX: Int,
        val padY: Int,
        val newWidth: Int,
        val newHeight: Int
    )

    suspend fun detectObjects(bitmap: Bitmap): Result<AnalysisResult> = withContext(Dispatchers.IO) {
        try {
            val session = ortSession ?: throw Exception("模型未初始化")
            val startTime = System.currentTimeMillis()

            Log.d(TAG, "开始ONNX推理，原始图片尺寸: ${bitmap.width}x${bitmap.height}")

            // 使用 letterbox 预处理图片
            val (letterboxBitmap, letterboxInfo) = createLetterboxImage(bitmap)
            val inputBuffer = bitmapToFloatBuffer(letterboxBitmap)

            // 释放 letterbox 处理后的临时bitmap
            letterboxBitmap.recycle()

            // 创建输入张量
            val inputTensor = OnnxTensor.createTensor(
                ortEnvironment,
                inputBuffer,
                longArrayOf(1, 3, INPUT_SIZE.toLong(), INPUT_SIZE.toLong())
            )

            // 执行推理
            val inputs = mapOf("images" to inputTensor)
            val outputs = session.run(inputs)

            // 处理输出 - 使用 letterbox 信息进行坐标转换
            val detections = processModelOutput(outputs, bitmap.width, bitmap.height, letterboxInfo)

            // 释放ONNX资源
            inputTensor.close()
            outputs.close()

            // 提取所有有效检测框的坐标（在检测阶段完成，避免保存时重复计算）
            val detectionCoordinates = extractDetectionCoordinates(detections)

            // 绘制结果 - 在原始尺寸的图片上绘制
            val processedBitmap = drawDetections(bitmap.copy(Bitmap.Config.ARGB_8888, true), detections)
            val inferenceTime = System.currentTimeMillis() - startTime

            Log.d(TAG, "ONNX推理完成: 原始图片${bitmap.width}x${bitmap.height}, 处理后图片${processedBitmap.width}x${processedBitmap.height}, 检测到${detections.size}个对象, 耗时${inferenceTime}ms")

            if (detectionCoordinates != null) {
                Log.d(TAG, "检测坐标数组: ${detectionCoordinates.size}个坐标值 (${detectionCoordinates.size / 4}个检测框)")
            }

            val result = AnalysisResult(detections, detections.size, processedBitmap, inferenceTime, detectionCoordinates)
            Result.success(result)

        } catch (e: Exception) {
            Log.e(TAG, "ONNX推理失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 设置置信度阈值
     * @param threshold 置信度阈值，范围 [0.0, 1.0]
     */
    fun setConfidenceThreshold(threshold: Float) {
        confidenceThreshold = kotlin.math.max(0.0f, kotlin.math.min(1.0f, threshold))
        Log.d(TAG, "置信度阈值设置为: $confidenceThreshold")
    }

    /**
     * 设置NMS阈值
     * @param threshold NMS阈值，范围 [0.0, 1.0]
     */
    fun setNmsThreshold(threshold: Float) {
        nmsThreshold = kotlin.math.max(0.0f, kotlin.math.min(1.0f, threshold))
        Log.d(TAG, "NMS阈值设置为: $nmsThreshold")
    }

    /**
     * 同时设置两个阈值
     * @param confThreshold 置信度阈值
     * @param nmsThreshold NMS阈值
     */
    fun setThresholds(confThreshold: Float, nmsThreshold: Float) {
        setConfidenceThreshold(confThreshold)
        setNmsThreshold(nmsThreshold)
    }

    /**
     * 获取当前置信度阈值
     */
    fun getConfidenceThreshold(): Float = confidenceThreshold

    /**
     * 获取当前NMS阈值
     */
    fun getNmsThreshold(): Float = nmsThreshold

    /**
     * 重置阈值为默认值
     */
    fun resetThresholds() {
        confidenceThreshold = DEFAULT_CONFIDENCE_THRESHOLD
        nmsThreshold = DEFAULT_NMS_THRESHOLD
        Log.d(TAG, "阈值重置为默认值: conf=$confidenceThreshold, nms=$nmsThreshold")
    }

    /**
     * 获取当前检测器的配置信息
     */
    fun getDetectorInfo(): String {
        return "YoloV8Detector配置:\n" +
                "- 输入尺寸: ${INPUT_SIZE}x${INPUT_SIZE}\n" +
                "- 置信度阈值: $confidenceThreshold\n" +
                "- NMS阈值: $nmsThreshold\n" +
                "- ImageNet归一化: $USE_IMAGENET_NORMALIZATION\n" +
                "- 类别数: ${classes.size}\n" +
                "- 模型状态: ${if (ortSession != null) "已加载" else "未加载"}"
    }

    /**
     * 验证检测器状态
     */
    fun validateDetectorState(): Boolean {
        val isValid = ortSession != null && ortEnvironment != null && classes.isNotEmpty()
        Log.d(TAG, "检测器状态验证: $isValid (session=${ortSession != null}, env=${ortEnvironment != null}, classes=${classes.size})")
        return isValid
    }

    /**
     * 测试坐标转换逻辑
     */
    fun testCoordinateTransform(originalWidth: Int, originalHeight: Int) {
        val scale = minOf(INPUT_SIZE.toFloat() / originalWidth, INPUT_SIZE.toFloat() / originalHeight)
        val newWidth = (originalWidth * scale).toInt()
        val newHeight = (originalHeight * scale).toInt()
        val padX = (INPUT_SIZE - newWidth) / 2
        val padY = (INPUT_SIZE - newHeight) / 2

        val letterboxInfo = LetterboxInfo(scale, padX, padY, newWidth, newHeight)

        Log.d(TAG, "坐标转换测试 - 原始图像: ${originalWidth}x${originalHeight}")
        Log.d(TAG, "Letterbox信息: scale=${letterboxInfo.scale}, pad=(${letterboxInfo.padX},${letterboxInfo.padY}), 新尺寸=${letterboxInfo.newWidth}x${letterboxInfo.newHeight}")

        // 测试几个关键点的坐标转换
        val testPoints = listOf(
            Pair(320f, 160f), // letterbox 中心上方
            Pair(320f, 320f), // letterbox 中心
            Pair(320f, 480f)  // letterbox 中心下方
        )

        for ((x, y) in testPoints) {
            val originalX = (x - letterboxInfo.padX) / letterboxInfo.scale
            val originalY = (y - letterboxInfo.padY) / letterboxInfo.scale
            Log.d(TAG, "测试点: letterbox($x,$y) -> 原始(${originalX},${originalY})")
        }
    }

    /**
     * 创建 letterbox 预处理图像
     * 保持原始图像的长宽比，用黑色填充到目标尺寸
     */
    private fun createLetterboxImage(bitmap: Bitmap): Pair<Bitmap, LetterboxInfo> {
        val originalWidth = bitmap.width
        val originalHeight = bitmap.height

        // 计算缩放比例，保持长宽比
        val scale = minOf(
            INPUT_SIZE.toFloat() / originalWidth,
            INPUT_SIZE.toFloat() / originalHeight
        )

        // 计算缩放后的新尺寸
        val newWidth = (originalWidth * scale).toInt()
        val newHeight = (originalHeight * scale).toInt()

        // 计算居中放置的偏移量
        val padX = (INPUT_SIZE - newWidth) / 2
        val padY = (INPUT_SIZE - newHeight) / 2

        Log.d(TAG, "Letterbox预处理: 原始${originalWidth}x${originalHeight} -> 缩放${newWidth}x${newHeight}, scale=$scale, pad=($padX,$padY)")

        // 创建黑色背景的目标图像
        val letterboxBitmap = Bitmap.createBitmap(INPUT_SIZE, INPUT_SIZE, Bitmap.Config.ARGB_8888)
        letterboxBitmap.eraseColor(Color.BLACK)

        // 使用高质量缩放原始图像
        val scaledBitmap = Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)

        // 将缩放后的图像绘制到黑色背景上
        val canvas = Canvas(letterboxBitmap)
        canvas.drawBitmap(scaledBitmap, padX.toFloat(), padY.toFloat(), null)

        // 释放临时的缩放图像
        scaledBitmap.recycle()

        val letterboxInfo = LetterboxInfo(scale, padX, padY, newWidth, newHeight)

        return Pair(letterboxBitmap, letterboxInfo)
    }

    /**
     * 将Bitmap转换为FloatBuffer
     */
    private fun bitmapToFloatBuffer(bitmap: Bitmap): FloatBuffer {
        val buffer = FloatBuffer.allocate(3 * INPUT_SIZE * INPUT_SIZE)
        val pixels = IntArray(INPUT_SIZE * INPUT_SIZE)
        bitmap.getPixels(pixels, 0, INPUT_SIZE, 0, 0, INPUT_SIZE, INPUT_SIZE)

        val area = INPUT_SIZE * INPUT_SIZE

        for (i in pixels.indices) {
            val pixel = pixels[i]
            var r = (pixel shr 16 and 0xFF) / 255.0f
            var g = (pixel shr 8 and 0xFF) / 255.0f
            var b = (pixel and 0xFF) / 255.0f

            // 如果启用 ImageNet 归一化
            if (USE_IMAGENET_NORMALIZATION) {
                r = (r - IMAGENET_MEAN[0]) / IMAGENET_STD[0]
                g = (g - IMAGENET_MEAN[1]) / IMAGENET_STD[1]
                b = (b - IMAGENET_MEAN[2]) / IMAGENET_STD[2]
            }

            buffer.put(i, r)
            buffer.put(i + area, g)
            buffer.put(i + area * 2, b)
        }

        buffer.rewind()
        return buffer
    }

    /**
     * 处理模型输出 - 基于YOLOv8格式，支持 letterbox 坐标转换
     */
    private fun processModelOutput(outputs: OrtSession.Result, originalWidth: Int, originalHeight: Int, letterboxInfo: LetterboxInfo): List<Detection> {
        val output = outputs.get(0) as OnnxTensor
        val outputShape = output.info.shape
        val outputData = output.floatBuffer

        Log.d(TAG, "模型输出形状: ${outputShape.contentToString()}")
        Log.d(TAG, "输出数据容量: ${outputData.capacity()}")

        val detections = mutableListOf<Detection>()

        try {
            // 解析YOLOv8输出格式
            if (outputShape.size == 3) {
                val batchSize = outputShape[0].toInt() // 通常是1
                val features = outputShape[1].toInt()  // 特征数 (4 + 类别数)
                val numBoxes = outputShape[2].toInt()  // 检测框数量 (通常8400)

                Log.d(TAG, "YOLOv8格式: batch=$batchSize, features=$features, boxes=$numBoxes")

                // 计算类别数量
                val numClasses = features - 4 // 减去4个坐标值
                val actualClasses = minOf(numClasses, classes.size)

                Log.d(TAG, "类别数: 模型=$numClasses, 配置=${classes.size}, 使用=$actualClasses")

                // 处理每个检测框
                for (i in 0 until numBoxes) {
                    // YOLOv8输出格式: [x, y, w, h, class1_prob, class2_prob, ...]
                    val x = outputData.get(i)                    // 中心点x
                    val y = outputData.get(numBoxes + i)         // 中心点y
                    val w = outputData.get(2 * numBoxes + i)     // 宽度
                    val h = outputData.get(3 * numBoxes + i)     // 高度

                    // 找到最高概率的类别
                    var maxProb = 0f
                    var maxClassId = 0

                    for (classId in 0 until actualClasses) {
                        val prob = outputData.get((4 + classId) * numBoxes + i)
                        if (prob > maxProb) {
                            maxProb = prob
                            maxClassId = classId
                        }
                    }

                    // 如果置信度超过阈值，添加检测结果
                    if (maxProb > confidenceThreshold) {
                        // 使用 letterbox 信息进行坐标转换
                        // YOLO 输出的坐标是相对于 640x640 letterbox 图像的
                        // 需要转换回原始图像坐标系
                        val originalCenterX = (x - letterboxInfo.padX) / letterboxInfo.scale
                        val originalCenterY = (y - letterboxInfo.padY) / letterboxInfo.scale
                        val detectionWidth = w / letterboxInfo.scale
                        val detectionHeight = h / letterboxInfo.scale

                        val left = originalCenterX - detectionWidth / 2
                        val top = originalCenterY - detectionHeight / 2
                        val right = originalCenterX + detectionWidth / 2
                        val bottom = originalCenterY + detectionHeight / 2

                        // 边界检查
                        val bbox = RectF(
                            kotlin.math.max(0f, left),
                            kotlin.math.max(0f, top),
                            kotlin.math.min(originalWidth.toFloat(), right),
                            kotlin.math.min(originalHeight.toFloat(), bottom)
                        )

                        val detection = Detection(
                            bbox = bbox,
                            confidence = maxProb,
                            classId = maxClassId,
                            className = if (maxClassId < classes.size) classes[maxClassId] else "Class_$maxClassId"
                        )

                        detections.add(detection)

                        // 调试信息：记录前几个检测结果
                        if (detections.size <= 5) {
                            Log.d(TAG, "检测结果[${detections.size}]: " +
                                    "原始YOLO坐标=($x,$y,$w,$h), " +
                                    "letterbox信息=(scale=${letterboxInfo.scale}, pad=${letterboxInfo.padX},${letterboxInfo.padY}), " +
                                    "转换后坐标=($originalCenterX,$originalCenterY,$detectionWidth,$detectionHeight), " +
                                    "bbox=$bbox, conf=$maxProb, class=${detection.className}")
                        }

                        // 限制检测数量，避免过多结果
                        if (detections.size >= 50) break
                    }
                }

                Log.d(TAG, "原始检测结果: ${detections.size}个")

                // 简单的NMS处理
                val finalDetections = applySimpleNMS(detections)
                Log.d(TAG, "NMS后检测结果: ${finalDetections.size}个")

                return finalDetections
            } else {
                Log.w(TAG, "不支持的输出格式: ${outputShape.contentToString()}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理模型输出失败: ${e.message}", e)
        }

        return detections
    }

    /**
     * 提取所有有效检测框的坐标数据
     *
     * @param detections 检测结果列表
     * @return FloatArray? 返回坐标数组 [left1, top1, right1, bottom1, left2, top2, right2, bottom2, ...]
     */
    private fun extractDetectionCoordinates(detections: List<Detection>): FloatArray? {
        if (detections.isEmpty()) {
            Log.d(TAG, "没有检测结果，跳过坐标提取")
            return null
        }

        val coordinates = mutableListOf<Float>()

        detections.forEachIndexed { index, detection ->
            val bbox = detection.bbox

            // 添加检测框的四个坐标值：left, top, right, bottom
            coordinates.add(bbox.left)
            coordinates.add(bbox.top)
            coordinates.add(bbox.right)
            coordinates.add(bbox.bottom)

            Log.d(TAG, "检测框[$index] 坐标: (${String.format("%.2f", bbox.left)}, ${String.format("%.2f", bbox.top)}) - (${String.format("%.2f", bbox.right)}, ${String.format("%.2f", bbox.bottom)})")
        }

        Log.d(TAG, "坐标提取完成: 共${detections.size}个检测框，${coordinates.size}个坐标值")
        return coordinates.toFloatArray()
    }

    /**
     * 简单的NMS处理
     */
    private fun applySimpleNMS(detections: List<Detection>): List<Detection> {
        if (detections.isEmpty()) return detections

        val sortedDetections = detections.sortedByDescending { it.confidence }
        val selectedDetections = mutableListOf<Detection>()

        for (detection in sortedDetections) {
            var shouldKeep = true

            for (selected in selectedDetections) {
                if (detection.classId == selected.classId) {
                    val iou = calculateIoU(detection.bbox, selected.bbox)
                    if (iou > nmsThreshold) {
                        shouldKeep = false
                        break
                    }
                }
            }

            if (shouldKeep) {
                selectedDetections.add(detection)
            }
        }

        return selectedDetections
    }

    /**
     * 计算IoU
     */
    private fun calculateIoU(box1: RectF, box2: RectF): Float {
        val intersectionLeft = kotlin.math.max(box1.left, box2.left)
        val intersectionTop = kotlin.math.max(box1.top, box2.top)
        val intersectionRight = kotlin.math.min(box1.right, box2.right)
        val intersectionBottom = kotlin.math.min(box1.bottom, box2.bottom)

        if (intersectionLeft >= intersectionRight || intersectionTop >= intersectionBottom) {
            return 0f
        }

        val intersectionArea = (intersectionRight - intersectionLeft) * (intersectionBottom - intersectionTop)
        val box1Area = (box1.right - box1.left) * (box1.bottom - box1.top)
        val box2Area = (box2.right - box2.left) * (box2.bottom - box2.top)
        val unionArea = box1Area + box2Area - intersectionArea

        return if (unionArea > 0) intersectionArea / unionArea else 0f
    }

    private fun drawDetections(bitmap: Bitmap, detections: List<Detection>): Bitmap {
        Log.d(TAG, "开始绘制检测结果，输入图片尺寸: ${bitmap.width}x${bitmap.height}, 检测数量: ${detections.size}")

        // 确保bitmap是可变的
        val mutableBitmap = if (bitmap.isMutable) {
            bitmap
        } else {
            bitmap.copy(Bitmap.Config.ARGB_8888, true)
        }

        Log.d(TAG, "绘制用图片尺寸: ${mutableBitmap.width}x${mutableBitmap.height}, 可变性: ${mutableBitmap.isMutable}")

        val canvas = Canvas(mutableBitmap)

        // 使用更明显的绘制样式
        val strokePaint = Paint().apply {
            style = Paint.Style.STROKE
            strokeWidth = 12f
            color = Color.RED
            isAntiAlias = true
        }

        val fillPaint = Paint().apply {
            style = Paint.Style.FILL
            color = Color.argb(80, 255, 0, 0) // 半透明红色填充
            isAntiAlias = true
        }

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = 60f
            isAntiAlias = true
            setShadowLayer(4f, 2f, 2f, Color.BLACK)
        }

        val backgroundPaint = Paint().apply {
            style = Paint.Style.FILL
            color = Color.argb(180, 255, 0, 0) // 半透明红色背景
        }

        for (index in detections.indices) {
            val detection = detections[index]
            Log.d(TAG, "绘制检测[$index]: bbox=${detection.bbox}, confidence=${detection.confidence}, class=${detection.className}")

            // 验证边界框是否有效
            val bbox = detection.bbox
            if (bbox.left >= bbox.right || bbox.top >= bbox.bottom) {
                Log.w(TAG, "无效的边界框: $bbox")
                continue
            }

            // 确保边界框在图片范围内
            val clampedBbox = RectF(
                kotlin.math.max(0f, kotlin.math.min(bitmap.width.toFloat(), bbox.left)),
                kotlin.math.max(0f, kotlin.math.min(bitmap.height.toFloat(), bbox.top)),
                kotlin.math.max(0f, kotlin.math.min(bitmap.width.toFloat(), bbox.right)),
                kotlin.math.max(0f, kotlin.math.min(bitmap.height.toFloat(), bbox.bottom))
            )

            Log.d(TAG, "限制后的边界框: $clampedBbox")

            // 绘制半透明填充
            canvas.drawRect(clampedBbox, fillPaint)

            // 绘制边框
            canvas.drawRect(clampedBbox, strokePaint)

            // 绘制标签
            val label = "${detection.className} ${(detection.confidence * 100).toInt()}%"
            val textBounds = android.graphics.Rect()
            textPaint.getTextBounds(label, 0, label.length, textBounds)

            // 标签背景
            val labelLeft = clampedBbox.left
            val labelTop = clampedBbox.top - textBounds.height() - 20f
            val labelRight = labelLeft + textBounds.width() + 20f
            val labelBottom = clampedBbox.top

            canvas.drawRect(labelLeft, labelTop, labelRight, labelBottom, backgroundPaint)

            // 标签文字
            canvas.drawText(
                label,
                labelLeft + 10f,
                labelBottom - 10f,
                textPaint
            )

            Log.d(TAG, "检测[$index]绘制完成")
        }

        Log.d(TAG, "所有检测结果绘制完成，最终图片尺寸: ${mutableBitmap.width}x${mutableBitmap.height}")
        return mutableBitmap
    }
    
    fun cleanup() {
        ortSession?.close()
        ortEnvironment?.close()
        ortSession = null
        ortEnvironment = null
    }
}
