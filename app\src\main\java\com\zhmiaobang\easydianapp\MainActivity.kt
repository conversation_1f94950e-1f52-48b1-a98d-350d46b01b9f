package com.zhmiaobang.easydianapp

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.zhmiaobang.easydianapp.adapter.ImagePagerAdapter
import com.zhmiaobang.easydianapp.databinding.ActivityMainBinding
import com.zhmiaobang.easydianapp.init.ConfigTools
import com.zhmiaobang.easydianapp.json.deviceJson.DeviceJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.model.ImageItem
import com.zhmiaobang.easydianapp.module.home.HomeActivity
import com.zhmiaobang.easydianapp.module.login.LoginActivity
import kotlinx.coroutines.launch

/**
 * 主启动Activity - 实现完整的启动流程
 * 包含License检查、用户认证检查、JWT Token刷新等功能
 * 优化版本 by Claude 4.0 sonnet
 */
class MainActivity : BaseActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var imagePagerAdapter: ImagePagerAdapter
    private val imageItems = mutableListOf<ImageItem>()

    // 启动流程状态枚举
    private enum class StartupState {
        CHECKING_LICENSE,
        CHECKING_AUTH,
        REFRESHING_TOKEN,
        NAVIGATING,
        COMPLETED
    }

    private var currentState = StartupState.CHECKING_LICENSE

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化UI组件
        initializeUI()
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()

        // 设置状态栏高度
        setupStatusBar()

        // 初始化图片数据
        initializeImageData()

        // 设置ViewPager2
        setupViewPager()

        // 设置下拉刷新
        setupSwipeRefresh()

        logAndUpdateStatus("UI组件初始化完成")

        // 开始启动流程
        initializeStartupFlow()
    }

    /**
     * 设置状态栏
     */
    private fun setupStatusBar() {
        val statusBarHeight = getStatusBarHeight()
        binding.statusBarPlaceholder.layoutParams.height = statusBarHeight
    }

    /**
     * 获取状态栏高度
     */
    private fun getStatusBarHeight(): Int {
        var result = 0
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    /**
     * 初始化图片数据
     */
    private fun initializeImageData() {
        imageItems.clear()
        imageItems.addAll(
            listOf(
                ImageItem(
                    R.mipmap.ic_launcher,
                    "欢迎使用",
                    "智能化的苗场管理平台"
                ),
                ImageItem(
                    R.mipmap.ic_launcher_round,
                    "高效管理",
                    "提升苗场生产效率"
                ),
                ImageItem(
                    R.mipmap.ic_launcher,
                    "数据驱动",
                    "基于数据的决策支持"
                )
            )
        )
    }

    /**
     * 设置ViewPager2
     */
    private fun setupViewPager() {
        imagePagerAdapter = ImagePagerAdapter(imageItems)
        binding.viewPager.adapter = imagePagerAdapter

        // 设置页面指示器
        setupDotsIndicator()

        // 设置页面变化监听
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                updateDotsIndicator(position)
            }
        })
    }

    /**
     * 设置页面指示器
     */
    private fun setupDotsIndicator() {
        binding.dotsIndicator.removeAllViews()

        for (i in imageItems.indices) {
            val dot = ImageView(this)
            val params = LinearLayout.LayoutParams(
                resources.getDimensionPixelSize(R.dimen.dot_size),
                resources.getDimensionPixelSize(R.dimen.dot_size)
            )
            params.setMargins(
                resources.getDimensionPixelSize(R.dimen.dot_margin),
                0,
                resources.getDimensionPixelSize(R.dimen.dot_margin),
                0
            )
            dot.layoutParams = params
            dot.setImageResource(R.drawable.dot_inactive)
            binding.dotsIndicator.addView(dot)
        }

        // 设置第一个为激活状态
        if (imageItems.isNotEmpty()) {
            updateDotsIndicator(0)
        }
    }

    /**
     * 更新页面指示器
     */
    private fun updateDotsIndicator(position: Int) {
        for (i in 0 until binding.dotsIndicator.childCount) {
            val dot = binding.dotsIndicator.getChildAt(i) as ImageView
            if (i == position) {
                dot.setImageResource(R.drawable.dot_active)
            } else {
                dot.setImageResource(R.drawable.dot_inactive)
            }
        }
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setColorSchemeColors(
            ContextCompat.getColor(this, R.color.primary_color),
            ContextCompat.getColor(this, R.color.secondary_color)
        )

        binding.swipeRefreshLayout.setOnRefreshListener {
            Log.d(TAG, "用户触发下拉刷新")
            refreshStartupFlow()
        }
    }

    /**
     * 刷新启动流程
     */
    private fun refreshStartupFlow() {
        currentState = StartupState.CHECKING_LICENSE
        initializeStartupFlow()
    }

    /**
     * 初始化启动流程
     */
    private fun initializeStartupFlow() {
        logAndUpdateStatus("开始应用启动流程")

        // 开始启动流程
        startLicenseCheck()
    }



    /**
     * 第一阶段：License 检查
     */
    private fun startLicenseCheck() {
        currentState = StartupState.CHECKING_LICENSE
        logAndUpdateStatus("检查License协议状态")

        val isLicenseAgreed = ConfigTools.getLicense()
        Log.d(TAG, "License状态检查结果: $isLicenseAgreed")

        if (isLicenseAgreed) {
            logAndUpdateStatus("License已同意，继续认证检查")
            startAuthCheck()
        } else {
            logAndUpdateStatus("License未同意，显示协议对话框")
            showLicenseDialog()
        }
    }

    /**
     * 显示License授权对话框
     */
    private fun showLicenseDialog() {
        Log.d(TAG, "显示License授权对话框")
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.license_title))
            .setMessage(getString(R.string.license_content))
            .setPositiveButton(getString(R.string.license_agree)) { _, _ ->
                Log.d(TAG, "用户点击同意License协议")
                ConfigTools.setLicenseAgree()

                // 验证设置是否成功
                val verifyResult = ConfigTools.getLicense()
                Log.d(TAG, "License设置后验证结果: $verifyResult")

                if (verifyResult) {
                    Log.d(TAG, "License设置成功，继续认证检查")
                    startAuthCheck()
                } else {
                    Log.e(TAG, "License设置失败，重新显示对话框")
                    showToast("设置失败，请重试")
                    showLicenseDialog()
                }
            }
            .setNegativeButton(getString(R.string.license_disagree)) { _, _ ->
                Log.d(TAG, "用户拒绝License协议")
                showToast(getString(R.string.license_must_agree))
                finish()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 第二阶段：用户认证检查
     */
    private fun startAuthCheck() {
        currentState = StartupState.CHECKING_AUTH
        logAndUpdateStatus("检查用户认证状态")

        // 首先验证数据完整性
        if (!ConfigTools.validateDataIntegrity()) {
            Log.w(TAG, "数据完整性验证失败，跳转到登录页面")
            logAndUpdateStatus("数据完整性验证失败，跳转到登录页面")
            navigateToLogin()
            return
        }

        val jwtToken = ConfigTools.getJwtToken()

        when {
            jwtToken == null -> {
                logAndUpdateStatus("JWT Token不存在，跳转到登录页面")
                navigateToLogin()
            }
            ConfigTools.checkJwtTime(JWT_REFRESH_THRESHOLD_MINUTES) -> {
                logAndUpdateStatus("JWT Token需要刷新")
                startTokenRefresh()
            }
            else -> {
                logAndUpdateStatus("认证状态正常，跳转到主页面")
                navigateToHome()
            }
        }
    }

    /**
     * 第三阶段：JWT Token 刷新
     */
    private fun startTokenRefresh() {
        currentState = StartupState.REFRESHING_TOKEN
        logAndUpdateStatus("开始刷新JWT Token")

        lifecycleScope.launch {
            try {
                val deviceJson = DeviceJson()
                Log.d(TAG, "发送Token刷新请求，设备信息: ${deviceJson.model}")

                val response = RetrofitClient.apiService.refreshToken(deviceJson)

                when (response.code) {
                    200 -> {
                        logAndUpdateStatus("Token刷新成功")

                        // 保存新的JWT Token
                        ConfigTools.setJwtToken(response.results.jwtToken)
                        ConfigTools.setJwtTime()
                        ConfigTools.setUser(response.results.user)

                        Log.d(TAG, "新Token已保存: ${response.results.jwtToken.token.take(20)}...")

                        // 跳转到主页面
                        navigateToHome()
                    }
                    else -> {
                        Log.w(TAG, "Token刷新失败: ${response.msg}")
                        handleTokenRefreshFailure("服务器返回错误: ${response.msg}")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Token刷新异常: ${e.message}", e)
                val errorMessage = ExceptionUtil.catchException(e)
                handleTokenRefreshFailure(errorMessage)
            }
        }
    }

    /**
     * 处理Token刷新失败
     */
    private fun handleTokenRefreshFailure(errorMessage: String) {
        Log.w(TAG, "Token刷新失败: $errorMessage")
        navigateToLogin()

        // 检查是否是网络问题，如果是网络问题，可以尝试使用现有Token继续
        val currentToken = ConfigTools.getJwtToken()
        val jwtTime = ConfigTools.getJwtTime()
        val timeDiff = (System.currentTimeMillis() - jwtTime) / (1000 * 60 * 60 * 24) // 转换为天数

        if (currentToken != null && timeDiff < 30) { // 如果Token存在且保存时间不超过30天
            Log.i(TAG, "Token刷新失败但现有Token仍可用，继续使用现有Token")
            showToast("网络连接异常，使用离线模式")
            navigateToHome()
//            navigateToLogin()
        } else {
            Log.w(TAG, "Token过期或不存在，需要重新登录")

            // 使用安全的认证数据清理方法
            ConfigTools.clearAuthData()

            // 显示错误提示
            showToast("登录已过期，请重新登录")

            // 跳转到登录页面
//            navigateToLogin()
        }
    }

    /**
     * 跳转到登录页面
     */
    private fun navigateToLogin() {
        currentState = StartupState.NAVIGATING
        logAndUpdateStatus("跳转到登录页面")

        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }

    /**
     * 跳转到主页面
     */
    private fun navigateToHome() {
        currentState = StartupState.NAVIGATING
        logAndUpdateStatus("跳转到主页面")

        val intent = Intent(this, HomeActivity::class.java)
        startActivity(intent)
        finish()
    }

    /**
     * 更新状态文本显示
     */
    private fun updateStatusText(message: String) {
        binding.sampleText.text = message
        Log.d(TAG, "状态更新: $message")

        // 停止下拉刷新动画
        if (binding.swipeRefreshLayout.isRefreshing) {
            binding.swipeRefreshLayout.isRefreshing = false
        }
    }

    /**
     * 记录日志并同时更新状态显示
     */
    private fun logAndUpdateStatus(message: String) {
        Log.d(TAG, message)
        updateStatusText(message)
    }



    /**
     * A native method that is implemented by the 'easydianapp' native library,
     * which is packaged with this application.
     */
    external fun stringFromJNI(): String

    companion object {
        // Used to load the 'easydianapp' library on application startup.
        private const val TAG = "MainActivity"
        private const val JWT_REFRESH_THRESHOLD_MINUTES = 15 // JWT刷新阈值：7天
        init {
            System.loadLibrary("easydianapp")
            System.loadLibrary("opencv_java4")
        }
    }
}