<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:rippleColor="@color/primary_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Cover Image -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_cover"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_image_placeholder"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/RoundedImageView"
            tools:src="@drawable/ic_image_placeholder" />

        <!-- Feed Point Number -->
        <TextView
            android:id="@+id/tv_no"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/status_indicator"
            app:layout_constraintStart_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toTopOf="@+id/iv_cover"
            tools:text="FP001" />

        <!-- Feed Point Name -->
        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/status_indicator"
            app:layout_constraintStart_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toBottomOf="@+id/tv_no"
            tools:text="A1池-投料点01" />

        <!-- Status Indicator -->
        <LinearLayout
            android:id="@+id/status_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_cover">

            <View
                android:id="@+id/status_dot"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:layout_marginEnd="4dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/hIndex_green"
                tools:backgroundTint="@color/hIndex_green" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="有效" />

        </LinearLayout>

        <!-- Action Buttons Container -->
        <LinearLayout
            android:id="@+id/action_buttons_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:gravity="start"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toBottomOf="@+id/tv_name">

            <!-- QR Code Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_qr_code"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="QR码"
                android:textSize="12sp"
                app:icon="@drawable/ic_qr_code_24"
                app:iconGravity="textStart"
                app:iconPadding="4dp"
                app:iconSize="16dp"
                app:strokeColor="@color/primary_color" />

            <!-- NFC Code Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_nfc_code"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="NFC码"
                android:textSize="12sp"
                app:icon="@drawable/ic_nfc_24"
                app:iconGravity="textStart"
                app:iconPadding="4dp"
                app:iconSize="16dp"
                app:strokeColor="@color/secondary_color" />

        </LinearLayout>

        <!-- Clickable Area for Name/Cover -->
        <View
            android:id="@+id/clickable_area"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toTopOf="@+id/action_buttons_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
