package com.zhmiaobang.easydianapp.viewmodel.user

import androidx.lifecycle.MutableLiveData
import com.zhmiaobang.easydianapp.init.ConfigTools
import com.zhmiaobang.easydianapp.json.user.UserJson
import com.zhmiaobang.easydianapp.viewmodel.BaseViewModel

class UserViewModel: BaseViewModel() {
    val userObserver: MutableLiveData<UserJson> by lazy { MutableLiveData<UserJson>() }

    init {
        val user = ConfigTools.getUser()
        if (user!=null){
            userObserver.value=user
        }

    }
}