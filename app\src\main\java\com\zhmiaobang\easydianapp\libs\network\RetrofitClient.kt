package com.zhmiaobang.easydianapp.libs.network

import android.content.Context
import android.util.Log
import com.google.gson.GsonBuilder
import com.zhmiaobang.easydianapp.BuildConfig
import com.zhmiaobang.easydianapp.init.ConfigTools
import com.zhmiaobang.easydianapp.init.MyApp.Companion.context
import okhttp3.Cache
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object RetrofitClient {

    private const val TAG = "RetrofitClient"
    private const val BASE_URL = "https://a30414ff5519.ngrok-free.app"
    private const val CACHE_SIZE: Long = 100 * 1024 * 1024 // 100 MiB
    private val gson = GsonBuilder().disableHtmlEscaping().create()

    // 缓存控制状态
    @Volatile
    private var useCache: Boolean = true

    // 不需要token的接口列表
    private val NO_TOKEN_ENDPOINTS = listOf(
        "api/user/employee/login/code/verify/"
    )

    /**
     * 设置是否使用缓存
     * @param enabled true 启用缓存，false 禁用缓存
     */
    fun setUseCache(enabled: Boolean) {
        useCache = enabled
        Log.d(TAG, "缓存设置已更新: ${if (enabled) "启用" else "禁用"}")
    }

    /**
     * 获取当前缓存设置
     */
    fun isUsingCache(): Boolean = useCache

    // 创建 OkHttpClient 实例 - 简化版
    private fun createOkHttpClient(context: Context): OkHttpClient {
        return OkHttpClient.Builder().apply {
            callTimeout(30, TimeUnit.SECONDS)

            if (BuildConfig.DEBUG) {
                val loggingInterceptor = HttpLoggingInterceptor { message ->
                    Log.d(TAG, message)
                }
                loggingInterceptor.level = HttpLoggingInterceptor.Level.BODY
                addInterceptor(loggingInterceptor)
            }

            // 总是启用缓存，通过拦截器智能控制
            cache(Cache(context.cacheDir, CACHE_SIZE))
            addNetworkInterceptor(createSmartCacheInterceptor())
        }.build()
    }

    // 智能缓存拦截器
    private fun createSmartCacheInterceptor(): Interceptor {
        return Interceptor { chain ->
            val originalRequest = chain.request()
            Log.d(TAG, "发送网络请求: ${originalRequest.method} ${originalRequest.url}")

            val requestBuilder = originalRequest.newBuilder()
                .header("Accept-Encoding", "gzip")

            // 添加 JWT Token（除了登录接口）
            if (!shouldSkipToken(originalRequest)) {
                val token = ConfigTools.getJwtToken()
                if (token != null) {
                    requestBuilder.header("Authorization", "Bearer ${token.token}")
                    Log.d(TAG, "添加 JWT Token: ${token.token.take(20)}...")
                }
            }

            val request = requestBuilder.build()
            val response = chain.proceed(request)

            Log.d(TAG, "收到网络响应: ${response.code} ${response.message}")

            // 智能缓存处理
            return@Interceptor handleSmartCache(request, response)
        }
    }

    // 智能缓存处理
    private fun handleSmartCache(request: Request, response: Response): Response {
        return when {
            // 全局禁用缓存时，强制不缓存
            !useCache -> {
                Log.d(TAG, "全局缓存已禁用，强制不缓存")
                response.newBuilder()
                    .header("Cache-Control", "no-store, no-cache, must-revalidate")
                    .header("Pragma", "no-cache")
                    .removeHeader("Expires")
                    .removeHeader("Last-Modified")
                    .removeHeader("ETag")
                    .build()
            }
            // POST 请求不缓存
            request.method == "POST" -> {
                Log.d(TAG, "POST 请求，不缓存")
                response.newBuilder()
                    .header("Cache-Control", "no-store")
                    .header("Pragma", "no-cache")
                    .build()
            }
            // 请求中已有缓存控制头，直接使用
            request.header("Cache-Control") != null -> {
                val cacheControl = request.header("Cache-Control")!!
                Log.d(TAG, "使用请求缓存控制: $cacheControl")
                response.newBuilder()
                    .removeHeader("Pragma")
                    .header("Cache-Control", "public, $cacheControl")
                    .build()
            }
            // GET 请求默认5分钟缓存
            else -> {
                Log.d(TAG, "GET 请求，默认5分钟缓存")
                response.newBuilder()
                    .removeHeader("Pragma")
                    .header("Cache-Control", "public, max-age=300")
                    .build()
            }
        }
    }

    private fun shouldSkipToken(request: Request): Boolean {
        val path = request.url.encodedPath
        return NO_TOKEN_ENDPOINTS.any { path.contains(it) }
    }

    private fun createRetrofit(context: Context): Retrofit {
        return Retrofit.Builder().apply {
            baseUrl(BASE_URL)
            addConverterFactory(GsonConverterFactory.create(gson))
            client(createOkHttpClient(context))
        }.build()
    }

    val apiService: ApiService by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        createRetrofit(context).create(ApiService::class.java)
    }
}
