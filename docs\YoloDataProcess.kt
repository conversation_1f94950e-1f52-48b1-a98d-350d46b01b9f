package com.zhmiaobang.mbfarmer.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.RectF
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import java.util.PriorityQueue
import kotlin.math.max
import kotlin.math.min

class YoloDataProcess(
    val context: Context,
    var classes: Array<String>,
    val iouThresh: Float = 0.5f,
    val confidenceThreshold: Float = 0.35f
) {

    companion object {
        const val BATCH_SIZE = 1
        const val INPUT_SIZE = 640
        const val PIXEL_SIZE = 3



    }



    fun bitmapToFloatBuffer(bitmap: Bitmap): FloatBuffer {
        val imageSTD = 255.0f

        val cap = BATCH_SIZE * PIXEL_SIZE * INPUT_SIZE * INPUT_SIZE
        val order = ByteOrder.nativeOrder()
        val buffer = ByteBuffer.allocateDirect(cap * Float.SIZE_BYTES).order(order).asFloatBuffer()

        val area = INPUT_SIZE * INPUT_SIZE
        val bitmapData = IntArray(area) // 单张图片的信息，640x640尺寸 (Single image information, 640x640 size)
        bitmap.getPixels(
            bitmapData,
            0,
            bitmap.width,
            0,
            0,
            bitmap.width,
            bitmap.height
        ) // 将信息存入数组 (Store information into the array)

        // 从数组中逐个取出数据放入buffer (Fetch data from the array and put it into the buffer one by one)
        for (i in 0 until INPUT_SIZE - 1) {
            for (j in 0 until INPUT_SIZE - 1) {
                val idx = INPUT_SIZE * i + j
                val pixelValue = bitmapData[idx]
                // 从上到下依次提取R值、G值、B值 -> 除以255进行0~1之间的归一化 (Extract R, G, B values from top to bottom -> Normalize between 0~1 by dividing by 255)
                buffer.put(idx, ((pixelValue shr 16 and 0xff) / imageSTD))
                buffer.put(idx + area, ((pixelValue shr 8 and 0xff) / imageSTD))
                buffer.put(idx + area * 2, ((pixelValue and 0xff) / imageSTD))
                // 原理：bitmap是ARGB格式的32bit，R值从16bit开始(16~23bit是R区域)，因此右移16bit (Principle: bitmap is ARGB format 32bit, R value starts from 16bit (16~23bit is R area), so shift right 16bit)
                // 这样就去掉了A值，剩下RGB值的24bit。然后与255进行AND运算获取最后8bit的R值，除以255进行归一化 (This removes the A value, leaving the 24bit RGB value. Then perform AND operation with 255 to get the last 8bit R value, and normalize by dividing by 255)
                // 再右移8bit去掉R值，对剩下的GB值进行AND运算和归一化，重复这个过程将RGB值存入buffer (Shift right 8bit again to remove the R value, perform AND operation and normalization on the remaining GB values, and repeat this process to store the RGB values into the buffer)
            }
        }
        buffer.rewind() // position 0
        return buffer
    }



    fun outputsToNPMSPredictions(outputs: Array<*>): ArrayList<YoloResultJson> {
//        val confidenceThreshold = 0.45f
        val results = ArrayList<YoloResultJson>()
        val rows: Int
        val cols: Int

        (outputs[0] as Array<*>).also {
            rows = it.size
            cols = (it[0] as FloatArray).size
        }

        //将数组形状从[84 8400]转换为[8400 84] (Convert array shape from [84 8400] to [8400 84])
        val output = Array(cols) { FloatArray(rows) }
        for (i in 0 until rows) {
            for (j in 0 until cols) {
                output[j][i] = ((((outputs[0]) as Array<*>)[i]) as FloatArray)[j]
            }
        }

        for (i in 0 until cols) {
            var detectionClass: Int = -1
            var maxScore = 0f
            val classArray = FloatArray(classes.size)
            // 单独提取label创建一维数组(0~3是坐标值) (Extract label separately to create a one-dimensional array (0~3 are coordinate values))
            System.arraycopy(output[i], 4, classArray, 0, classes.size)
            // 从label中选择最大值 (Select the maximum value from the label)
            for (j in classes.indices) {
                if (classArray[j] > maxScore) {
                    detectionClass = j
                    maxScore = classArray[j]
                }
            }

            //如果在80个coco数据集中最大概率超过特定值(当前是45%)则保存该值 (If the maximum probability in the 80 coco datasets exceeds a specific value (currently 45%), save the value)
            if (maxScore > confidenceThreshold) {
                val xPos = output[i][0]
                val yPos = output[i][1]
                val width = output[i][2]
                val height = output[i][3]
                //矩形不能超出屏幕边界，超出时取屏幕最大值 (The rectangle cannot exceed the screen boundary, take the maximum value of the screen when exceeded)
                val rectF = RectF(
                    max(0f, xPos - width / 2f),
                    max(0f, yPos - height / 2f),
                    min(INPUT_SIZE - 1f, xPos + width / 2f),
                    min(INPUT_SIZE - 1f, yPos + height / 2f)
                )
                val result = YoloResultJson(detectionClass, maxScore, rectF)
                results.add(result)
            }
        }
        return nms(results)
    }

    private fun nms(results: ArrayList<YoloResultJson>): ArrayList<YoloResultJson> {
        val list = ArrayList<YoloResultJson>()

        for (i in classes.indices) {
            //1.在类别(标签)中找到具有最高概率值的类别 (1. Find the category with the highest probability value in the category (label))
            val pq = PriorityQueue<YoloResultJson>(50) { o1, o2 ->
                o1.score.compareTo(o2.score)
            }
            val classResults = results.filter { it.classIndex == i }
            pq.addAll(classResults)

            //NMS处理 (NMS processing)
            while (pq.isNotEmpty()) {
                // 保存在队列中具有最大概率值的类别 (Save the category with the highest probability value in the queue)
                val detections = pq.toTypedArray()
                val max = detections[0]
                list.add(max)
                pq.clear()

                // 检查交集比率，如果超过50%则移除 (Check the intersection ratio, remove if it exceeds 50%)
                for (k in 1 until detections.size) {
                    val detection = detections[k]
                    val rectF = detection.rectF
//                    val iouThresh = 0.5f

                    if (boxIOU(max.rectF, rectF) < iouThresh) {
                        pq.add(detection)
                    }
                }
            }
        }
        return list
    }

    // 重叠比率(交集/并集) (Overlap ratio (intersection/union))
    private fun boxIOU(a: RectF, b: RectF): Float {
        return boxIntersection(a, b) / boxUnion(a, b)
    }

    //交集 (Intersection)
    private fun boxIntersection(a: RectF, b: RectF): Float {
        // x1, x2 == 各rect对象的中心x或y值, w1, w2 == 各rect对象的宽度或高度 (x1, x2 == center x or y value of each rect object, w1, w2 == width or height of each rect object)
        val w = overlap(
            (a.left + a.right) / 2f, a.right - a.left,
            (b.left + b.right) / 2f, b.right - b.left
        )
        val h = overlap(
            (a.top + a.bottom) / 2f, a.bottom - a.top,
            (b.top + b.bottom) / 2f, b.bottom - b.top
        )

        return if (w < 0 || h < 0) 0f else w * h
    }

    //并集 (Union)
    private fun boxUnion(a: RectF, b: RectF): Float {
        val i: Float = boxIntersection(a, b)
        return (a.right - a.left) * (a.bottom - a.top) + (b.right - b.left) * (b.bottom - b.top) - i
    }

    //相互重叠部分的长度 (Length of mutually overlapping parts)
    private fun overlap(x1: Float, w1: Float, x2: Float, w2: Float): Float {
        val l1 = x1 - w1 / 2
        val l2 = x2 - w2 / 2
        val left = max(l1, l2)
        val r1 = x1 + w1 / 2
        val r2 = x2 + w2 / 2
        val right = min(r1, r2)
        return right - left
    }
}
