package com.zhmiaobang.easydianapp.utils

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

/**
 * 分页滚动监听器
 * 用于检测RecyclerView滚动到底部时触发加载更多
 * 
 * <AUTHOR> 4.0 sonnet
 */
abstract class PaginationScrollListener(
    private val layoutManager: LinearLayoutManager,
    private val threshold: Int = 5 // 距离底部多少个item时开始加载
) : RecyclerView.OnScrollListener() {

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)

        // 只在向下滚动时检测
        if (dy <= 0) return

        val visibleItemCount = layoutManager.childCount
        val totalItemCount = layoutManager.itemCount
        val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

        // 检查是否接近底部
        if (!isLoading() && !isLastPage()) {
            if ((visibleItemCount + firstVisibleItemPosition + threshold) >= totalItemCount
                && firstVisibleItemPosition >= 0) {
                loadMoreItems()
            }
        }
    }

    /**
     * 是否正在加载
     */
    abstract fun isLoading(): Boolean

    /**
     * 是否是最后一页
     */
    abstract fun isLastPage(): Boolean

    /**
     * 加载更多数据
     */
    abstract fun loadMoreItems()
}
