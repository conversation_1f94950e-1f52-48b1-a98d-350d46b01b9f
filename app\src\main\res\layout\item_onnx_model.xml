<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@android:color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- 模型图标 -->
        <ImageView
            android:id="@+id/iv_onnx_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="模型图标"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_image_placeholder" />

        <!-- 模型信息区域 -->
        <LinearLayout
            android:id="@+id/ll_onnx_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/rb_selected"
            app:layout_constraintStart_toEndOf="@+id/iv_onnx_icon"
            app:layout_constraintTop_toTopOf="parent">

            <!-- 模型名称 -->
            <TextView
                android:id="@+id/tv_onnx_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="南美白对虾计数模型" />

            <!-- 分类名称 -->
            <TextView
                android:id="@+id/tv_onnx_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="南美白对虾" />

        </LinearLayout>

        <!-- 选择状态指示器 -->
        <RadioButton
            android:id="@+id/rb_selected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:focusable="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 可点击区域覆盖整个卡片 -->
        <View
            android:id="@+id/clickable_area"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
