package com.zhmiaobang.easydianapp.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.RoundedCornersTransformation
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ItemFeedPointBinding
import com.zhmiaobang.easydianapp.databinding.ItemLoadErrorBinding
import com.zhmiaobang.easydianapp.databinding.ItemLoadingMoreBinding
import com.zhmiaobang.easydianapp.databinding.ItemNoMoreDataBinding
import com.zhmiaobang.easydianapp.json.feed.FeedPointJson
import com.zhmiaobang.easydianapp.utils.LoadingState

/**
 * 投料点列表适配器 - 支持分页加载
 * 用于在 FeedPointListActivity 中展示投料点列表
 *
 * 功能特性：
 * - 展示投料点基本信息（编号、名称、封面、状态）
 * - 支持QR码和NFC码操作按钮
 * - 点击名称/封面跳转到编辑页面
 * - 使用Coil加载封面图片
 * - 状态指示器显示
 * - 支持分页加载和加载状态显示
 *
 * <AUTHOR> 4.0 sonnet
 */
class FeedPointAdapter(
    private var feedPoints: MutableList<FeedPointJson> = mutableListOf()
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "FeedPointAdapter"

        // ViewType常量
        private const val TYPE_FEED_POINT = 0
        private const val TYPE_LOADING = 1
        private const val TYPE_ERROR = 2
        private const val TYPE_NO_MORE = 3
    }

    // 加载状态
    private var loadingState: LoadingState = LoadingState.IDLE
    private var errorMessage: String? = null

    // 点击事件监听器接口
    interface OnItemClickListener {
        fun onItemClick(feedPoint: FeedPointJson)
        fun onQrCodeClick(feedPoint: FeedPointJson)
        fun onNfcCodeClick(feedPoint: FeedPointJson)
        fun onRetryClick() // 重试点击事件
    }

    private var itemClickListener: OnItemClickListener? = null

    /**
     * 设置点击事件监听器
     */
    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.itemClickListener = listener
    }

    /**
     * 更新所有数据（刷新）
     */
    fun updateData(newFeedPoints: List<FeedPointJson>) {
        feedPoints.clear()
        feedPoints.addAll(newFeedPoints)
        loadingState = LoadingState.IDLE
        notifyDataSetChanged()
    }

    /**
     * 追加数据（分页加载）
     */
    fun addData(newFeedPoints: List<FeedPointJson>) {
        val startPosition = feedPoints.size
        feedPoints.addAll(newFeedPoints)
        loadingState = LoadingState.IDLE
        notifyItemRangeInserted(startPosition, newFeedPoints.size)
    }

    /**
     * 显示加载更多状态
     */
    fun showLoadingMore() {
        if (loadingState != LoadingState.LOADING_MORE) {
            loadingState = LoadingState.LOADING_MORE
            notifyItemChanged(itemCount - 1)
        }
    }

    /**
     * 显示加载错误状态
     */
    fun showLoadError(message: String) {
        errorMessage = message
        loadingState = LoadingState.ERROR
        notifyItemChanged(itemCount - 1)
    }

    /**
     * 显示没有更多数据状态
     */
    fun showNoMoreData() {
        if (loadingState != LoadingState.NO_MORE) {
            loadingState = LoadingState.NO_MORE
            notifyItemChanged(itemCount - 1)
        }
    }

    /**
     * 隐藏加载状态
     */
    fun hideLoadingState() {
        if (loadingState != LoadingState.IDLE) {
            loadingState = LoadingState.IDLE
            notifyItemChanged(itemCount - 1)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < feedPoints.size) {
            TYPE_FEED_POINT
        } else {
            when (loadingState) {
                LoadingState.LOADING_MORE -> TYPE_LOADING
                LoadingState.ERROR -> TYPE_ERROR
                LoadingState.NO_MORE -> TYPE_NO_MORE
                else -> TYPE_FEED_POINT
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_FEED_POINT -> {
                val binding = ItemFeedPointBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                FeedPointViewHolder(binding)
            }
            TYPE_LOADING -> {
                val binding = ItemLoadingMoreBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                LoadingViewHolder(binding)
            }
            TYPE_ERROR -> {
                val binding = ItemLoadErrorBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ErrorViewHolder(binding)
            }
            TYPE_NO_MORE -> {
                val binding = ItemNoMoreDataBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                NoMoreViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is FeedPointViewHolder -> {
                if (position < feedPoints.size) {
                    holder.bind(feedPoints[position])
                }
            }
            is ErrorViewHolder -> {
                holder.bind(errorMessage ?: "加载失败")
            }
        }
    }

    override fun getItemCount(): Int {
        return feedPoints.size + if (shouldShowLoadingItem()) 1 else 0
    }

    /**
     * 是否应该显示加载项
     */
    private fun shouldShowLoadingItem(): Boolean {
        return loadingState != LoadingState.IDLE && feedPoints.isNotEmpty()
    }

    /**
     * ViewHolder类
     */
    inner class FeedPointViewHolder(
        private val binding: ItemFeedPointBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(feedPoint: FeedPointJson) {
            with(binding) {
                // 绑定基本信息
                tvNo.text = feedPoint.no
                tvName.text = feedPoint.name

                // 加载封面图片
                loadCoverImage(feedPoint.cover)

                // 设置状态指示器
                setStatusIndicator(feedPoint.status)

                // 设置点击事件
                setupClickListeners(feedPoint)
            }
        }

        /**
         * 加载封面图片
         */
        private fun loadCoverImage(coverUrl: String?) {
            binding.ivCover.load(coverUrl) {
                crossfade(300)
                placeholder(R.drawable.ic_image_placeholder)
                error(R.drawable.ic_image_placeholder)
                transformations(RoundedCornersTransformation(8f))
            }
        }

        /**
         * 设置状态指示器
         */
        private fun setStatusIndicator(status: Int) {
            val context = binding.root.context
            
            when (status) {
                1 -> {
                    // 有效状态
                    binding.statusDot.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.hIndex_green)
                    binding.tvStatus.text = "有效"
                    binding.tvStatus.setTextColor(
                        ContextCompat.getColor(context, R.color.hIndex_green)
                    )
                }
                2 -> {
                    // 无效状态
                    binding.statusDot.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.hIndex_red)
                    binding.tvStatus.text = "无效"
                    binding.tvStatus.setTextColor(
                        ContextCompat.getColor(context, R.color.hIndex_red)
                    )
                }
                else -> {
                    // 未知状态
                    binding.statusDot.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.text_hint)
                    binding.tvStatus.text = "未知"
                    binding.tvStatus.setTextColor(
                        ContextCompat.getColor(context, R.color.text_hint)
                    )
                }
            }
        }

        /**
         * 设置点击事件监听器
         */
        private fun setupClickListeners(feedPoint: FeedPointJson) {
            with(binding) {
                // 点击名称/封面区域跳转到编辑页面
                clickableArea.setOnClickListener {
                    itemClickListener?.onItemClick(feedPoint)
                }

                // QR码按钮点击事件
                btnQrCode.setOnClickListener {
                    itemClickListener?.onQrCodeClick(feedPoint)
                }

                // NFC码按钮点击事件
                btnNfcCode.setOnClickListener {
                    itemClickListener?.onNfcCodeClick(feedPoint)
                }
            }
        }
    }

    /**
     * 加载更多ViewHolder
     */
    inner class LoadingViewHolder(
        private val binding: ItemLoadingMoreBinding
    ) : RecyclerView.ViewHolder(binding.root)

    /**
     * 加载错误ViewHolder
     */
    inner class ErrorViewHolder(
        private val binding: ItemLoadErrorBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(errorMessage: String) {
            binding.tvErrorMessage.text = errorMessage
            binding.btnRetry.setOnClickListener {
                itemClickListener?.onRetryClick()
            }
        }
    }

    /**
     * 没有更多数据ViewHolder
     */
    inner class NoMoreViewHolder(
        private val binding: ItemNoMoreDataBinding
    ) : RecyclerView.ViewHolder(binding.root)
}
