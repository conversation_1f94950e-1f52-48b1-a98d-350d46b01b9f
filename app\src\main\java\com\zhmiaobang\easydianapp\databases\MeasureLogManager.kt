package com.zhmiaobang.easydianapp.databases

import android.content.Context
import androidx.lifecycle.LiveData
import com.zhmiaobang.easydianapp.databases.entity.MeasureLog
import com.zhmiaobang.easydianapp.databases.repository.MeasureLogRepository
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 测量日志管理器
 * 
 * 提供简化的测量日志操作接口，封装常用的数据库操作
 * 适合在 Activity 和 Fragment 中直接使用
 * 
 * 使用示例：
 * ```kotlin
 * // 在 Activity 或 Fragment 中
 * val measureLogManager = MeasureLogManager.getInstance(this)
 * 
 * // 创建测量日志
 * lifecycleScope.launch {
 *     val logId = measureLogManager.createMeasureLog(onnxModel, phone, imagePath)
 *     Log.d("MeasureLog", "创建测量日志，ID: $logId")
 * }
 * 
 * // 观察所有测量日志
 * measureLogManager.getAllMeasureLogsLiveData().observe(this) { logs ->
 *     // 更新UI
 *     adapter.updateData(logs)
 * }
 * ```
 * 
 * <AUTHOR> 4.0 sonnet
 */
class MeasureLogManager private constructor(context: Context) {
    
    private val repository = MeasureLogRepository.getInstance(context)
    
    companion object {
        @Volatile
        private var INSTANCE: MeasureLogManager? = null
        
        /**
         * 获取管理器实例（单例模式）
         * 
         * @param context 应用程序上下文
         * @return 管理器实例
         */
        fun getInstance(context: Context): MeasureLogManager {
            return INSTANCE ?: synchronized(this) {
                val instance = MeasureLogManager(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    // ==================== 便捷创建方法 ====================
    
    /**
     * 创建新的测量日志记录
     * 
     * @param onnxModelJson 选中的ONNX模型
     * @param phone 电话号码
     * @param imgSrc 源图片路径
     * @return 插入记录的 ID
     */
    suspend fun createMeasureLog(
        onnxModelJson: OnnxModelJson,
        phone: String,
        imgSrc: String
    ): Long {
        return repository.createMeasureLog(onnxModelJson, phone, imgSrc)
    }
    
    /**
     * 在协程作用域中创建测量日志记录
     * 
     * @param scope 协程作用域
     * @param onnxModelJson 选中的ONNX模型
     * @param phone 电话号码
     * @param imgSrc 源图片路径
     * @param onSuccess 成功回调，参数为插入记录的 ID
     * @param onError 错误回调，参数为异常信息
     */
    fun createMeasureLogAsync(
        scope: CoroutineScope,
        onnxModelJson: OnnxModelJson,
        phone: String,
        imgSrc: String,
        onSuccess: (Long) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        scope.launch {
            try {
                val logId = createMeasureLog(onnxModelJson, phone, imgSrc)
                withContext(Dispatchers.Main) {
                    onSuccess(logId)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    onError(e.message ?: "创建测量日志失败")
                }
            }
        }
    }
    
    // ==================== 查询方法 ====================
    
    /**
     * 获取所有测量日志记录（LiveData）
     * 适合在 Activity/Fragment 中观察数据变化
     * 
     * @return 所有测量日志记录的 LiveData
     */
    fun getAllMeasureLogsLiveData(): LiveData<List<MeasureLog>> {
        return repository.getAllMeasureLogsLiveData()
    }
    
    /**
     * 根据电话号码获取测量日志记录（LiveData）
     * 
     * @param phone 电话号码
     * @return 该电话号码的所有测量日志记录的 LiveData
     */
    fun getMeasureLogsByPhoneLiveData(phone: String): LiveData<List<MeasureLog>> {
        return repository.getMeasureLogsByPhoneLiveData(phone)
    }
    
    /**
     * 获取所有测量日志记录
     * 
     * @return 所有测量日志记录
     */
    suspend fun getAllMeasureLogs(): List<MeasureLog> {
        return repository.getAllMeasureLogs()
    }
    
    /**
     * 根据 ID 获取测量日志记录
     * 
     * @param id 记录 ID
     * @return 测量日志记录，如果不存在则返回 null
     */
    suspend fun getMeasureLogById(id: Long): MeasureLog? {
        return repository.getMeasureLogById(id)
    }
    
    /**
     * 分页获取测量日志记录
     * 
     * @param page 页码（从0开始）
     * @param pageSize 每页数量
     * @return 测量日志记录列表
     */
    suspend fun getMeasureLogsByPage(page: Int, pageSize: Int = 20): List<MeasureLog> {
        return repository.getMeasureLogsByPage(page, pageSize)
    }
    
    // ==================== 更新方法 ====================
    
    /**
     * 完成测量（更新测量结果）
     *
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @param count 计数结果
     * @return 是否更新成功
     */
    suspend fun completeMeasure(id: Long, destImg: String?, count: Int?): Boolean {
        return repository.updateMeasureResult(id, destImg, count) > 0
    }

    /**
     * 完成测量（更新测量结果，包含阈值信息）
     *
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @param count 计数结果
     * @param conf 置信度阈值
     * @param nms NMS阈值
     * @return 是否更新成功
     */
    suspend fun completeMeasureWithThresholds(
        id: Long,
        destImg: String?,
        count: Int?,
        conf: Float?,
        nms: Float?
    ): Boolean {
        return repository.updateMeasureResultWithThresholds(id, destImg, count, conf, nms) > 0
    }
    
    /**
     * 在协程作用域中完成测量
     * 
     * @param scope 协程作用域
     * @param id 记录 ID
     * @param destImg 目标图片路径
     * @param count 计数结果
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun completeMeasureAsync(
        scope: CoroutineScope,
        id: Long,
        destImg: String?,
        count: Int?,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        scope.launch {
            try {
                val success = completeMeasure(id, destImg, count)
                withContext(Dispatchers.Main) {
                    if (success) {
                        onSuccess()
                    } else {
                        onError("更新测量结果失败")
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    onError(e.message ?: "更新测量结果失败")
                }
            }
        }
    }
    
    // ==================== 删除方法 ====================
    
    /**
     * 删除测量日志记录
     * 
     * @param id 记录 ID
     * @return 是否删除成功
     */
    suspend fun deleteMeasureLog(id: Long): Boolean {
        return repository.deleteMeasureLogById(id) > 0
    }
    
    /**
     * 在协程作用域中删除测量日志记录
     * 
     * @param scope 协程作用域
     * @param id 记录 ID
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun deleteMeasureLogAsync(
        scope: CoroutineScope,
        id: Long,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        scope.launch {
            try {
                val success = deleteMeasureLog(id)
                withContext(Dispatchers.Main) {
                    if (success) {
                        onSuccess()
                    } else {
                        onError("删除测量日志失败")
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    onError(e.message ?: "删除测量日志失败")
                }
            }
        }
    }
    
    // ==================== 统计方法 ====================
    
    /**
     * 获取测量日志记录总数
     * 
     * @return 记录总数
     */
    suspend fun getMeasureLogCount(): Int {
        return repository.getMeasureLogCount()
    }
    
    /**
     * 获取已完成的测量日志记录数量
     * 
     * @return 已完成的记录数量
     */
    suspend fun getCompletedMeasureLogCount(): Int {
        return repository.getCompletedMeasureLogCount()
    }
}
