package com.zhmiaobang.easydianapp.module.home

import androidx.fragment.app.viewModels
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.zhmiaobang.easydianapp.R

class HomeIndexFragment : Fragment() {

    companion object {
        fun newInstance() = HomeIndexFragment()
    }

    private val viewModel: HomeIndexViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // TODO: Use the ViewModel
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.fragment_home_index, container, false)
    }
}