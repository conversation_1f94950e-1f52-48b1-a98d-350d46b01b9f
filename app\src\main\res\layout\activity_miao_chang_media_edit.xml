<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".module.profile.MiaoChangMediaEditActivity">

    <!-- 顶部工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/media_edit_toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/hIndex_blue"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:titleTextColor="@android:color/white"
        app:titleCentered="true"
        app:title="编辑媒体信息"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@android:color/white" />

    <!-- 主内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/media_edit_scroll_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@+id/media_edit_toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/media_edit_save_button">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 基本信息标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="基本信息"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/hIndex_blue"
                android:layout_marginBottom="16dp" />

            <!-- 联系人姓名输入框 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/media_edit_contact_person_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="联系人姓名"
                app:startIconDrawable="@drawable/ic_person"
                app:startIconTint="@color/hIndex_blue">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/media_edit_contact_person"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 联系电话输入框 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/media_edit_contact_phone_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="联系电话"
                app:startIconDrawable="@drawable/ic_phone"
                app:startIconTint="@color/hIndex_blue">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/media_edit_contact_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 媒体信息标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="媒体信息"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/hIndex_blue"
                android:layout_marginBottom="16dp" />

            <!-- Logo图片选择区域 -->
            <include
                android:id="@+id/media_edit_logo_section"
                layout="@layout/item_image_selector"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp" />

            <!-- 二维码选择区域 (抖音和微信并排显示) -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="24dp"
                android:baselineAligned="false">

                <!-- 抖音二维码选择区域 -->
                <include
                    android:id="@+id/media_edit_douyin_section"
                    layout="@layout/item_image_selector"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp" />

                <!-- 微信二维码选择区域 -->
                <include
                    android:id="@+id/media_edit_wechat_section"
                    layout="@layout/item_image_selector"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- 底部保存按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/media_edit_save_button"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:backgroundTint="@color/hIndex_blue"
        android:text="保存更改"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:cornerRadius="8dp"
        app:icon="@drawable/ic_save"
        app:iconTint="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>