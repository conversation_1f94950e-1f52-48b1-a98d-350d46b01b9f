<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/black"
    tools:context=".module.measure.DisplayMeasureImageActivity">

    <!-- 顶部工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/black"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar"
        app:titleCentered="true"
        app:titleTextColor="@color/white"
        app:navigationIconTint="@color/white" />

    <!-- 图片显示区域 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/black">

        <!-- PhotoView 用于图片缩放 -->
        <com.github.chrisbanes.photoview.PhotoView
            android:id="@+id/photoView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="@color/black"
            android:scaleType="fitCenter" />

        <!-- 加载指示器 -->
        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progressIndicator"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:indeterminate="true"
            android:visibility="gone"
            app:indicatorColor="@color/white"
            app:trackColor="@color/divider_color" />

        <!-- 错误提示 -->
        <LinearLayout
            android:id="@+id/errorLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ImageView
                android:layout_width="96dp"
                android:layout_height="96dp"
                android:src="@drawable/ic_image_error"
                android:alpha="0.6"
                app:tint="@color/white" />

            <TextView
                android:id="@+id/tvErrorMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="图片加载失败"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnRetryLoad"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="重试"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </FrameLayout>

    <!-- 底部信息栏 -->
    <LinearLayout
        android:id="@+id/bottomInfoLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/black"
        android:gravity="center_vertical">

        <!-- 计数信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="计数结果："
                android:textColor="@color/white"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvCountValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="@color/primary_color"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="156" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="个"
                android:textColor="@color/white"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 分享按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnShare"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                app:icon="@drawable/ic_share_24"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconTint="@color/white"
                style="@style/Widget.Material3.Button.TextButton" />

            <!-- 保存按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                app:icon="@drawable/ic_download_24"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconTint="@color/white"
                style="@style/Widget.Material3.Button.TextButton" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>