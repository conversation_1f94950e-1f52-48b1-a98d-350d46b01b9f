<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:rippleColor="@color/primary_color">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Cover Image -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_cover"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_image_placeholder"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/RoundedImageView"
            tools:src="@drawable/ic_image_placeholder" />

        <!-- Feed Package Number -->
        <TextView
            android:id="@+id/tv_no"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/status_indicator"
            app:layout_constraintStart_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toTopOf="@+id/iv_cover"
            tools:text="FPK001" />

        <!-- Feed Package Name -->
        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/status_indicator"
            app:layout_constraintStart_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toBottomOf="@+id/tv_no"
            tools:text="优质饲料包A" />

        <!-- Status Indicator -->
        <LinearLayout
            android:id="@+id/status_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_cover">

            <View
                android:id="@+id/status_dot"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:layout_marginEnd="4dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/hIndex_green"
                tools:backgroundTint="@color/hIndex_green" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="有效" />

        </LinearLayout>

        <!-- Clickable Area for Logo/Name -->
        <View
            android:id="@+id/clickable_area"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
