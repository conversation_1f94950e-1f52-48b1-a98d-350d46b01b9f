package com.zhmiaobang.easydianapp.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ItemEmployeeBinding
import com.zhmiaobang.easydianapp.databinding.ItemLoadErrorBinding
import com.zhmiaobang.easydianapp.databinding.ItemLoadingMoreBinding
import com.zhmiaobang.easydianapp.databinding.ItemNoMoreDataBinding
import com.zhmiaobang.easydianapp.json.user.UserJson
import com.zhmiaobang.easydianapp.utils.LoadingState

/**
 * 员工列表适配器
 * 用于在 EmployeeListActivity 中展示员工列表
 *
 * 功能特性：
 * - 展示员工基本信息（头像、姓名、电话、状态）
 * - 点击头像/姓名跳转到编辑页面
 * - 使用Coil加载头像图片
 * - 状态指示器显示
 * - 支持加载状态显示
 *
 * <AUTHOR> 4.0 sonnet
 */
class EmployeeAdapter(
    private var employees: MutableList<UserJson> = mutableListOf()
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "EmployeeAdapter"

        // ViewType常量
        private const val TYPE_EMPLOYEE = 0
        private const val TYPE_LOADING = 1
        private const val TYPE_ERROR = 2
        private const val TYPE_NO_MORE = 3
    }

    // 加载状态
    private var loadingState: LoadingState = LoadingState.IDLE
    private var errorMessage: String? = null

    // 点击事件监听器接口
    interface OnItemClickListener {
        fun onItemClick(employee: UserJson)
        fun onQrCodeClick(employee: UserJson) // 二维码点击事件
        fun onRetryClick() // 重试点击事件
    }

    private var itemClickListener: OnItemClickListener? = null

    /**
     * 设置点击事件监听器
     */
    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.itemClickListener = listener
    }

    /**
     * 更新所有数据（刷新）
     */
    fun updateData(newEmployees: List<UserJson>) {
        employees.clear()
        employees.addAll(newEmployees)
        loadingState = LoadingState.IDLE
        notifyDataSetChanged()
    }

    /**
     * 显示加载错误状态
     */
    fun showLoadError(message: String) {
        errorMessage = message
        loadingState = LoadingState.ERROR
        notifyItemChanged(itemCount - 1)
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < employees.size) {
            TYPE_EMPLOYEE
        } else {
            when (loadingState) {
                LoadingState.LOADING_MORE -> TYPE_LOADING
                LoadingState.ERROR -> TYPE_ERROR
                LoadingState.NO_MORE -> TYPE_NO_MORE
                else -> TYPE_EMPLOYEE
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_EMPLOYEE -> {
                val binding = ItemEmployeeBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                EmployeeViewHolder(binding)
            }
            TYPE_LOADING -> {
                val binding = ItemLoadingMoreBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                LoadingViewHolder(binding)
            }
            TYPE_ERROR -> {
                val binding = ItemLoadErrorBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ErrorViewHolder(binding)
            }
            TYPE_NO_MORE -> {
                val binding = ItemNoMoreDataBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                NoMoreViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is EmployeeViewHolder -> {
                if (position < employees.size) {
                    holder.bind(employees[position])
                }
            }
            is ErrorViewHolder -> {
                holder.bind(errorMessage ?: "加载失败")
            }
        }
    }

    override fun getItemCount(): Int {
        return employees.size + if (shouldShowLoadingItem()) 1 else 0
    }

    /**
     * 是否应该显示加载项
     */
    private fun shouldShowLoadingItem(): Boolean {
        return loadingState != LoadingState.IDLE && employees.isNotEmpty()
    }

    /**
     * ViewHolder类
     */
    inner class EmployeeViewHolder(
        private val binding: ItemEmployeeBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(employee: UserJson) {
            with(binding) {
                // 绑定基本信息
                tvEmployeeName.text = employee.nickname ?: "未设置姓名"
                tvEmployeePhone.text = employee.employeePhone ?: "未设置电话"

                // 加载头像
                loadAvatar(employee.avatar)

                // 设置状态指示器
                setStatusIndicator(employee.is_active)

                // 设置角色信息
                tvEmployeeRole.text = employee.role.name

                // 设置点击事件
                setupClickListeners(employee)
            }
        }

        /**
         * 加载头像图片
         */
        private fun loadAvatar(avatarUrl: String?) {
            binding.ivEmployeeAvatar.load(avatarUrl) {
                crossfade(300)
                placeholder(R.drawable.ic_person)
                error(R.drawable.ic_person)
                transformations(CircleCropTransformation())
            }
        }

        /**
         * 设置状态指示器
         */
        private fun setStatusIndicator(isActive: Boolean) {
            val context = binding.root.context
            if (isActive) {
                binding.statusDot.setBackgroundResource(R.drawable.circle_shape)
                binding.statusDot.backgroundTintList = context.getColorStateList(R.color.hIndex_green)
                binding.tvEmployeeStatus.text = "在职"
                binding.tvEmployeeStatus.setTextColor(context.getColor(R.color.hIndex_green))
            } else {
                binding.statusDot.setBackgroundResource(R.drawable.circle_shape)
                binding.statusDot.backgroundTintList = context.getColorStateList(R.color.hIndex_red)
                binding.tvEmployeeStatus.text = "离职"
                binding.tvEmployeeStatus.setTextColor(context.getColor(R.color.hIndex_red))
            }
        }

        /**
         * 设置点击事件监听器
         */
        private fun setupClickListeners(employee: UserJson) {
            with(binding) {
                // 点击头像/姓名区域跳转到编辑页面
                clickableArea.setOnClickListener {
                    itemClickListener?.onItemClick(employee)
                }

                // 点击二维码容器显示员工信息
                qrCodeContainer.setOnClickListener {
                    itemClickListener?.onQrCodeClick(employee)
                }
            }
        }
    }

    /**
     * 加载更多ViewHolder
     */
    inner class LoadingViewHolder(
        private val binding: ItemLoadingMoreBinding
    ) : RecyclerView.ViewHolder(binding.root)

    /**
     * 错误ViewHolder
     */
    inner class ErrorViewHolder(
        private val binding: ItemLoadErrorBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(message: String) {
            binding.tvErrorMessage.text = message
            binding.btnRetry.setOnClickListener {
                itemClickListener?.onRetryClick()
            }
        }
    }

    /**
     * 没有更多数据ViewHolder
     */
    inner class NoMoreViewHolder(
        private val binding: ItemNoMoreDataBinding
    ) : RecyclerView.ViewHolder(binding.root)
}
