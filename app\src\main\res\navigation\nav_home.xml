<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_home"
    app:startDestination="@id/homeIndexFragment">

    <fragment
        android:id="@+id/homeIndexFragment"
        android:name="com.zhmiaobang.easydianapp.module.home.HomeIndexFragment"
        android:label="@string/home_nav_index"
        tools:layout="@layout/fragment_home_index" />

    <fragment
        android:id="@+id/homeCountFragment"
        android:name="com.zhmiaobang.easydianapp.module.home.HomeCountFragment"
        android:label="@string/home_nav_count"
        tools:layout="@layout/fragment_home_count" />

    <fragment
        android:id="@+id/homeFeedFragment"
        android:name="com.zhmiaobang.easydianapp.module.home.HomeFeedFragment"
        android:label="@string/home_nav_feed"
        tools:layout="@layout/fragment_home_feed" />

    <fragment
        android:id="@+id/homeMeasureFragment"
        android:name="com.zhmiaobang.easydianapp.module.home.HomeMeasureFragment"
        android:label="@string/home_nav_measure"
        tools:layout="@layout/fragment_home_measure" />

    <fragment
        android:id="@+id/homeProfileFragment"
        android:name="com.zhmiaobang.easydianapp.module.home.HomeProfileFragment"
        android:label="@string/home_nav_profile"
        tools:layout="@layout/fragment_home_profile" />

</navigation>
