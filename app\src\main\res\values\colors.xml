<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- Login theme colors -->
    <color name="login_primary_blue">#4285F4</color>
    <color name="login_light_blue">#E3F2FD</color>
    <color name="login_text_primary">#212121</color>
    <color name="login_text_secondary">#757575</color>
    <color name="login_border_gray">#E0E0E0</color>

    <!-- Home Index theme colors -->
    <color name="hIndex_background">#F5F5F5</color>
    <color name="hIndex_card_background">#FFFFFF</color>
    <color name="hIndex_text_primary">#333333</color>
    <color name="hIndex_text_secondary">#666666</color>
    <color name="hIndex_text_hint">#999999</color>
    <color name="hIndex_blue">#2196F3</color>
    <color name="hIndex_blue_light">#E3F2FD</color>
    <color name="hIndex_green">#4CAF50</color>
    <color name="hIndex_orange">#FF9800</color>
    <color name="hIndex_purple">#9C27B0</color>
    <color name="hIndex_red">#F44336</color>
    <color name="hIndex_trend_up">#4CAF50</color>
    <color name="hIndex_trend_down">#F44336</color>
    <color name="hIndex_divider">#E0E0E0</color>

    <!-- Home Bottom Navigation colors -->
    <color name="home_nav_selected">#2196F3</color>
    <color name="home_nav_unselected">#999999</color>
    <color name="home_nav_background">#FFFFFF</color>

    <!-- Aquaculture Theme Colors - 水产养殖主题色 -->
    <color name="aqua_primary">#0277BD</color>          <!-- 深海蓝 -->
    <color name="aqua_primary_light">#58A5F0</color>    <!-- 浅海蓝 -->
    <color name="aqua_primary_dark">#004C8C</color>     <!-- 深蓝 -->

    <color name="aqua_secondary">#00ACC1</color>        <!-- 青蓝色 -->
    <color name="aqua_secondary_light">#5DDEF4</color>  <!-- 浅青蓝 -->
    <color name="aqua_secondary_dark">#00838F</color>   <!-- 深青蓝 -->

    <color name="aqua_accent">#26C6DA</color>           <!-- 水青色 -->
    <color name="aqua_accent_light">#6FF9FF</color>     <!-- 浅水青 -->
    <color name="aqua_accent_dark">#0095A8</color>      <!-- 深水青 -->

    <!-- Card Background Colors - 卡片背景色 -->
    <color name="aqua_card_model">#E1F5FE</color>       <!-- 模型选择卡片 - 极浅蓝 -->
    <color name="aqua_card_phone">#F0F8FF</color>       <!-- 电话输入卡片 - 爱丽丝蓝 -->
    <color name="aqua_card_image">#E8F4FD</color>       <!-- 图片选择卡片 - 浅天蓝 -->

    <!-- Gradient Colors - 渐变色 -->
    <color name="aqua_gradient_start">#E3F2FD</color>   <!-- 渐变起始 -->
    <color name="aqua_gradient_end">#F1F8E9</color>     <!-- 渐变结束 -->

    <!-- Shadow and Border Colors - 阴影和边框色 -->
    <color name="aqua_shadow">#1A0277BD</color>         <!-- 阴影色 -->
    <color name="aqua_border">#B3E5FC</color>           <!-- 边框色 -->
    <color name="aqua_divider">#E0F2F1</color>          <!-- 分割线色 -->

    <!-- MainActivity theme colors -->
    <color name="primary_color">#4285F4</color>
    <color name="secondary_color">#34A853</color>
    <color name="background_gradient_start">#F8F9FA</color>
    <color name="background_gradient_end">#E8F0FE</color>
    <color name="text_primary">#202124</color>
    <color name="text_secondary">#5F6368</color>
    <color name="text_hint">#9AA0A6</color>
    <color name="card_background">#FFFFFF</color>
    <color name="dot_active">#4285F4</color>
    <color name="dot_inactive">#DADCE0</color>

    <!-- 状态颜色 -->
    <color name="success_color">#4CAF50</color>
    <color name="error_color">#F44336</color>
    <color name="warning_color">#FF9800</color>
    <color name="info_color">#2196F3</color>

    <!-- 分割线和边框颜色 -->
    <color name="divider_color">#E0E0E0</color>
    <color name="border_color">#CCCCCC</color>

    <!-- 图片占位符背景色 -->
    <color name="image_placeholder_bg">#F5F5F5</color>

    <!-- 强调色和第三级文本颜色 -->
    <color name="accent_color">#FF9800</color>
    <color name="text_tertiary">#9E9E9E</color>
</resources>