<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- 图片访问权限 - 支持Android SDK 29-36 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- NFC权限 -->
    <uses-permission android:name="android.permission.NFC" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" /> <!-- NFC硬件特性 -->
    <uses-feature
        android:name="android.hardware.nfc"
        android:required="false" /> <!-- 震动权限 - 用于拍照反馈 -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <application
        android:name=".init.MyApp"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.EasyDianApp"
        tools:targetApi="31">
        <activity
            android:name=".module.miacochang.preseed.GetPreSeedQrCodeActivity"
            android:exported="false" />
        <activity
            android:name=".module.measure.DisplayMeasureImageActivity"
            android:exported="false" />
        <activity
            android:name=".module.measure.MeasureLogListActivity"
            android:exported="false" />
        <activity
            android:name=".module.measure.MeasureResultActivity"
            android:exported="false" />
        <activity
            android:name=".module.measure.SelectMeasureOnnxModelActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.feed.FeeddPointNfcWriterActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.feed.ShowFeedPointQrCodeActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.preseed.PreSeedListActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.employee.EmployeeQrCodeActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.employee.EditEmployeeActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.employee.EmployeeListActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.feed.EditFeedPackageActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.feed.FeedPackageListActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.feed.EditFeedpointActivity"
            android:exported="false" />
        <activity
            android:name=".module.miacochang.feed.FeedPointListActivity"
            android:exported="false" />
        <activity
            android:name=".module.profile.MiaoChangMediaEditActivity"
            android:exported="false" />
        <activity
            android:name=".module.count.CountResultActivity"
            android:exported="false" />
        <activity
            android:name=".module.count.CountCameraActivity"
            android:exported="false" />
        <activity
            android:name=".module.home.HomeActivity"
            android:exported="false" />
        <activity
            android:name=".module.login.LoginActivity"
            android:exported="false" />
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity> <!-- FileProvider for sharing files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>