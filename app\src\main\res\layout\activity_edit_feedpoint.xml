<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.miacochang.feed.EditFeedpointActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_color"
            app:title="编辑投料点"
            app:titleTextColor="@color/white"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 基本信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="基本信息"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- 投料点编号 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edit_no_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="投料点编号"
                        app:startIconDrawable="@drawable/ic_tag_24"
                        app:startIconTint="@color/primary_color">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_no"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 投料点名称 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edit_name_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="投料点名称"
                        app:startIconDrawable="@drawable/ic_location_24"
                        app:startIconTint="@color/primary_color">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 状态选择 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edit_status_layout"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="状态"
                        app:startIconDrawable="@drawable/ic_status_24"
                        app:startIconTint="@color/primary_color">

                        <AutoCompleteTextView
                            android:id="@+id/edit_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 喂养周期 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edit_feed_period_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="喂养周期（天）"
                        app:startIconDrawable="@drawable/ic_schedule_24"
                        app:startIconTint="@color/primary_color">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_feed_period"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 详细信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="详细信息"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- 描述 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edit_description_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="描述"
                        app:startIconDrawable="@drawable/ic_description_24"
                        app:startIconTint="@color/primary_color">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:maxLines="3"
                            android:minLines="2" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 管理员备注 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/edit_remark_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="管理员备注"
                        app:startIconDrawable="@drawable/ic_note_24"
                        app:startIconTint="@color/primary_color">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_remark"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:maxLines="3"
                            android:minLines="2" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 封面图片卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="封面图片"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- 图片选择区域 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- 图片预览区域 -->
                        <FrameLayout
                            android:id="@+id/image_preview_container"
                            android:layout_width="match_parent"
                            android:layout_height="200dp"
                            android:layout_marginBottom="16dp"
                            android:background="@drawable/dashed_border"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?attr/selectableItemBackground">

                            <!-- 空状态 -->
                            <LinearLayout
                                android:id="@+id/image_empty_state"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:gravity="center"
                                android:orientation="vertical">

                                <ImageView
                                    android:layout_width="48dp"
                                    android:layout_height="48dp"
                                    android:layout_marginBottom="8dp"
                                    android:alpha="0.6"
                                    android:src="@drawable/ic_add_photo_24"
                                    app:tint="@color/text_hint" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="点击选择封面图片"
                                    android:textColor="@color/text_hint"
                                    android:textSize="14sp" />

                            </LinearLayout>

                            <!-- 图片预览 -->
                            <com.google.android.material.imageview.ShapeableImageView
                                android:id="@+id/image_preview"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:visibility="gone"
                                app:shapeAppearanceOverlay="@style/RoundedImageView" />

                            <!-- 删除按钮 -->
                            <com.google.android.material.floatingactionbutton.FloatingActionButton
                                android:id="@+id/image_delete_btn"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="top|end"
                                android:layout_margin="8dp"
                                android:contentDescription="删除图片"
                                android:src="@drawable/ic_delete_24"
                                android:visibility="gone"
                                app:backgroundTint="@color/hIndex_red"
                                app:fabSize="mini"
                                app:tint="@color/white" />

                        </FrameLayout>

                        <!-- 图片操作按钮 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_select_image"
                                style="@style/Widget.Material3.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="8dp"
                                android:layout_weight="1"
                                android:text="选择图片"
                                app:icon="@drawable/ic_image_24"
                                app:strokeColor="@color/primary_color" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_take_photo"
                                style="@style/Widget.Material3.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="拍照"
                                app:icon="@drawable/ic_camera_24"
                                app:strokeColor="@color/secondary_color" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 保存按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:text="保存"
                android:textSize="16sp"
                app:backgroundTint="@color/primary_color"
                app:icon="@drawable/ic_save_24"
                app:iconGravity="textStart" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Loading Progress -->
    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progress_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminate="true"
        android:visibility="gone"
        app:indicatorColor="@color/primary_color" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>