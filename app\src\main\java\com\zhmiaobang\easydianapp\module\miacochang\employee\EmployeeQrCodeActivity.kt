package com.zhmiaobang.easydianapp.module.miacochang.employee

import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.View
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.ActivityEmployeeQrCodeBinding
import com.zhmiaobang.easydianapp.json.employee.EmployeeLoginCodeJson
import com.zhmiaobang.easydianapp.json.user.UserJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import java.text.SimpleDateFormat
import java.util.*

/**
 * 员工登录二维码显示Activity
 *
 * 功能特性：
 * - 显示员工登录二维码
 * - 显示员工基本信息
 * - 显示二维码过期时间和倒计时
 * - 自动处理二维码过期
 *
 * <AUTHOR> 4.0 sonnet
 */
class EmployeeQrCodeActivity : BaseActivity() {

    companion object {
        private const val TAG = "EmployeeQrCodeActivity"
        private const val EXTRA_LOGIN_CODE_DATA = "loginCodeData"
        private const val EXTRA_EMPLOYEE = "employee"
    }

    // ViewBinding
    private val binding: ActivityEmployeeQrCodeBinding by lazy {
        ActivityEmployeeQrCodeBinding.inflate(layoutInflater)
    }

    // 数据
    private var loginCodeData: EmployeeLoginCodeJson? = null
    private var employee: UserJson? = null

    // 倒计时器
    private var countDownTimer: CountDownTimer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initializeUI()

        Log.d(TAG, "EmployeeQrCodeActivity创建完成")
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()

        // 获取传入的数据
        loginCodeData = intent.getParcelableExtra(EXTRA_LOGIN_CODE_DATA)
        employee = intent.getParcelableExtra(EXTRA_EMPLOYEE)

        if (loginCodeData == null || employee == null) {
            Log.e(TAG, "缺少必要的数据参数")
            showToast("数据异常，请重试")
            finish()
            return
        }

        setupToolbar()
        bindDataToUI()
        startCountDown()
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        initToolbar(
            toolbar = binding.qrCodeToolbar,
            title = "员工登录二维码",
            showBack = true
        )
    }

    /**
     * 绑定数据到UI
     */
    private fun bindDataToUI() {
        val loginCode = loginCodeData!!
        val emp = employee!!

        // 显示员工信息
        binding.tvEmployeeName.text = emp.nickname ?: "未设置姓名"
        binding.tvEmployeeId.text = "ID: ${emp.id}"
        binding.tvEmployeeRole.text = emp.role.name

        // 加载员工头像
        loadEmployeeAvatar(emp.avatar)

        // 显示二维码
        loadQrCodeImage(loginCode.qrcodeUrl)

        // 显示二维码信息
        binding.tvQrCodeId.text = "二维码ID: ${loginCode.id}"
        binding.tvEmployeeCode.text = "员工代码: ${loginCode.code}"
        binding.tvCreateTime.text = "生成时间: ${formatDateTime(loginCode.createTime)}"
        binding.tvExpireTime.text = "过期时间: ${formatDateTime(loginCode.expire_time)}"

        // 调试日志：显示原始时间格式
        Log.d(TAG, "原始创建时间: ${loginCode.createTime}")
        Log.d(TAG, "原始过期时间: ${loginCode.expire_time}")
        Log.d(TAG, "数据绑定完成，员工: ${emp.nickname}, 二维码ID: ${loginCode.id}")
    }

    /**
     * 加载员工头像
     */
    private fun loadEmployeeAvatar(avatarUrl: String?) {
        binding.ivEmployeeAvatar.load(avatarUrl) {
            crossfade(300)
            placeholder(R.drawable.ic_person)
            error(R.drawable.ic_person)
            transformations(CircleCropTransformation())
        }
    }

    /**
     * 加载二维码图片
     */
    private fun loadQrCodeImage(qrCodeUrl: String) {
        Log.d(TAG, "开始加载二维码图片: $qrCodeUrl")

        binding.ivQrCode.load(qrCodeUrl) {
            crossfade(300)
            placeholder(R.drawable.ic_qr_code)
            error(R.drawable.ic_qr_code)
        }
    }

    /**
     * 格式化日期时间
     */
    private fun formatDateTime(dateTimeString: String): String {
        // 常见的时间格式列表
        val possibleFormats = listOf(
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'",  // ISO格式带微秒和Z
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",     // ISO格式带毫秒和Z
            "yyyy-MM-dd'T'HH:mm:ss'Z'",         // ISO格式带Z
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS",     // ISO格式带微秒
            "yyyy-MM-dd'T'HH:mm:ss.SSS",        // ISO格式带毫秒
            "yyyy-MM-dd'T'HH:mm:ss",            // 标准ISO格式
            "yyyy-MM-dd HH:mm:ss.SSSSSS",       // 空格分隔带微秒
            "yyyy-MM-dd HH:mm:ss.SSS",          // 空格分隔带毫秒
            "yyyy-MM-dd HH:mm:ss",              // 空格分隔标准格式
            "yyyy/MM/dd HH:mm:ss",              // 斜杠分隔
            "dd/MM/yyyy HH:mm:ss",              // 欧洲格式
            "MM/dd/yyyy HH:mm:ss"               // 美国格式
        )

        val outputFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

        for (formatPattern in possibleFormats) {
            try {
                val inputFormat = SimpleDateFormat(formatPattern, Locale.getDefault())
                val date = inputFormat.parse(dateTimeString)
                if (date != null) {
                    Log.d(TAG, "成功解析时间格式: $formatPattern, 原始: $dateTimeString")
                    return outputFormat.format(date)
                }
            } catch (e: Exception) {
                // 继续尝试下一个格式
                continue
            }
        }

        Log.w(TAG, "所有时间格式解析失败: $dateTimeString")
        return dateTimeString // 返回原始字符串
    }

    /**
     * 解析时间字符串为Date对象
     */
    private fun parseDateTime(dateTimeString: String): Date? {
        // 常见的时间格式列表
        val possibleFormats = listOf(
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'",  // ISO格式带微秒和Z
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",     // ISO格式带毫秒和Z
            "yyyy-MM-dd'T'HH:mm:ss'Z'",         // ISO格式带Z
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS",     // ISO格式带微秒
            "yyyy-MM-dd'T'HH:mm:ss.SSS",        // ISO格式带毫秒
            "yyyy-MM-dd'T'HH:mm:ss",            // 标准ISO格式
            "yyyy-MM-dd HH:mm:ss.SSSSSS",       // 空格分隔带微秒
            "yyyy-MM-dd HH:mm:ss.SSS",          // 空格分隔带毫秒
            "yyyy-MM-dd HH:mm:ss",              // 空格分隔标准格式
            "yyyy/MM/dd HH:mm:ss",              // 斜杠分隔
            "dd/MM/yyyy HH:mm:ss",              // 欧洲格式
            "MM/dd/yyyy HH:mm:ss"               // 美国格式
        )

        for (formatPattern in possibleFormats) {
            try {
                val inputFormat = SimpleDateFormat(formatPattern, Locale.getDefault())
                val date = inputFormat.parse(dateTimeString)
                if (date != null) {
                    Log.d(TAG, "成功解析时间: $formatPattern, 原始: $dateTimeString, 结果: $date")
                    return date
                }
            } catch (e: Exception) {
                // 继续尝试下一个格式
                continue
            }
        }

        Log.w(TAG, "时间解析失败: $dateTimeString")
        return null
    }

    /**
     * 开始倒计时
     */
    private fun startCountDown() {
        val loginCode = loginCodeData ?: return

        try {
            val expireDate = parseDateTime(loginCode.expire_time)
            val currentTime = System.currentTimeMillis()
            val expireTime = expireDate?.time ?: currentTime

            val remainingTime = expireTime - currentTime

            if (remainingTime <= 0) {
                Log.d(TAG, "二维码已过期，剩余时间: ${remainingTime}ms")
                showExpiredState()
                return
            }

            Log.d(TAG, "开始倒计时，剩余时间: ${remainingTime}ms (${remainingTime/1000}秒)")

            countDownTimer = object : CountDownTimer(remainingTime, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    val seconds = millisUntilFinished / 1000
                    val minutes = seconds / 60
                    val remainingSeconds = seconds % 60

                    binding.tvCountDown.text = String.format("剩余时间: %02d:%02d", minutes, remainingSeconds)

                    // 当剩余时间少于30秒时，改变颜色提醒
                    if (seconds <= 30) {
                        binding.tvCountDown.setTextColor(getColor(R.color.hIndex_red))
                    }
                }

                override fun onFinish() {
                    showExpiredState()
                }
            }.start()

        } catch (e: Exception) {
            Log.e(TAG, "倒计时启动失败: ${e.message}", e)
            binding.tvCountDown.text = "时间解析失败"
            binding.tvCountDown.setTextColor(getColor(R.color.hIndex_red))
        }
    }

    /**
     * 显示过期状态
     */
    private fun showExpiredState() {
        Log.d(TAG, "二维码已过期")

        binding.tvCountDown.text = "二维码已过期"
        binding.tvCountDown.setTextColor(getColor(R.color.hIndex_red))

        // 可以添加过期后的处理逻辑，比如禁用二维码显示或提示用户重新生成
        binding.ivQrCode.alpha = 0.5f
        binding.tvExpiredHint.visibility = View.VISIBLE

        showToast("二维码已过期，请重新生成")
    }
}