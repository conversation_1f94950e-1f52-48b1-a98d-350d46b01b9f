pluginManagement {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public' }
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url "https://jitpack.io" }

    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        mavenCentral()
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url "https://jitpack.io" }

    }
}

rootProject.name = "EasyDianApp"
include ':app'
include ':opencv'
