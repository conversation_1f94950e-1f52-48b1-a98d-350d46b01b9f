package com.zhmiaobang.easydianapp.module.miacochang.feed

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.adapter.FeedPackageAdapter
import com.zhmiaobang.easydianapp.databinding.ActivityFeedPackageListBinding
import com.zhmiaobang.easydianapp.json.feed.FeedPackageJson
import com.zhmiaobang.easydianapp.libs.basic.BaseActivity
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.utils.PaginationScrollListener
import com.zhmiaobang.easydianapp.viewmodel.feed.FeedPackageViewModel
import kotlin.collections.isNotEmpty

/**
 * 饲料包列表Activity
 *
 * 功能特性：
 * - 展示饲料包列表数据
 * - 点击logo/名称跳转到编辑页面
 * - 支持分页加载和下拉刷新
 * - 完整的加载状态和错误处理
 *
 * <AUTHOR> 4.0 sonnet
 */
class FeedPackageListActivity : BaseActivity() {

    companion object {
        private const val TAG = "FeedPackageListActivity"
        private const val REQUEST_CODE_EDIT = 1001
    }

    // ViewBinding
    private val binding: ActivityFeedPackageListBinding by lazy {
        ActivityFeedPackageListBinding.inflate(layoutInflater)
    }

    // ViewModel
    private val viewModel: FeedPackageViewModel by viewModels()

    // Adapter
    private lateinit var feedPackageAdapter: FeedPackageAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        initializeUI()
        // 手动调用setupObservers，因为BaseActivity的单参数onCreate不会自动调用
        setupObservers()

        Log.d(TAG, "FeedPackageListActivity创建完成，观察者已设置")
    }

    /**
     * 初始化UI组件
     */
    override fun initializeUI() {
        super.initializeUI()
        setupToolbar()
        setupRecyclerView()
        setupSwipeRefresh()

        loadFeedPackageData()
    }

    /**
     * 设置观察者
     */
    override fun setupObservers() {
        super.setupObservers()
        Log.d(TAG, "开始设置观察者")

        // 观察分页数据
        viewModel.paginatedDataObserver.observe(this) { paginatedData ->
            Log.d(TAG, "收到分页数据更新: items=${paginatedData.items.size}, state=${paginatedData.paginationState}")

            try {
                val state = paginatedData.paginationState
                val items = paginatedData.items

                // 处理加载状态
                when (state.loadingState) {
                    LoadingState.LOADING_FIRST -> {
                        if (items.isEmpty()) {
                            showLoading()
                        }
                        binding.listFeedPackageSwipeRefreshLayout.isRefreshing = false
                    }
                    LoadingState.LOADING_MORE -> {
                        hideLoading()
                        binding.listFeedPackageSwipeRefreshLayout.isRefreshing = false
                        feedPackageAdapter.showLoadingMore()
                    }
                    LoadingState.ERROR -> {
                        hideLoading()
                        binding.listFeedPackageSwipeRefreshLayout.isRefreshing = false
                        if (items.isEmpty()) {
                            showError(state.errorMessage ?: "加载失败")
                        } else {
                            feedPackageAdapter.showLoadError(state.errorMessage ?: "加载失败")
                        }
                    }
                    LoadingState.NO_MORE -> {
                        hideLoading()
                        binding.listFeedPackageSwipeRefreshLayout.isRefreshing = false
                        feedPackageAdapter.showNoMoreData()
                    }
                    else -> {
                        hideLoading()
                        binding.listFeedPackageSwipeRefreshLayout.isRefreshing = false
                        if (!state.hasMoreData && items.isNotEmpty()) {
                            feedPackageAdapter.showNoMoreData()
                        }
                    }
                }

                // 更新数据显示
                if (items.isNotEmpty()) {
                    showFeedPackageList(items)
                    Log.d(TAG, "显示饲料包列表，总数量: ${items.size}")
                } else if (state.loadingState != LoadingState.LOADING_FIRST) {
                    showEmptyState()
                }

            } catch (e: Exception) {
                Log.e(TAG, "处理分页数据失败: ${e.message}", e)
                hideLoading()
                binding.listFeedPackageSwipeRefreshLayout.isRefreshing = false
                showError("数据处理失败，请重试")
            }
        }

        // 观察错误信息
        viewModel.errorObserver.observe(this) { errorMessage ->
            Log.e(TAG, "ViewModel错误: $errorMessage")
            hideLoading()
            showError(errorMessage)
        }

        Log.d(TAG, "观察者设置完成")
    }

    /**
     * 设置Toolbar
     */
    private fun setupToolbar() {
        initToolbar(
            toolbar = binding.listFeedPackageToolbar,
            title = "饲料包管理",
            showBack = true
        )
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        feedPackageAdapter = FeedPackageAdapter()
        val layoutManager = LinearLayoutManager(this)

        binding.listFeedPackageRecyclerView.apply {
            this.layoutManager = layoutManager
            adapter = feedPackageAdapter
            setHasFixedSize(true)
        }

        // 设置点击事件监听器
        feedPackageAdapter.setOnItemClickListener(object : FeedPackageAdapter.OnItemClickListener {
            override fun onItemClick(feedPackage: FeedPackageJson) {
                Log.d(TAG, "点击饲料包: ${feedPackage.name}")
                navigateToEditActivity(feedPackage)
            }

            override fun onRetryClick() {
                Log.d(TAG, "点击重试按钮")
                viewModel.retryLoad()
            }
        })

        // 设置分页滚动监听器
        val scrollListener = object : PaginationScrollListener(layoutManager) {
            override fun isLoading(): Boolean {
                return viewModel.getCurrentPaginationState().isLoading
            }

            override fun isLastPage(): Boolean {
                return !viewModel.getCurrentPaginationState().hasMoreData
            }

            override fun loadMoreItems() {
                Log.d(TAG, "滚动触发加载更多")
                viewModel.loadNextPage()
            }
        }

        binding.listFeedPackageRecyclerView.addOnScrollListener(scrollListener)
    }

    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.listFeedPackageSwipeRefreshLayout.setOnRefreshListener {
            Log.d(TAG, "下拉刷新触发")
            refreshData()
        }

        // 设置刷新指示器颜色
        binding.listFeedPackageSwipeRefreshLayout.setColorSchemeResources(
            R.color.primary_color,
            R.color.hIndex_green,
            R.color.hIndex_blue
        )
    }

    /**
     * 加载饲料包数据
     */
    private fun loadFeedPackageData() {
        Log.d(TAG, "开始加载饲料包数据")

        // 检查JWT Token
        val token = com.zhmiaobang.easydianapp.init.ConfigTools.getJwtToken()
        if (token == null) {
            Log.e(TAG, "JWT Token为空，无法发送请求")
            showError("用户未登录，请重新登录")
            return
        }

        // 检查用户信息
        val user = com.zhmiaobang.easydianapp.init.ConfigTools.getUser()
        if (user == null) {
            Log.e(TAG, "用户信息为空")
            showError("用户信息异常，请重新登录")
            return
        }

        try {
            Log.d(TAG, "开始加载第一页饲料包数据...")
            viewModel.loadFirstPage()
            Log.d(TAG, "饲料包数据请求已发送")
        } catch (e: Exception) {
            hideLoading()
            binding.listFeedPackageSwipeRefreshLayout.isRefreshing = false
            val errorMessage = ExceptionUtil.catchException(e)
            showError(errorMessage)
            Log.e(TAG, "加载饲料包数据失败: ${e.message}", e)
        }
    }

    /**
     * 显示饲料包列表
     */
    private fun showFeedPackageList(feedPackages: List<FeedPackageJson>) {
        Log.d(TAG, "显示饲料包列表，数量: ${feedPackages.size}")

        binding.listFeedPackageRecyclerView.visibility = View.VISIBLE
        binding.listFeedPackageEmptyStateLayout.visibility = View.GONE

        feedPackageAdapter.updateData(feedPackages)
    }

    /**
     * 显示空状态
     */
    private fun showEmptyState() {
        Log.d(TAG, "显示空状态")

        binding.listFeedPackageRecyclerView.visibility = View.GONE
        binding.listFeedPackageEmptyStateLayout.visibility = View.VISIBLE
    }

    /**
     * 显示加载状态
     */
    private fun showLoading() {
        binding.listFeedPackageProgressIndicator.visibility = View.VISIBLE
    }

    /**
     * 隐藏加载状态
     */
    private fun hideLoading() {
        binding.listFeedPackageProgressIndicator.visibility = View.GONE
    }

    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        showToast(message)
        showEmptyState()
    }

    /**
     * 跳转到编辑饲料包页面
     */
    private fun navigateToEditActivity(feedPackage: FeedPackageJson?) {
        val intent = Intent(this, EditFeedPackageActivity::class.java)

        feedPackage?.let {
            intent.putExtra("feed_package", it)
        }

        // 使用 startActivityForResult 以便接收返回结果
        startActivityForResult(intent, REQUEST_CODE_EDIT)
    }

    /**
     * 处理Activity返回结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_CODE_EDIT && resultCode == Activity.RESULT_OK) {
            // 编辑成功，刷新列表
            refreshData()
        }
    }

    /**
     * 刷新数据
     */
    private fun refreshData() {
        Log.d(TAG, "刷新饲料包数据")
        viewModel.refresh()
    }
}