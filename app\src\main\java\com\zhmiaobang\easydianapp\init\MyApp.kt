package com.zhmiaobang.easydianapp.init

import android.Manifest
import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.tencent.mmkv.MMKV

import org.opencv.android.OpenCVLoader

class MyApp: Application() {
    companion object {
        const val ANDROID_ENCRYPT_KEY = "BeBUxB7rN4b9KG/knoebzdNhSM1TKWdqRsAI/6l8MbQ="
        private const val ROLE_BOSS = "6666"
        private const val ROLE_EMPLOYEE = "4444"
        private const val ROLE_FARMER = "8888"
        private const val ROLE_AGENT = "7777"
        const val REQUEST_CODE_SCAN_ONE = 19821
        const val SEARCH_ACTIVATE_COMPANY = "search_activate_company"
        const val QRCODE_ACTIVATE_MIAOCHANG = "act_miaochang_qrcode"
        const val SELECT_MIAOCHANG_CATE = "SELECT_MIAOCHANG_CATE"
        const val EVENT_SELECT_COMPANY_CATE = "select_company_cate"
        const val LOGIN_ACTIVATE_COMPANY_CODE = "actcompanyCode"
        const val INVITE_COMPANY_BOSS = "invite_company_boss"
        const val ONNX_PATH = "onnx_models"
        const val TAG_OPENCV_COUNT_STEP = "TAG_OPENCV_COUNT_STEP"
        const val TAG_COUNT_COMPLETE = "TAG_COUNT_COMPLETE"
        const val ACT_MIAOCHANG_INTENT = "invite_miaochang_act"
        const val COUNT_TRANSFER = "count_transfer"

        private var applicationInstance: MyApp? = null


        /**
         * 返回上下文
         *
         * @return
         */
        val context: Context
            get() = applicationInstance!!

        // Permission arrays should be vals, not vars, as they shouldn't be reassigned
        val permissionScan: Array<String> =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                arrayOf(
                    Manifest.permission.CAMERA,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_IMAGES,
                )
            } else {
                arrayOf(
                    Manifest.permission.CAMERA,
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                )
            }



        val permissionReadWrite: Array<String> =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                arrayOf(
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_IMAGES,

                    )
            } else {
                arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
        val permissionCameraWithGps: Array<String> =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                arrayOf(
                    Manifest.permission.CAMERA,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                )
            } else {
                arrayOf(
                    Manifest.permission.CAMERA,
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                )
            }

        val permissionGps: Array<String> = arrayOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
        )

        @RequiresApi(Build.VERSION_CODES.R)
        val permission_query_all_app=
            arrayOf(
                Manifest.permission.QUERY_ALL_PACKAGES
            )

    }

    @SuppressLint("SuspiciousIndentation")
    override fun onCreate() {
        super.onCreate()
        applicationInstance = this

        MMKV.initialize(this)
        initializeOpenCv()


    }

    private fun initializeOpenCv() {
        if (!OpenCVLoader.initDebug()) {
            Log.e("OpenCV", "Unable to load OpenCV!")
        } else {
            Log.d("OpenCV", "OpenCV loaded Successfully!")
        }
    }

    override fun onTerminate() {
        super.onTerminate()
    }
}