package com.zhmiaobang.easydianapp.libs.basic

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.PersistableBundle
import android.provider.MediaStore
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.FileProvider
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.android.material.appbar.MaterialToolbar
import com.zhmiaobang.easydianapp.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * 基础Activity类 - 提供通用功能和UI设置
 * 优化版本 by Claude 4.0 sonnet
 */
open class BaseActivity : AppCompatActivity() {

    companion object {
        private const val IMAGE_COMPRESS_QUALITY_HIGH = 90
        private const val IMAGE_COMPRESS_QUALITY_NORMAL = 80
        private const val IMAGE_COMPRESS_QUALITY_LOW = 70
        private const val SHARE_IMAGE_PREFIX = "share_image_"
        private const val IMAGE_EXTENSION = ".jpg"
    }

    // ==================== 权限管理 ====================
    
    open val requestPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            val isGranted = permissions.entries.all { it.value }
            if (isGranted) {
                onPermissionGranted()
            } else {
                onPermissionDenied()
            }
        }

    // ==================== 生命周期方法 ====================

    @SuppressLint("SourceLockedOrientationActivity")
    override fun onCreate(savedInstanceState: Bundle?, persistentState: PersistableBundle?) {
        super.onCreate(savedInstanceState, persistentState)
        setupWindowFlags()
        setupSystemUI()
//        initializeUI()
//        setupObservers()
    }

    override fun onResume() {
        super.onResume()
        onActivityResumed()
    }

    override fun onDestroy() {
        onActivityDestroyed()
        super.onDestroy()
    }

    // ==================== 初始化方法 ====================

    /**
     * 设置窗口标志
     */
    private fun setupWindowFlags() {
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        window.apply {
            addFlags(WindowManager.LayoutParams.FLAG_SECURE)
            addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    /**
     * 设置系统UI
     */
    private fun setupSystemUI() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        window.apply {
            statusBarColor = Color.TRANSPARENT
            navigationBarColor = Color.TRANSPARENT
        }
    }

    // ==================== 抽象方法（子类实现） ====================

    /**
     * 初始化UI - 子类重写
     */
    protected open fun initializeUI() {}

    /**
     * 设置观察者 - 子类重写
     */
    protected open fun setupObservers() {}

    /**
     * 权限授予回调 - 子类重写
     */
    protected open fun onPermissionGranted() {}

    /**
     * 权限拒绝回调 - 子类重写
     */
    protected open fun onPermissionDenied() {}

    /**
     * Activity恢复回调 - 子类重写
     */
    protected open fun onActivityResumed() {}

    /**
     * Activity销毁回调 - 子类重写
     */
    protected open fun onActivityDestroyed() {}

    // ==================== 图片处理工具 ====================

    /**
     * 从URI获取真实路径
     */
    fun getRealPathFromURI(uri: Uri): String? {
        return try {
            val projection = arrayOf(MediaStore.Images.Media.DATA)
            contentResolver.query(uri, projection, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
                    cursor.getString(columnIndex)
                } else null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 从View生成Bitmap
     */
    fun getBitmapFromView(view: View): Bitmap? {
        return try {
            if (view.width <= 0 || view.height <= 0) return null
            
            val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            view.draw(canvas)
            bitmap
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 保存Bitmap到文件（异步）
     */
    fun saveBitmapToFileAsync(
        bitmap: Bitmap,
        file: File? = null,
        quality: Int = IMAGE_COMPRESS_QUALITY_NORMAL,
        callback: (Uri?) -> Unit
    ) {
        lifecycleScope.launch {
            val uri = withContext(Dispatchers.IO) {
                saveBitmapToFileSync(bitmap, file, quality)
            }
            callback(uri)
        }
    }

    /**
     * 保存Bitmap到文件（同步）
     */
    private fun saveBitmapToFileSync(
        bitmap: Bitmap,
        file: File? = null,
        quality: Int = IMAGE_COMPRESS_QUALITY_NORMAL
    ): Uri? {
        return try {
            val outputFile = file ?: createDefaultImageFile()
            
            FileOutputStream(outputFile).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, out)
            }
            
            FileProvider.getUriForFile(
                this,
                "${applicationContext.packageName}.provider",
                outputFile
            )
        } catch (e: IOException) {
            null
        }
    }

    /**
     * 创建默认图片文件
     */
    private fun createDefaultImageFile(): File {
        val fileName = "$SHARE_IMAGE_PREFIX${System.currentTimeMillis()}$IMAGE_EXTENSION"
        return File(getExternalFilesDir(null), fileName)
    }

    /**
     * 获取输出目录
     */
    fun getOutputDirectory(): File {
        val mediaDir = externalMediaDirs?.firstOrNull()?.let { dir ->
            File(dir, resources.getString(R.string.app_name)).apply { 
                if (!exists()) mkdirs() 
            }
        }
        return if (mediaDir?.exists() == true) mediaDir else filesDir
    }

    // ==================== UI工具方法 ====================

    /**
     * 设置Toolbar顶部偏移
     */
    fun setToolbarTop(rootView: View, moveView: View) {
        ViewCompat.setOnApplyWindowInsetsListener(rootView) { _, insets ->
            val systemWindow = insets.getInsets(
                WindowInsetsCompat.Type.systemBars() or
                        WindowInsetsCompat.Type.displayCutout()
            )
            moveView.setPadding(0, systemWindow.top, 0, 0)
            insets
        }
    }

    /**
     * 初始化Toolbar
     */
    fun initToolbar(
        toolbar: MaterialToolbar,
        title: String,
        showBack: Boolean = true,
        onBackClick: (() -> Unit)? = null
    ) {
        // 首先设置为支持的ActionBar
        setSupportActionBar(toolbar)

        toolbar.apply {
            if (showBack) {
                setNavigationIcon(R.drawable.ic_arrow_back_24)
                setNavigationOnClickListener {
                    Log.d("BaseActivity", "Toolbar返回按钮被点击，当前Activity: ${<EMAIL>}")
                    if (onBackClick != null) {
                        onBackClick.invoke()
                    } else {
                        // 使用 this@BaseActivity 确保调用的是当前Activity实例的方法
                        <EMAIL>()
                    }
                }

                // 设置ActionBar显示返回按钮
                supportActionBar?.setDisplayHomeAsUpEnabled(true)
                supportActionBar?.setDisplayShowHomeEnabled(false)
            } else {
                setLogo(R.drawable.ic_logout)
                supportActionBar?.setDisplayHomeAsUpEnabled(false)
            }

            isTitleCentered = true
            isSubtitleCentered = true
            this.title = title
        }
    }

    /**
     * 处理ActionBar菜单项点击事件
     */
    override fun onOptionsItemSelected(item: android.view.MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                Log.d("BaseActivity", "ActionBar返回按钮被点击，当前Activity: ${this.javaClass.simpleName}")
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    // ==================== Fragment管理 ====================

    /**
     * 切换Fragment
     */
    fun switchFragment(fragment: Fragment, containerId: Int, addToBackStack: Boolean = false) {
        val transaction = supportFragmentManager.beginTransaction()
            .replace(containerId, fragment)
            
        if (addToBackStack) {
            transaction.addToBackStack(null)
        }
        
        transaction.commit()
    }

    // ==================== 导航工具 ====================

    /**
     * 跳转到指定Activity
     */
    inline fun <reified T : Activity> navigateToActivity(
        context: Context = this,
        isFinished: Boolean = false,
        extras: Bundle? = null
    ) {
        val intent = Intent(context, T::class.java)
        extras?.let { intent.putExtras(it) }
        startActivity(intent)
        if (isFinished) finish()
    }

    // ==================== 通用工具 ====================

    /**
     * 显示Toast消息
     */
    fun showToast(message: String, context: Context = this, duration: Int = Toast.LENGTH_LONG) {
        Toast.makeText(context, message, duration).show()
    }

    /**
     * 安全地执行操作
     */
    protected inline fun safeExecute(action: () -> Unit) {
        try {
            action()
        } catch (e: Exception) {
            // 可以在这里添加日志记录
            showToast("操作失败，请重试")
        }
    }

    // ==================== 兼容性方法（保持向后兼容） ====================

    @Deprecated("使用 initToolbar 替代", ReplaceWith("initToolbar(toolbar, title, showBack)"))
    fun init_toolbar(toolbar: MaterialToolbar, title: String, showBack: Boolean = true) {
        initToolbar(toolbar, title, showBack)
    }

    @Deprecated("使用 navigateToActivity 替代", ReplaceWith("navigateToActivity<T>(context, isFinished)"))
    inline fun <reified T : Activity> gotoActivity(context: Context, isFinished: Boolean = false) {
        navigateToActivity<T>(context, isFinished)
    }

    @Deprecated("使用 onPermissionGranted 替代", ReplaceWith("onPermissionGranted()"))
    open fun enableRight() {
        onPermissionGranted()
    }

    @Deprecated("使用 onPermissionDenied 替代", ReplaceWith("onPermissionDenied()"))
    open fun noAuthRight() {
        onPermissionDenied()
    }

    @Deprecated("使用 initializeUI 替代", ReplaceWith("initializeUI()"))
    open fun initui() {
        initializeUI()
    }

    @Deprecated("使用 setupObservers 替代", ReplaceWith("setupObservers()"))
    open fun observer() {
        setupObservers()
    }
}
