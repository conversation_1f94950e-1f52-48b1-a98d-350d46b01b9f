package com.zhmiaobang.easydianapp.module.count

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.exifinterface.media.ExifInterface
import androidx.lifecycle.lifecycleScope
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.button.MaterialButton
import com.google.android.material.progressindicator.LinearProgressIndicator
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.json.count.BatchCountJson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.opencv.android.Utils
import org.opencv.core.*
import org.opencv.imgproc.Imgproc
import java.io.File
import java.io.IOException
import kotlin.math.*

/**
 * 虾苗特征数据类
 */
data class ShrimpFeatures(
    val area: Double,
    val aspectRatio: Double,
    val convexityRatio: Double,
    val boundingRect: org.opencv.core.Rect
)

class CountResultActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "CountResultActivity"

        // 虾苗检测参数
        private const val MIN_SHRIMP_AREA = 50.0
        private const val MAX_SHRIMP_AREA = 500.0
        private const val MIN_ASPECT_RATIO = 0.4
        private const val MAX_ASPECT_RATIO = 2.5
        private const val MIN_CONVEXITY_RATIO = 0.6
        private const val MAX_IMAGE_SIZE = 2048
        private const val BLOCK_SIZE = 1024
        private const val OVERLAP_SIZE = 128

        // 图像处理参数
        private const val GAUSSIAN_KERNEL_SIZE = 5
        private const val MORPH_OPEN_KERNEL_SIZE = 3
        private const val MORPH_CLOSE_KERNEL_SIZE = 5
        private const val ADAPTIVE_THRESH_BLOCK_SIZE = 11
        private const val ADAPTIVE_THRESH_C = 2.0
    }

    private var batchJson: BatchCountJson? = null
    private lateinit var toolbar: MaterialToolbar
    private lateinit var imageView: ImageView

    // 计数功能相关 UI 组件
    private lateinit var countingContainer: LinearLayout
    private lateinit var startButton: MaterialButton
    private lateinit var retryButton: MaterialButton
    private lateinit var progressIndicator: LinearProgressIndicator
    private lateinit var statusText: TextView
    private lateinit var countDisplayContainer: LinearLayout
    private lateinit var countDisplay: TextView

    // 计数状态
    private var isCountingInProgress = false
    private var currentShrimpCount = 0

    // OpenCV相关
    private var isOpenCVLoaded = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_count_result)

        // 获取传递的数据
        batchJson = intent.getParcelableExtra("batch_count")

        // 初始化视图
        initViews()

        // 设置系统栏
        setupSystemBars()

        // 设置工具栏
        setupToolbar()

        // 设置UI内容
        setupUI()
    }

    override fun onResume() {
        super.onResume()
        try {
            // 尝试加载OpenCV库
            System.loadLibrary("opencv_java4")
            isOpenCVLoaded = true
            Log.d(TAG, "OpenCV库加载成功")
        } catch (e: UnsatisfiedLinkError) {
            Log.e(TAG, "无法加载OpenCV库: ${e.message}")
            isOpenCVLoaded = false
        }
    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        toolbar = findViewById(R.id.count_result_toolbar)
        imageView = findViewById(R.id.count_result_image)

        // 计数功能相关组件
        countingContainer = findViewById(R.id.count_result_counting_container)
        startButton = findViewById(R.id.count_result_start_button)
        retryButton = findViewById(R.id.count_result_retry_button)
        progressIndicator = findViewById(R.id.count_result_progress)
        statusText = findViewById(R.id.count_result_status)
        countDisplayContainer = findViewById(R.id.count_result_display_container)
        countDisplay = findViewById(R.id.count_result_count_display)

        // 设置点击事件
        setupClickListeners()
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        startButton.setOnClickListener {
            if (!isCountingInProgress) {
                startShrimpCounting()
            }
        }

        retryButton.setOnClickListener {
            if (!isCountingInProgress) {
                resetCountingUI()
                startShrimpCounting()
            }
        }
    }

    /**
     * 设置系统栏
     */
    private fun setupSystemBars() {
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())

            // 为 Toolbar 设置顶部内边距
            val layoutParams = toolbar.layoutParams

            // 获取 actionBarSize
            val typedValue = android.util.TypedValue()
            val actionBarSize = if (theme.resolveAttribute(android.R.attr.actionBarSize, typedValue, true)) {
                resources.getDimensionPixelSize(typedValue.resourceId)
            } else {
                (56 * resources.displayMetrics.density).toInt()
            }

            layoutParams.height = actionBarSize + systemBars.top
            toolbar.layoutParams = layoutParams
            toolbar.setPadding(
                toolbar.paddingLeft,
                systemBars.top,
                toolbar.paddingRight,
                toolbar.paddingBottom
            )

            // 主容器不设置内边距
            v.setPadding(0, 0, 0, systemBars.bottom)
            insets
        }
    }

    /**
     * 设置工具栏
     */
    private fun setupToolbar() {
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(false)

        // 设置标题和副标题
        batchJson?.let { json ->
            supportActionBar?.title = json.cate
            supportActionBar?.subtitle = json.phone
        }

        // 设置返回按钮点击事件
        toolbar.setNavigationOnClickListener {
            onBackPressed()
        }

        // 确保标题和副标题居中显示
        toolbar.setTitleCentered(true)
        toolbar.setSubtitleCentered(true)
    }

    /**
     * 设置UI内容
     */
    private fun setupUI() {
        loadAndDisplayImage()
    }

    /**
     * 加载并显示图片
     */
    private fun loadAndDisplayImage() {
        batchJson?.imgSrc?.let { imagePath ->
            if (imagePath.isNotEmpty()) {
                try {
                    val imageFile = File(imagePath)
                    if (imageFile.exists()) {
                        val correctedBitmap = loadAndCorrectBitmap(imagePath)
                        if (correctedBitmap != null) {
                            imageView.setImageBitmap(correctedBitmap)
                            Log.d(TAG, "图片加载成功: $imagePath")
                        } else {
                            showImageLoadError("图片解码失败")
                        }
                    } else {
                        showImageLoadError("图片文件不存在")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "加载图片失败: ${e.message}", e)
                    showImageLoadError("加载图片时发生错误")
                }
            } else {
                showImageLoadError("图片路径为空")
            }
        } ?: run {
            showImageLoadError("未找到图片数据")
        }
    }

    /**
     * 加载图片并修正旋转方向
     */
    private fun loadAndCorrectBitmap(imagePath: String): Bitmap? {
        return try {
            // 首先获取图片的EXIF信息
            val exif = ExifInterface(imagePath)
            val orientation = exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )

            // 加载原始图片
            val originalBitmap = BitmapFactory.decodeFile(imagePath)
            if (originalBitmap == null) {
                Log.e(TAG, "无法解码图片文件: $imagePath")
                return null
            }

            // 根据EXIF信息计算需要旋转的角度
            val rotationAngle = when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> 90f
                ExifInterface.ORIENTATION_ROTATE_180 -> 180f
                ExifInterface.ORIENTATION_ROTATE_270 -> 270f
                ExifInterface.ORIENTATION_FLIP_HORIZONTAL -> 0f // 水平翻转，暂不处理
                ExifInterface.ORIENTATION_FLIP_VERTICAL -> 0f // 垂直翻转，暂不处理
                ExifInterface.ORIENTATION_TRANSPOSE -> 0f // 复杂变换，暂不处理
                ExifInterface.ORIENTATION_TRANSVERSE -> 0f // 复杂变换，暂不处理
                else -> 0f // ORIENTATION_NORMAL 或未知
            }

            // 如果不需要旋转，直接返回原图
            if (rotationAngle == 0f) {
                Log.d(TAG, "图片无需旋转，EXIF方向: $orientation")
                return originalBitmap
            }

            // 创建旋转矩阵
            val matrix = Matrix()
            matrix.postRotate(rotationAngle)

            // 应用旋转
            val rotatedBitmap = Bitmap.createBitmap(
                originalBitmap,
                0,
                0,
                originalBitmap.width,
                originalBitmap.height,
                matrix,
                true
            )

            // 释放原始图片内存
            if (rotatedBitmap != originalBitmap) {
                originalBitmap.recycle()
            }

            Log.d(TAG, "图片旋转完成: ${rotationAngle}度, EXIF方向: $orientation")
            rotatedBitmap

        } catch (e: Exception) {
            Log.e(TAG, "处理图片旋转失败: ${e.message}", e)
            // 如果处理失败，尝试直接加载原图
            BitmapFactory.decodeFile(imagePath)
        }
    }

    /**
     * 显示图片加载错误
     */
    private fun showImageLoadError(message: String) {
        Log.w(TAG, "图片加载错误: $message")
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        // 可以设置一个默认的占位图片
        imageView.setImageResource(R.drawable.ic_shrimp_count)
    }

    // ==================== 虾苗计数功能实现 ====================

    /**
     * 启动虾苗计数流程
     */
    private fun startShrimpCounting() {
        val imagePath = batchJson?.imgSrc
        if (imagePath.isNullOrEmpty()) {
            showCountingError("图片路径为空，无法进行计数")
            return
        }

        val imageFile = File(imagePath)
        if (!imageFile.exists()) {
            showCountingError("图片文件不存在")
            return
        }

        if (!isOpenCVLoaded) {
            showCountingError("OpenCV未加载完成，请稍后再试")
            return
        }

        isCountingInProgress = true
        updateCountingProgress(0, "正在加载图片...")

        lifecycleScope.launch {
            try {
                val result = withContext(Dispatchers.Default) {
                    performShrimpCounting(imagePath)
                }

                withContext(Dispatchers.Main) {
                    if (result >= 0) {
                        displayFinalResult(result)
                    } else {
                        showCountingError("计数过程中发生错误")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "虾苗计数失败: ${e.message}", e)
                withContext(Dispatchers.Main) {
                    showCountingError("计数失败: ${e.message}")
                }
            } finally {
                withContext(Dispatchers.Main) {
                    isCountingInProgress = false
                }
            }
        }
    }

    /**
     * 执行虾苗计数（在后台线程执行）
     */
    private suspend fun performShrimpCounting(imagePath: String): Int {
        var originalMat: Mat? = null
        var processedMat: Mat? = null

        try {
            // 步骤1: 加载和预处理图片
            withContext(Dispatchers.Main) {
                updateCountingProgress(10, "正在加载图片...")
            }

            originalMat = loadImageAsMat(imagePath) ?: return -1

            withContext(Dispatchers.Main) {
                updateCountingProgress(20, "正在缩放图片...")
            }

            val scaledMat = scaleImageForProcessing(originalMat)
            originalMat.release()
            originalMat = scaledMat

            // 步骤2: 图像预处理
            withContext(Dispatchers.Main) {
                updateCountingProgress(30, "正在进行图像预处理...")
            }

            processedMat = preprocessImage(originalMat)

            // 步骤3: 检测虾苗轮廓
            withContext(Dispatchers.Main) {
                updateCountingProgress(50, "正在检测虾苗轮廓...")
            }

            val contours = detectShrimpContours(processedMat)

            // 步骤4: 分离粘连虾苗
            withContext(Dispatchers.Main) {
                updateCountingProgress(70, "正在分离粘连虾苗...")
            }

            val separatedContours = separateOverlappingShrimps(contours, processedMat)

            // 步骤5: 过滤有效虾苗
            withContext(Dispatchers.Main) {
                updateCountingProgress(85, "正在过滤有效虾苗...")
            }

            val validShrimps = filterValidShrimps(separatedContours)

            withContext(Dispatchers.Main) {
                updateCountingProgress(100, "计数完成")
            }

            return validShrimps.size

        } catch (e: Exception) {
            Log.e(TAG, "虾苗计数处理失败: ${e.message}", e)
            return -1
        } finally {
            // 释放资源
            originalMat?.release()
            processedMat?.release()
        }
    }

    /**
     * 加载图片为 OpenCV Mat 对象
     */
    private fun loadImageAsMat(imagePath: String): Mat? {
        return try {
            val bitmap = loadAndCorrectBitmap(imagePath) ?: return null
            val mat = Mat()
            Utils.bitmapToMat(bitmap, mat)
            bitmap.recycle()

            // 转换为 BGR 格式（OpenCV 默认格式）
            val bgrMat = Mat()
            Imgproc.cvtColor(mat, bgrMat, Imgproc.COLOR_RGBA2BGR)
            mat.release()

            Log.d(TAG, "图片加载成功，尺寸: ${bgrMat.width()} x ${bgrMat.height()}")
            bgrMat
        } catch (e: Exception) {
            Log.e(TAG, "加载图片为Mat失败: ${e.message}", e)
            null
        }
    }

    /**
     * 缩放图片以优化处理性能
     */
    private fun scaleImageForProcessing(originalMat: Mat): Mat {
        val originalWidth = originalMat.width()
        val originalHeight = originalMat.height()
        val maxDimension = max(originalWidth, originalHeight)

        return if (maxDimension > MAX_IMAGE_SIZE) {
            val scaleFactor = MAX_IMAGE_SIZE.toDouble() / maxDimension
            val newWidth = (originalWidth * scaleFactor).toInt()
            val newHeight = (originalHeight * scaleFactor).toInt()

            val scaledMat = Mat()
            Imgproc.resize(originalMat, scaledMat, Size(newWidth.toDouble(), newHeight.toDouble()))

            Log.d(TAG, "图片已缩放: ${originalWidth}x${originalHeight} -> ${newWidth}x${newHeight}")
            scaledMat
        } else {
            Log.d(TAG, "图片无需缩放: ${originalWidth}x${originalHeight}")
            originalMat.clone()
        }
    }

    /**
     * 图像预处理管道
     */
    private fun preprocessImage(inputMat: Mat): Mat {
        var currentMat = inputMat
        var tempMat: Mat

        try {
            // 1. 转换为灰度图
            tempMat = Mat()
            Imgproc.cvtColor(currentMat, tempMat, Imgproc.COLOR_BGR2GRAY)
            currentMat = tempMat

            // 2. 高斯模糊
            tempMat = Mat()
            Imgproc.GaussianBlur(
                currentMat, tempMat,
                Size(GAUSSIAN_KERNEL_SIZE.toDouble(), GAUSSIAN_KERNEL_SIZE.toDouble()),
                0.0
            )
            currentMat.release()
            currentMat = tempMat

            // 3. 自适应阈值
            tempMat = Mat()
            Imgproc.adaptiveThreshold(
                currentMat, tempMat,
                255.0,
                Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C,
                Imgproc.THRESH_BINARY_INV,
                ADAPTIVE_THRESH_BLOCK_SIZE,
                ADAPTIVE_THRESH_C
            )
            currentMat.release()
            currentMat = tempMat

            // 4. 形态学开运算（去除噪点）
            val openKernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_ELLIPSE,
                Size(MORPH_OPEN_KERNEL_SIZE.toDouble(), MORPH_OPEN_KERNEL_SIZE.toDouble())
            )
            tempMat = Mat()
            Imgproc.morphologyEx(currentMat, tempMat, Imgproc.MORPH_OPEN, openKernel)
            currentMat.release()
            currentMat = tempMat
            openKernel.release()

            // 5. 形态学闭运算（填充空洞）
            val closeKernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_ELLIPSE,
                Size(MORPH_CLOSE_KERNEL_SIZE.toDouble(), MORPH_CLOSE_KERNEL_SIZE.toDouble())
            )
            tempMat = Mat()
            Imgproc.morphologyEx(currentMat, tempMat, Imgproc.MORPH_CLOSE, closeKernel)
            currentMat.release()
            currentMat = tempMat
            closeKernel.release()

            Log.d(TAG, "图像预处理完成")
            return currentMat

        } catch (e: Exception) {
            Log.e(TAG, "图像预处理失败: ${e.message}", e)
            currentMat.release()
            throw e
        }
    }

    /**
     * 检测虾苗轮廓
     */
    private fun detectShrimpContours(processedMat: Mat): List<MatOfPoint> {
        val contours = mutableListOf<MatOfPoint>()
        val hierarchy = Mat()

        try {
            Imgproc.findContours(
                processedMat,
                contours,
                hierarchy,
                Imgproc.RETR_EXTERNAL,
                Imgproc.CHAIN_APPROX_SIMPLE
            )

            Log.d(TAG, "检测到 ${contours.size} 个轮廓")
            return contours

        } catch (e: Exception) {
            Log.e(TAG, "轮廓检测失败: ${e.message}", e)
            return emptyList()
        } finally {
            hierarchy.release()
        }
    }

    /**
     * 分离粘连重叠的虾苗
     */
    private fun separateOverlappingShrimps(contours: List<MatOfPoint>, processedMat: Mat): List<MatOfPoint> {
        val separatedContours = mutableListOf<MatOfPoint>()

        for (contour in contours) {
            val area = Imgproc.contourArea(contour)

            if (area > MAX_SHRIMP_AREA) {
                // 大面积轮廓可能是粘连的虾苗，尝试分离
                val separated = separateContourUsingWatershed(contour, processedMat)
                separatedContours.addAll(separated)
            } else {
                // 正常大小的轮廓直接添加
                separatedContours.add(contour)
            }
        }

        Log.d(TAG, "分离后轮廓数量: ${separatedContours.size}")
        return separatedContours
    }

    /**
     * 使用分水岭算法分离单个轮廓
     */
    private fun separateContourUsingWatershed(contour: MatOfPoint, processedMat: Mat): List<MatOfPoint> {
        val result = mutableListOf<MatOfPoint>()
        var mask: Mat? = null
        var distTransform: Mat? = null
        var markers: Mat? = null

        try {
            // 创建轮廓掩码
            mask = Mat.zeros(processedMat.size(), CvType.CV_8UC1)
            Imgproc.fillPoly(mask, listOf(contour), Scalar(255.0))

            // 距离变换
            distTransform = Mat()
            Imgproc.distanceTransform(mask, distTransform, Imgproc.DIST_L2, 5)

            // 查找局部最大值作为种子点
            val localMaxima = Mat()
            Imgproc.threshold(distTransform, localMaxima, 0.3 * 255, 255.0, Imgproc.THRESH_BINARY)

            // 查找连通组件作为标记
            markers = Mat()
            localMaxima.convertTo(markers, CvType.CV_32S)

            val numLabels = Imgproc.connectedComponents(localMaxima, markers)

            if (numLabels > 2) { // 至少有2个虾苗（背景是标签0）
                // 应用分水岭算法
                val colorImage = Mat()
                Imgproc.cvtColor(processedMat, colorImage, Imgproc.COLOR_GRAY2BGR)
                Imgproc.watershed(colorImage, markers)

                // 提取分离后的轮廓
                for (label in 1 until numLabels) {
                    val labelMask = Mat()
                    Core.compare(markers, Scalar(label.toDouble()), labelMask, Core.CMP_EQ)

                    val labelContours = mutableListOf<MatOfPoint>()
                    val labelHierarchy = Mat()
                    Imgproc.findContours(labelMask, labelContours, labelHierarchy, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE)

                    result.addAll(labelContours)

                    labelMask.release()
                    labelHierarchy.release()
                }

                colorImage.release()
            } else {
                // 分离失败，返回原轮廓
                result.add(contour)
            }

            localMaxima.release()

        } catch (e: Exception) {
            Log.e(TAG, "分水岭分离失败: ${e.message}", e)
            result.clear()
            result.add(contour) // 返回原轮廓
        } finally {
            mask?.release()
            distTransform?.release()
            markers?.release()
        }

        return result
    }

    /**
     * 过滤有效的虾苗轮廓
     */
    private fun filterValidShrimps(contours: List<MatOfPoint>): List<MatOfPoint> {
        val validShrimps = mutableListOf<MatOfPoint>()

        for (contour in contours) {
            if (isValidShrimpContour(contour)) {
                validShrimps.add(contour)
            }
        }

        Log.d(TAG, "有效虾苗数量: ${validShrimps.size}")
        return validShrimps
    }

    /**
     * 验证轮廓是否为有效虾苗
     */
    private fun isValidShrimpContour(contour: MatOfPoint): Boolean {
        try {
            val features = calculateContourFeatures(contour)

            // 面积过滤
            if (features.area < MIN_SHRIMP_AREA || features.area > MAX_SHRIMP_AREA) {
                return false
            }

            // 长宽比过滤
            if (features.aspectRatio < MIN_ASPECT_RATIO || features.aspectRatio > MAX_ASPECT_RATIO) {
                return false
            }

            // 凸包面积比过滤（排除过于不规则的形状）
            if (features.convexityRatio < MIN_CONVEXITY_RATIO) {
                return false
            }

            return true

        } catch (e: Exception) {
            Log.e(TAG, "轮廓验证失败: ${e.message}", e)
            return false
        }
    }

    /**
     * 计算轮廓特征
     */
    private fun calculateContourFeatures(contour: MatOfPoint): ShrimpFeatures {
        val area = Imgproc.contourArea(contour)
        val boundingRect = Imgproc.boundingRect(contour)

        // 计算长宽比
        val aspectRatio = max(boundingRect.width, boundingRect.height).toDouble() /
                         min(boundingRect.width, boundingRect.height).toDouble()

        // 计算凸包面积比
        val hull = MatOfInt()
        Imgproc.convexHull(contour, hull)
        val hullPoints = mutableListOf<Point>()
        val contourArray = contour.toArray()
        val hullIndices = hull.toArray()

        for (index in hullIndices) {
            hullPoints.add(contourArray[index])
        }

        val hullContour = MatOfPoint(*hullPoints.toTypedArray())
        val hullArea = Imgproc.contourArea(hullContour)
        val convexityRatio = if (hullArea > 0) area / hullArea else 0.0

        hull.release()
        hullContour.release()

        return ShrimpFeatures(
            area = area,
            aspectRatio = aspectRatio,
            convexityRatio = convexityRatio,
            boundingRect = boundingRect
        )
    }

    // ==================== UI 更新和错误处理 ====================

    /**
     * 更新计数进度
     */
    private fun updateCountingProgress(progress: Int, status: String) {
        runOnUiThread {
            progressIndicator.visibility = View.VISIBLE
            progressIndicator.progress = progress
            statusText.text = status
            statusText.visibility = View.VISIBLE

            // 隐藏按钮和结果显示
            startButton.visibility = View.GONE
            retryButton.visibility = View.GONE
            countDisplayContainer.visibility = View.GONE
        }
    }

    /**
     * 显示最终计数结果
     */
    private fun displayFinalResult(count: Int) {
        currentShrimpCount = count

        runOnUiThread {
            // 隐藏进度指示器
            progressIndicator.visibility = View.GONE
            statusText.visibility = View.GONE

            // 显示结果
            countDisplay.text = count.toString()
            countDisplayContainer.visibility = View.VISIBLE
            retryButton.visibility = View.VISIBLE

            // 显示成功消息
            Toast.makeText(this@CountResultActivity, "计数完成！检测到 $count 个虾苗", Toast.LENGTH_LONG).show()

            Log.d(TAG, "虾苗计数完成，结果: $count")
        }
    }

    /**
     * 显示计数错误
     */
    private fun showCountingError(message: String) {
        runOnUiThread {
            // 隐藏进度指示器
            progressIndicator.visibility = View.GONE

            // 显示错误状态
            statusText.text = "计数失败: $message"
            statusText.visibility = View.VISIBLE

            // 显示重试按钮
            startButton.visibility = View.VISIBLE
            retryButton.visibility = View.VISIBLE
            countDisplayContainer.visibility = View.GONE

            Toast.makeText(this@CountResultActivity, "计数失败: $message", Toast.LENGTH_LONG).show()

            Log.e(TAG, "虾苗计数失败: $message")
        }
    }

    /**
     * 重置计数UI状态
     */
    private fun resetCountingUI() {
        runOnUiThread {
            progressIndicator.visibility = View.GONE
            statusText.text = "点击开始计数按钮开始检测虾苗"
            statusText.visibility = View.VISIBLE
            startButton.visibility = View.VISIBLE
            retryButton.visibility = View.GONE
            countDisplayContainer.visibility = View.GONE
            currentShrimpCount = 0
        }
    }
}