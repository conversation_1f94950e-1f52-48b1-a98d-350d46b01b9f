package com.zhmiaobang.easydianapp.module.home

import androidx.fragment.app.viewModels
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import coil3.load
import coil3.request.crossfade
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.databinding.FragmentHomeProfileBinding
import com.zhmiaobang.easydianapp.json.user.UserJson
import com.zhmiaobang.easydianapp.module.profile.MiaoChangMediaEditActivity
import com.zhmiaobang.easydianapp.module.miacochang.feed.FeedPointListActivity
import com.zhmiaobang.easydianapp.module.miacochang.feed.FeedPackageListActivity
import com.zhmiaobang.easydianapp.module.miacochang.employee.EmployeeListActivity
import com.zhmiaobang.easydianapp.module.miacochang.preseed.PreSeedListActivity
import com.zhmiaobang.easydianapp.viewmodel.user.UserViewModel

class HomeProfileFragment : Fragment() {

    companion object {
        private const val TAG = "HomeProfileFragment"
        private const val ROLE_MIAOCHANG_BOSS = 8888  // 苗场老板角色代码
        private const val ROLE_FARMER = 8888  // 农民/试水苗角色代码
        fun newInstance() = HomeProfileFragment()
    }

    private var _binding: FragmentHomeProfileBinding? = null
    private val binding get() = _binding!!

    private val userViewModel: UserViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeProfileBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupObservers()
        setupClickListeners()
    }

    override fun onResume() {
        super.onResume()
        // 在页面恢复时刷新用户信息
        refreshUserInfo()
    }

    /**
     * 刷新用户信息
     */
    private fun refreshUserInfo() {
        try {
            val user = com.zhmiaobang.easydianapp.init.ConfigTools.getUser()
            if (user != null) {
                Log.d(TAG, "手动刷新用户信息: ${user.nickname}")
                updateUserInfo(user)
            } else {
                Log.w(TAG, "刷新时用户信息为空")
                showDefaultUserInfo()
            }
        } catch (e: Exception) {
            Log.e(TAG, "刷新用户信息失败: ${e.message}", e)
            showDefaultUserInfo()
        }
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察用户信息变化
        userViewModel.userObserver.observe(viewLifecycleOwner) { user ->
            if (user != null) {
                Log.d(TAG, "收到用户信息更新: ${user.nickname}")
                updateUserInfo(user)
            } else {
                Log.w(TAG, "用户信息为空")
                showDefaultUserInfo()
            }
        }

        // 观察错误信息
        userViewModel.errorObserver.observe(viewLifecycleOwner) { errorMessage ->
            Log.e(TAG, "UserViewModel 错误: $errorMessage")
        }
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 编辑按钮点击事件（只有苗场老板能看到此按钮）
        binding.hprofileEditBtn.setOnClickListener {
            Log.d(TAG, "苗场老板点击编辑按钮，跳转到媒体编辑界面")
            navigateToMediaEditActivity()
        }

        // 投料设置点击事件（只有苗场老板能看到此功能）
        binding.hprofileFeedSettingsContainer.setOnClickListener {
            Log.d(TAG, "苗场老板点击投料设置，跳转到投料点列表界面")
            navigateToFeedPointListActivity()
        }

        // 饵料管理点击事件（只有苗场老板能看到此功能）
        binding.hprofileFeedPackageContainer.setOnClickListener {
            Log.d(TAG, "苗场老板点击饵料管理，跳转到饲料包列表界面")
            navigateToFeedPackageListActivity()
        }

        // 员工管理点击事件（只有苗场老板能看到此功能）
        binding.hprofileEmployeeManagementContainer.setOnClickListener {
            Log.d(TAG, "苗场老板点击员工管理，跳转到员工列表界面")
            navigateToEmployeeListActivity()
        }

        // 试水苗管理点击事件（只有农民角色能看到此功能）
        binding.hprofilePreseedManagementContainer.setOnClickListener {
            Log.d(TAG, "农民点击试水苗管理，跳转到预播种列表界面")
            navigateToPreSeedListActivity()
        }

        // 头像点击事件
        binding.hprofileAvatarCard.setOnClickListener {
            // TODO: 实现更换头像功能
            Log.d(TAG, "点击头像")
        }
    }

    /**
     * 跳转到媒体编辑界面
     */
    private fun navigateToMediaEditActivity() {
        try {
            val intent = Intent(requireContext(), MiaoChangMediaEditActivity::class.java)
            startActivity(intent)
            Log.d(TAG, "成功跳转到媒体编辑界面")
        } catch (e: Exception) {
            Log.e(TAG, "跳转到媒体编辑界面失败: ${e.message}", e)
            showToast("页面跳转失败")
        }
    }

    /**
     * 跳转到投料点列表界面
     */
    private fun navigateToFeedPointListActivity() {
        try {
            val intent = Intent(requireContext(), FeedPointListActivity::class.java)
            startActivity(intent)
            Log.d(TAG, "成功跳转到投料点列表界面")
        } catch (e: Exception) {
            Log.e(TAG, "跳转到投料点列表界面失败: ${e.message}", e)
            showToast("页面跳转失败")
        }
    }

    /**
     * 跳转到饲料包列表界面
     */
    private fun navigateToFeedPackageListActivity() {
        try {
            val intent = Intent(requireContext(), FeedPackageListActivity::class.java)
            startActivity(intent)
            Log.d(TAG, "成功跳转到饲料包列表界面")
        } catch (e: Exception) {
            Log.e(TAG, "跳转到饲料包列表界面失败: ${e.message}", e)
            showToast("页面跳转失败")
        }
    }

    /**
     * 跳转到员工列表界面
     */
    private fun navigateToEmployeeListActivity() {
        try {
            val intent = Intent(requireContext(), EmployeeListActivity::class.java)
            startActivity(intent)
            Log.d(TAG, "成功跳转到员工列表界面")
        } catch (e: Exception) {
            Log.e(TAG, "跳转到员工列表界面失败: ${e.message}", e)
            showToast("页面跳转失败")
        }
    }

    /**
     * 跳转到预播种列表界面
     */
    private fun navigateToPreSeedListActivity() {
        try {
            val intent = Intent(requireContext(), PreSeedListActivity::class.java)
            startActivity(intent)
            Log.d(TAG, "成功跳转到预播种列表界面")
        } catch (e: Exception) {
            Log.e(TAG, "跳转到预播种列表界面失败: ${e.message}", e)
            showToast("页面跳转失败")
        }
    }

    /**
     * 显示提示信息
     */
    private fun showToast(message: String) {
        android.widget.Toast.makeText(requireContext(), message, android.widget.Toast.LENGTH_SHORT).show()
    }

    /**
     * 更新用户信息显示
     */
    private fun updateUserInfo(user: UserJson) {
        try {
            // 更新头像
            loadUserAvatar(user.avatar)

            // 更新昵称
            val displayName = user.nickname?.takeIf { it.isNotBlank() } ?: "未知用户"
            binding.hprofileUserName.text = displayName
            Log.d(TAG, "更新用户昵称: $displayName")

            // 更新工号/ID
            binding.hprofileUserId.text = "工号: ${user.id}"

            // 更新角色信息
            val roleText = user.role.name
            binding.hprofileUserRole.text = roleText
            Log.d(TAG, "更新用户角色: $roleText")

            // 更新电话信息（如果布局中有电话显示的话）
            val phoneNumber = user.phone?.takeIf { it.isNotBlank() }
                ?: user.employeePhone?.takeIf { it.isNotBlank() }
                ?: "未设置"
            Log.d(TAG, "用户电话: $phoneNumber")

            // 根据用户角色显示/隐藏苗场老板专属功能
            updateBossOnlyFeaturesVisibility(user)

        } catch (e: Exception) {
            Log.e(TAG, "更新用户信息失败: ${e.message}", e)
            showDefaultUserInfo()
        }
    }

    /**
     * 根据用户角色更新角色专属功能的可见性
     */
    private fun updateBossOnlyFeaturesVisibility(user: UserJson) {
        val userRole = user.role
        val isBoss = userRole.code == ROLE_MIAOCHANG_BOSS
        val isFarmer = userRole.code == ROLE_FARMER

        // 控制编辑按钮可见性（只有苗场老板可见）
        binding.hprofileEditBtn.visibility = if (isBoss) View.VISIBLE else View.GONE

        // 控制饲料管理卡片可见性（只有苗场老板可见）
        binding.hprofileFeedManagementCard.visibility = if (isBoss) View.VISIBLE else View.GONE

        // 控制系统管理卡片可见性（苗场老板或农民可见）
        val showSystemManagement = isBoss || isFarmer
        binding.hprofileSystemManagementCard.visibility = if (showSystemManagement) View.VISIBLE else View.GONE

        // 控制员工管理按钮可见性（只有苗场老板可见）
        binding.hprofileEmployeeManagementContainer.visibility = if (isBoss) View.VISIBLE else View.GONE

        // 控制试水苗管理按钮可见性（只有农民可见）
        binding.hprofilePreseedManagementContainer.visibility = if (isFarmer) View.VISIBLE else View.GONE

        Log.d(TAG, "角色专属功能可见性更新: 角色=${userRole.name}(${userRole.code}), 苗场老板=${isBoss}, 农民=${isFarmer}")
    }

    /**
     * 加载用户头像
     */
    private fun loadUserAvatar(avatarUrl: String?) {
        try {
            if (avatarUrl.isNullOrBlank()) {
                Log.d(TAG, "用户头像URL为空，使用默认头像")
                // 直接设置默认头像
                binding.hprofileAvatar.setImageResource(R.drawable.ic_person)
            } else {
                Log.d(TAG, "加载用户头像: ${avatarUrl.take(50)}...")

                // 使用 Coil 3.x 加载图片
                binding.hprofileAvatar.load(avatarUrl) {
                    // 设置占位图片和错误图片 - 使用 Context 获取 Drawable
                    placeholder(requireContext().getDrawable(R.drawable.ic_person))

                    // 设置圆形裁剪
                    transformations(CircleCropTransformation())
                    // 设置淡入动画
                    crossfade(true)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "加载头像失败: ${e.message}", e)
            // 加载失败时设置默认头像
            binding.hprofileAvatar.setImageResource(R.drawable.ic_person)
        }
    }

    /**
     * 显示默认用户信息
     */
    private fun showDefaultUserInfo() {
        try {
            binding.hprofileUserName.text = "未登录用户"
            binding.hprofileUserId.text = "工号: --"
            binding.hprofileUserRole.text = "未知角色"

            // 设置默认头像
            binding.hprofileAvatar.setImageResource(R.drawable.ic_person)

            // 隐藏角色专属功能（未登录或信息异常时）
            binding.hprofileEditBtn.visibility = View.GONE
            binding.hprofileFeedManagementCard.visibility = View.GONE
            binding.hprofileSystemManagementCard.visibility = View.GONE
            binding.hprofileEmployeeManagementContainer.visibility = View.GONE
            binding.hprofilePreseedManagementContainer.visibility = View.GONE

            Log.d(TAG, "显示默认用户信息")
        } catch (e: Exception) {
            Log.e(TAG, "显示默认用户信息失败: ${e.message}", e)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}