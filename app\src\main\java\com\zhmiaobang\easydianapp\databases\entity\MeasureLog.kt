package com.zhmiaobang.easydianapp.databases.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.zhmiaobang.easydianapp.databases.converter.OnnxModelJsonConverter
import com.zhmiaobang.easydianapp.databases.converter.FloatArrayConverter
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson

/**
 * 测量日志实体类
 * 
 * 用于存储测量模块的操作记录，包括：
 * - 选中的ONNX模型信息
 * - 电话号码
 * - 源图片路径和处理后的目标图片路径
 * - 测量计数结果
 * - 创建时间戳
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Entity(
    tableName = "measure_log"
)
@TypeConverters(OnnxModelJsonConverter::class, FloatArrayConverter::class)
data class MeasureLog(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    val id: Long = 0,
    
    /**
     * 选中的ONNX模型信息
     * 使用TypeConverter将复杂对象转换为JSON字符串存储
     */
    @ColumnInfo(name = "onnx_model_json")
    val onnxModelJson: OnnxModelJson,
    
    /**
     * 电话号码 - 非空
     */
    @ColumnInfo(name = "phone")
    val phone: String,
    
    /**
     * 源图片路径 - 非空
     */
    @ColumnInfo(name = "img_src")
    val imgSrc: String,
    
    /**
     * 处理后的目标图片路径 - 可为空
     */
    @ColumnInfo(name = "dest_img")
    val destImg: String? = null,
    
    /**
     * 测量计数结果 - 可为空
     */
    @ColumnInfo(name = "count")
    val count: Int? = null,
    
    /**
     * 创建时间戳 - 可为空，默认为当前时间
     */
    @ColumnInfo(name = "create_at")
    val createAt: Long? = System.currentTimeMillis(),

    /**
     * 置信度阈值 - 用于存储检测时使用的置信度阈值
     */
    @ColumnInfo(name = "conf")
    val conf: Float? = null,

    /**
     * NMS阈值 - 用于存储检测时使用的非极大值抑制阈值
     */
    @ColumnInfo(name = "nms")
    val nms: Float? = null,

    /**
     * 检测框长宽尺寸数据 - 存储每个检测框的长宽尺寸
     * 数组格式：[width1, height1, width2, height2, ...]
     */
    @ColumnInfo(name = "dimension_lengths")
    val dimensionLengths: FloatArray? = null,

    /**
     * 检测框对角线长度数据 - 存储每个检测框的对角线长度
     * 数组格式：[diagonal1, diagonal2, diagonal3, ...]
     */
    @ColumnInfo(name = "diagonal_lengths")
    val diagonalLengths: FloatArray? = null
) {
    
    companion object {
        /**
         * 创建新的测量日志记录
         *
         * @param onnxModelJson 选中的ONNX模型
         * @param phone 电话号码
         * @param imgSrc 源图片路径
         * @param destImg 目标图片路径（可选）
         * @param count 计数结果（可选）
         * @param conf 置信度阈值（可选）
         * @param nms NMS阈值（可选）
         * @param dimensionLengths 检测框长宽尺寸数据（可选）
         * @param diagonalLengths 检测框对角线长度数据（可选）
         * @return 新的MeasureLog实例
         */
        fun create(
            onnxModelJson: OnnxModelJson,
            phone: String,
            imgSrc: String,
            destImg: String? = null,
            count: Int? = null,
            conf: Float? = null,
            nms: Float? = null,
            dimensionLengths: FloatArray? = null,
            diagonalLengths: FloatArray? = null
        ): MeasureLog {
            return MeasureLog(
                onnxModelJson = onnxModelJson,
                phone = phone,
                imgSrc = imgSrc,
                destImg = destImg,
                count = count,
                createAt = System.currentTimeMillis(),
                conf = conf,
                nms = nms,
                dimensionLengths = dimensionLengths,
                diagonalLengths = diagonalLengths
            )
        }
    }
    
    /**
     * 检查测量是否已完成
     * 当有计数结果和目标图片时认为测量完成
     */
    fun isCompleted(): Boolean {
        return count != null && !destImg.isNullOrEmpty()
    }
    
    /**
     * 获取模型名称
     */
    fun getModelName(): String {
        return onnxModelJson.name
    }
    
    /**
     * 获取模型分类名称
     */
    fun getCategoryName(): String {
        return onnxModelJson.shrimpCate.name
    }
    
    /**
     * 获取格式化的创建时间
     */
    fun getFormattedCreateTime(): String {
        return createAt?.let {
            val date = java.util.Date(it)
            val format = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            format.format(date)
        } ?: "未知时间"
    }

    /**
     * 获取格式化的置信度阈值
     */
    fun getFormattedConfidence(): String {
        return conf?.let { String.format("%.2f", it) } ?: "未记录"
    }

    /**
     * 获取格式化的NMS阈值
     */
    fun getFormattedNms(): String {
        return nms?.let { String.format("%.2f", it) } ?: "未记录"
    }

    /**
     * 获取检测参数信息
     */
    fun getDetectionParams(): String {
        return "置信度: ${getFormattedConfidence()}, NMS: ${getFormattedNms()}"
    }

    /**
     * 获取格式化的检测框尺寸信息（最大检测框）
     */
    fun getFormattedDimensions(): String {
        return dimensionLengths?.let { dimensions ->
            if (dimensions.size == 2) {
                "最大框: ${String.format("%.1f", dimensions[0])}×${String.format("%.1f", dimensions[1])}"
            } else {
                "尺寸数据格式错误"
            }
        } ?: "未记录"
    }

    /**
     * 获取格式化的对角线长度信息（最大检测框）
     */
    fun getFormattedDiagonals(): String {
        return diagonalLengths?.let { diagonals ->
            if (diagonals.size == 1) {
                "最大框对角线: ${String.format("%.1f", diagonals[0])}"
            } else {
                "对角线数据格式错误"
            }
        } ?: "未记录"
    }

    /**
     * 获取最大检测框的宽度
     */
    fun getLargestBoxWidth(): Float? {
        return dimensionLengths?.let { dimensions ->
            if (dimensions.size == 2) dimensions[0] else null
        }
    }

    /**
     * 获取最大检测框的高度
     */
    fun getLargestBoxHeight(): Float? {
        return dimensionLengths?.let { dimensions ->
            if (dimensions.size == 2) dimensions[1] else null
        }
    }

    /**
     * 获取最大检测框的面积
     */
    fun getLargestBoxArea(): Float? {
        return dimensionLengths?.let { dimensions ->
            if (dimensions.size == 2) dimensions[0] * dimensions[1] else null
        }
    }

    /**
     * 获取最大检测框的对角线长度
     */
    fun getLargestBoxDiagonal(): Float? {
        return diagonalLengths?.let { diagonals ->
            if (diagonals.size == 1) diagonals[0] else null
        }
    }

    /**
     * 获取最大检测框的完整信息
     */
    fun getLargestBoxInfo(): String {
        val width = getLargestBoxWidth()
        val height = getLargestBoxHeight()
        val area = getLargestBoxArea()
        val diagonal = getLargestBoxDiagonal()

        return if (width != null && height != null && area != null && diagonal != null) {
            "最大框: ${String.format("%.1f", width)}×${String.format("%.1f", height)}, " +
            "面积: ${String.format("%.1f", area)}, " +
            "对角线: ${String.format("%.1f", diagonal)}"
        } else {
            "无最大框数据"
        }
    }
}
