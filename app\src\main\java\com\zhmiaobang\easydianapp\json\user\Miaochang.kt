package com.zhmiaobang.easydianapp.json.user

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Miaochang(
    val city: City?=null,
    val createTime: String?=null,
    val description: String?=null,
    val id: Int,
    val maxAgent: Int,
    val maxBatch: Int,
    val maxCountSample: Int,
    val maxEmployee: Int,
    val maxFeedPackage: Int,
    val maxFeedPoint: Int,
    val maxMeasueSample: Int,
    val maxPreSeed: Int,
    val media: MiaoChangMedia?=null,
    val name: String,
    val no: String,
    val preSeedPublicEndTime: String?=null,
    val preSeedPublicStartTime: String?=null,
    val preSeedPublicStatus: Boolean,
    val status: Int,
    val updateTime: String?=null
): Parcelable