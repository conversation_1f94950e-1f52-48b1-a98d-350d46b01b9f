package com.zhmiaobang.easydianapp.onnx

import android.content.Context
import android.util.Log
import com.zhmiaobang.easydianapp.json.onnx.OnnxModelJson
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import retrofit2.Response
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

/**
 * ONNX 模型管理器
 * 
 * 负责管理 ONNX 模型文件的下载、存储和验证
 * 
 * <AUTHOR> 4.0 sonnet
 */
class OnnxModelManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "OnnxModelManager"
        private const val ONNX_DIR = "onnx"
        
        @Volatile
        private var INSTANCE: OnnxModelManager? = null
        
        fun getInstance(context: Context): OnnxModelManager {
            return INSTANCE ?: synchronized(this) {
                val instance = OnnxModelManager(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    private val onnxDir: File by lazy {
        File(context.filesDir, ONNX_DIR).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    /**
     * 获取模型文件的本地路径
     */
    fun getModelFilePath(onnxModel: OnnxModelJson): String {
        return File(onnxDir, onnxModel.localFilename).absolutePath
    }
    
    /**
     * 检查模型文件是否存在
     */
    fun isModelFileExists(onnxModel: OnnxModelJson): Boolean {
        val modelFile = File(onnxDir, onnxModel.localFilename)
        val exists = modelFile.exists() && modelFile.length() > 0
        Log.d(TAG, "检查模型文件: ${onnxModel.localFilename}, 存在: $exists")
        return exists
    }
    
    /**
     * 下载模型文件
     */
    suspend fun downloadModelFile(
        onnxModel: OnnxModelJson,
        onProgress: (Int) -> Unit = {}
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始下载模型文件: ${onnxModel.name}")
            
            // 首先尝试主URL
            var downloadUrl = onnxModel.primaryUrl
            var response = downloadFile(downloadUrl)
            
            // 如果主URL失败，尝试备用URL
            if (!response.isSuccessful && !onnxModel.secondUrl.isNullOrEmpty()) {
                Log.w(TAG, "主URL下载失败，尝试备用URL")
                downloadUrl = onnxModel.secondUrl!!
                response = downloadFile(downloadUrl)
            }
            
            if (!response.isSuccessful) {
                throw Exception("下载失败: HTTP ${response.code()}")
            }
            
            val responseBody = response.body()
                ?: throw Exception("响应体为空")
            
            val modelFile = File(onnxDir, onnxModel.localFilename)
            val totalBytes = responseBody.contentLength()
            
            Log.d(TAG, "开始写入文件: ${modelFile.absolutePath}, 大小: ${totalBytes}字节")
            
            responseBody.byteStream().use { inputStream ->
                FileOutputStream(modelFile).use { outputStream ->
                    val buffer = ByteArray(8192)
                    var downloadedBytes = 0L
                    var bytesRead: Int
                    
                    while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                        outputStream.write(buffer, 0, bytesRead)
                        downloadedBytes += bytesRead
                        
                        if (totalBytes > 0) {
                            val progress = ((downloadedBytes * 100) / totalBytes).toInt()
                            onProgress(progress)
                        }
                    }
                }
            }
            
            Log.d(TAG, "模型文件下载完成: ${modelFile.absolutePath}")
            Result.success(modelFile.absolutePath)
            
        } catch (e: Exception) {
            Log.e(TAG, "下载模型文件失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 下载文件的网络请求
     */
    private suspend fun downloadFile(url: String): Response<ResponseBody> {
        return try {
            RetrofitClient.apiService.downloadFile(url)
        } catch (e: Exception) {
            Log.e(TAG, "网络请求失败: ${e.message}", e)
            throw e
        }
    }
    
    /**
     * 验证模型文件完整性
     */
    fun validateModelFile(onnxModel: OnnxModelJson): Boolean {
        val modelFile = File(onnxDir, onnxModel.localFilename)
        
        if (!modelFile.exists()) {
            Log.w(TAG, "模型文件不存在: ${modelFile.absolutePath}")
            return false
        }
        
        if (modelFile.length() == 0L) {
            Log.w(TAG, "模型文件为空: ${modelFile.absolutePath}")
            return false
        }
        
        // 可以添加更多验证逻辑，如文件头检查、校验和验证等
        Log.d(TAG, "模型文件验证通过: ${modelFile.absolutePath}")
        return true
    }
    
    /**
     * 删除模型文件
     */
    fun deleteModelFile(onnxModel: OnnxModelJson): Boolean {
        val modelFile = File(onnxDir, onnxModel.localFilename)
        return if (modelFile.exists()) {
            val deleted = modelFile.delete()
            Log.d(TAG, "删除模型文件: ${modelFile.absolutePath}, 成功: $deleted")
            deleted
        } else {
            Log.d(TAG, "模型文件不存在，无需删除: ${modelFile.absolutePath}")
            true
        }
    }
    
    /**
     * 获取模型目录大小
     */
    fun getModelDirectorySize(): Long {
        return onnxDir.walkTopDown()
            .filter { it.isFile }
            .map { it.length() }
            .sum()
    }
    
    /**
     * 清理所有模型文件
     */
    fun clearAllModels(): Boolean {
        return try {
            onnxDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    file.delete()
                }
            }
            Log.d(TAG, "清理所有模型文件完成")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清理模型文件失败: ${e.message}", e)
            false
        }
    }
}
