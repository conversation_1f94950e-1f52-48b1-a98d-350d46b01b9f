<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/hIndex_background"
    tools:context=".module.home.HomeProfileFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- User Profile Header -->
        <androidx.cardview.widget.CardView
            android:id="@+id/hprofile_user_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/hIndex_card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp">

                <!-- User Avatar -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/hprofile_avatar_card"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_marginEnd="16dp"
                    app:cardCornerRadius="32dp"
                    app:cardElevation="0dp">

                    <ImageView
                        android:id="@+id/hprofile_avatar"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/hIndex_blue"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_person" />

                </androidx.cardview.widget.CardView>

                <!-- User Info -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/hprofile_user_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="张师傅"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/hprofile_user_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="工号: EMP001"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/hprofile_user_role"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="A区管理员"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="14sp" />

                </LinearLayout>

                <!-- Edit Button -->
                <ImageView
                    android:id="@+id/hprofile_edit_btn"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="top"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true"
                    android:src="@drawable/ic_edit" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Work Statistics Section -->
        <androidx.cardview.widget.CardView
            android:id="@+id/hprofile_stats_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/hIndex_card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"/>

        <!-- Monthly Work Statistics Section -->

        <!-- Feed Management Section (仅苗场老板可见) -->
        <androidx.cardview.widget.CardView
            android:id="@+id/hprofile_feed_management_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:cardBackgroundColor="@color/hIndex_card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Section Title -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="饲料管理"
                    android:textColor="@color/hIndex_text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Feed Management Buttons Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- Feed Point Settings Button -->
                    <LinearLayout
                        android:id="@+id/hprofile_feed_settings_container"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/rounded_button_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <ImageView
                            android:id="@+id/hprofile_feed_settings_icon"
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:layout_marginBottom="6dp"
                            android:src="@drawable/ic_feeding"
                            app:tint="@color/hIndex_orange" />

                        <TextView
                            android:id="@+id/hprofile_feed_settings_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="投料设置"
                            android:textColor="@color/hIndex_text_primary"
                            android:textSize="13sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- Feed Package Management Button -->
                    <LinearLayout
                        android:id="@+id/hprofile_feed_package_container"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/rounded_button_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <ImageView
                            android:id="@+id/hprofile_feed_package_icon"
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:layout_marginBottom="6dp"
                            android:src="@drawable/ic_feeding"
                            app:tint="@color/hIndex_green" />

                        <TextView
                            android:id="@+id/hprofile_feed_package_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="饵料管理"
                            android:textColor="@color/hIndex_text_primary"
                            android:textSize="13sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- System Management Section (仅苗场老板可见) -->
        <androidx.cardview.widget.CardView
            android:id="@+id/hprofile_system_management_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:cardBackgroundColor="@color/hIndex_card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Section Title -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="系统管理"
                    android:textColor="@color/hIndex_text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- System Management Buttons Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- Employee Management Button -->
                    <LinearLayout
                        android:id="@+id/hprofile_employee_management_container"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/rounded_button_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <ImageView
                            android:id="@+id/hprofile_employee_management_icon"
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:layout_marginBottom="6dp"
                            android:src="@drawable/ic_person"
                            app:tint="@color/hIndex_blue" />

                        <TextView
                            android:id="@+id/hprofile_employee_management_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="员工管理"
                            android:textColor="@color/hIndex_text_primary"
                            android:textSize="13sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- PreSeed Management Button -->
                    <LinearLayout
                        android:id="@+id/hprofile_preseed_management_container"
                        android:layout_width="0dp"
                        android:layout_height="80dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/rounded_button_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="12dp"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/hprofile_preseed_management_icon"
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:layout_marginBottom="6dp"
                            android:src="@drawable/ic_qr_code_24"
                            app:tint="@color/hIndex_purple" />

                        <TextView
                            android:id="@+id/hprofile_preseed_management_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="试水苗管理"
                            android:textColor="@color/hIndex_text_primary"
                            android:textSize="13sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- System Settings Section -->
        <androidx.cardview.widget.CardView
            android:id="@+id/hprofile_settings_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/hIndex_card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- Notification Settings -->

                <!-- Theme Mode -->

                <!-- Language Settings -->
                <LinearLayout
                    android:id="@+id/hprofile_language_setting"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="4dp">

                    <ImageView
                        android:id="@+id/hprofile_language_icon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_language"
                        app:tint="@color/hIndex_text_secondary" />

                    <TextView
                        android:id="@+id/hprofile_language_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="语言设置"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/hprofile_language_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="简体中文"
                        android:textColor="@color/hIndex_text_secondary"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/hprofile_language_arrow"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/hIndex_text_hint" />

                </LinearLayout>

                <!-- Help Documentation -->
                <LinearLayout
                    android:id="@+id/hprofile_help_setting"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="4dp">

                    <ImageView
                        android:id="@+id/hprofile_help_icon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_help"
                        app:tint="@color/hIndex_text_secondary" />

                    <TextView
                        android:id="@+id/hprofile_help_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="帮助文档"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:id="@+id/hprofile_help_arrow"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/hIndex_text_hint" />

                </LinearLayout>

                <!-- About Application -->
                <LinearLayout
                    android:id="@+id/hprofile_about_setting"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="4dp">

                    <ImageView
                        android:id="@+id/hprofile_about_icon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_info"
                        app:tint="@color/hIndex_text_secondary" />

                    <TextView
                        android:id="@+id/hprofile_about_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="关于应用"
                        android:textColor="@color/hIndex_text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:id="@+id/hprofile_about_arrow"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/hIndex_text_hint" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Logout Button -->
        <androidx.cardview.widget.CardView
            android:id="@+id/hprofile_logout_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            app:cardBackgroundColor="@color/hIndex_card_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:id="@+id/hprofile_logout_btn"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:background="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/hprofile_logout_icon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_logout"
                    app:tint="@color/hIndex_trend_down" />

                <TextView
                    android:id="@+id/hprofile_logout_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="退出登录"
                    android:textColor="@color/hIndex_trend_down"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>