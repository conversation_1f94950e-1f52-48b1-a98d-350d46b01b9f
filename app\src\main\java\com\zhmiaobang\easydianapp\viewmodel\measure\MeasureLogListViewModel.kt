package com.zhmiaobang.easydianapp.viewmodel.measure

import android.content.Context
import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.zhmiaobang.easydianapp.databases.entity.MeasureLog
import com.zhmiaobang.easydianapp.databases.repository.MeasureLogRepository
import com.zhmiaobang.easydianapp.utils.PaginatedData
import com.zhmiaobang.easydianapp.utils.PaginationState
import com.zhmiaobang.easydianapp.viewmodel.BaseViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 测量日志列表ViewModel - 支持分页功能
 * 
 * 功能特性：
 * - 本地数据库分页查询
 * - 数据状态管理
 * - 完整的错误处理和加载状态
 * - 支持下拉刷新和上拉加载更多
 * 
 * <AUTHOR> 4.0 sonnet
 */
class MeasureLogListViewModel(context: Context) : BaseViewModel() {

    companion object {
        private const val TAG = "MeasureLogListViewModel"
        private const val PAGE_SIZE = 20 // 每页加载数量
    }

    // 数据仓库
    private val measureLogRepository = MeasureLogRepository.getInstance(context)

    // 分页数据观察者
    val paginatedDataObserver: MutableLiveData<PaginatedData<MeasureLog>> by lazy {
        MutableLiveData<PaginatedData<MeasureLog>>()
    }

    // 分页状态管理
    private var paginationState = PaginationState()

    // 所有已加载的测量日志数据（去重）
    private val allMeasureLogs = LinkedHashSet<MeasureLog>()

    /**
     * 加载第一页数据
     */
    fun loadFirstPage() = launch({
        if (paginationState.isLoading) {
            Log.d(TAG, "正在加载中，忽略重复请求")
            return@launch
        }

        Log.d(TAG, "开始加载第一页数据...")
        paginationState = paginationState.startFirstLoading()
        allMeasureLogs.clear()

        try {
            val measureLogs = measureLogRepository.getPagedMeasureLogs(page = 1, pageSize = PAGE_SIZE)
            val totalCount = measureLogRepository.getTotalCount()
            
            Log.d(TAG, "第一页加载成功: totalCount=$totalCount, results数量=${measureLogs.size}")

            // 添加数据到集合（自动去重）
            allMeasureLogs.addAll(measureLogs)

            // 更新分页状态
            val hasNext = measureLogs.size >= PAGE_SIZE && allMeasureLogs.size < totalCount
            paginationState = paginationState.loadSuccess(hasNext, totalCount)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allMeasureLogs.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "第一页加载失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "加载失败")

            val paginatedData = PaginatedData(
                items = allMeasureLogs.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        }
    })

    /**
     * 加载下一页数据
     */
    fun loadNextPage() = launch({
        if (!paginationState.canLoadMore) {
            Log.d(TAG, "无法加载更多: isLoading=${paginationState.isLoading}, hasMore=${paginationState.hasMoreData}")
            return@launch
        }

        Log.d(TAG, "开始加载第${paginationState.currentPage}页数据...")
        paginationState = paginationState.startLoadingMore()

        // 先发送加载状态
        val loadingData = PaginatedData(
            items = allMeasureLogs.toList(),
            paginationState = paginationState
        )
        paginatedDataObserver.postValue(loadingData)

        try {
            val measureLogs = measureLogRepository.getPagedMeasureLogs(
                page = paginationState.currentPage, 
                pageSize = PAGE_SIZE
            )
            val totalCount = measureLogRepository.getTotalCount()
            
            Log.d(TAG, "第${paginationState.currentPage}页加载成功: 新增${measureLogs.size}条记录")

            // 添加新数据到集合（自动去重）
            allMeasureLogs.addAll(measureLogs)

            // 更新分页状态
            val hasNext = measureLogs.size >= PAGE_SIZE && allMeasureLogs.size < totalCount
            paginationState = paginationState.loadSuccess(hasNext, totalCount)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allMeasureLogs.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "第${paginationState.currentPage}页加载失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "加载失败")

            val paginatedData = PaginatedData(
                items = allMeasureLogs.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        }
    })

    /**
     * 刷新数据（重新加载第一页）
     */
    fun refresh() = launch({
        Log.d(TAG, "开始刷新数据...")
        paginationState = paginationState.reset()
        loadFirstPage()
    })

    /**
     * 获取当前分页状态
     */
    fun getCurrentPaginationState(): PaginationState {
        return paginationState
    }

    /**
     * 删除测量日志记录
     * 
     * @param measureLog 要删除的记录
     */
    fun deleteMeasureLog(measureLog: MeasureLog) = launch({
        try {
            Log.d(TAG, "开始删除测量日志: id=${measureLog.id}")
            
            measureLogRepository.deleteMeasureLog(measureLog)
            
            // 从本地集合中移除
            allMeasureLogs.remove(measureLog)
            
            // 更新总数
            val newTotalCount = paginationState.totalCount - 1
            paginationState = paginationState.copy(totalCount = newTotalCount)
            
            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allMeasureLogs.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            
            Log.d(TAG, "测量日志删除成功: id=${measureLog.id}")
            
        } catch (e: Exception) {
            Log.e(TAG, "删除测量日志失败: ${e.message}", e)
            throw e
        }
    })

    /**
     * 根据电话号码筛选数据
     * 
     * @param phone 电话号码，为空则显示所有数据
     */
    fun filterByPhone(phone: String?) = launch({
        Log.d(TAG, "开始按电话号码筛选: $phone")
        
        paginationState = paginationState.reset().startFirstLoading()
        allMeasureLogs.clear()

        try {
            val measureLogs = if (phone.isNullOrBlank()) {
                measureLogRepository.getPagedMeasureLogs(page = 1, pageSize = PAGE_SIZE)
            } else {
                measureLogRepository.getPagedMeasureLogsByPhone(phone, page = 1, pageSize = PAGE_SIZE)
            }
            
            val totalCount = if (phone.isNullOrBlank()) {
                measureLogRepository.getTotalCount()
            } else {
                measureLogRepository.getTotalCountByPhone(phone)
            }
            
            Log.d(TAG, "筛选结果: totalCount=$totalCount, results数量=${measureLogs.size}")

            // 添加数据到集合
            allMeasureLogs.addAll(measureLogs)

            // 更新分页状态
            val hasNext = measureLogs.size >= PAGE_SIZE && allMeasureLogs.size < totalCount
            paginationState = paginationState.loadSuccess(hasNext, totalCount)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allMeasureLogs.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "筛选失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "筛选失败")

            val paginatedData = PaginatedData(
                items = allMeasureLogs.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        }
    })
}
