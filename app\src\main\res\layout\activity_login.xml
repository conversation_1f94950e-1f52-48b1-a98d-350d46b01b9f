<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".module.login.LoginActivity">

    <!-- 顶部标题栏 -->
    <TextView
        android:id="@+id/login_title_bar"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:background="@color/login_primary_blue"
        android:gravity="center"
        android:text="苗邦登录"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Logo图标 -->
    <ImageView
        android:id="@+id/login_logo_image"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="48dp"
        android:background="@drawable/login_logo_background"
        android:contentDescription="应用Logo"
        android:padding="20dp"
        android:src="@drawable/ic_fish"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login_title_bar"
        app:tint="@color/login_primary_blue" />

    <!-- 应用名称 -->
    <TextView
        android:id="@+id/login_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="苗邦"
        android:textColor="@color/login_text_primary"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login_logo_image" />

    <!-- 应用副标题 -->
    <TextView
        android:id="@+id/login_app_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="虾苗养殖管理系统"
        android:textColor="@color/login_text_secondary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/login_app_name" />

    <!-- 二维码扫描区域 -->
    <FrameLayout
        android:id="@+id/login_qr_container"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:layout_marginTop="32dp"
        android:background="@drawable/login_qr_border"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_app_subtitle">

        <!-- 二维码图标 -->
        <ImageView
            android:id="@+id/login_qr_icon"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_gravity="center"
            android:layout_marginBottom="16dp"
            android:backgroundTint="@color/black"
            android:contentDescription="二维码图标"
            android:src="@drawable/ic_qr_code"
            app:tint="@color/black" />

        <!-- 提示文字 -->
        <TextView
            android:id="@+id/login_qr_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="64dp"
            android:text="请扫描员工二维码"
            android:textColor="@color/login_text_secondary"
            android:textSize="14sp" />

    </FrameLayout>

    <!-- 启动扫码登录按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/login_scan_button"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="48dp"
        android:backgroundTint="@color/login_primary_blue"
        android:text="📷 启动扫码登录"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:cornerRadius="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/login_qr_container" />

</androidx.constraintlayout.widget.ConstraintLayout>