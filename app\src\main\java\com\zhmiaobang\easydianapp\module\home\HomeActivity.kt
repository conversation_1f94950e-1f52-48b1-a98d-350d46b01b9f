package com.zhmiaobang.easydianapp.module.home

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.NavigationUI
import androidx.navigation.ui.setupActionBarWithNavController
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.huawei.hms.hmsscankit.ScanUtil
import com.huawei.hms.ml.scan.HmsScan
import com.huawei.hms.ml.scan.HmsScanAnalyzerOptions

import com.zhmiaobang.easydianapp.R
import com.zhmiaobang.easydianapp.init.MyApp
import com.zhmiaobang.easydianapp.module.miacochang.preseed.GetPreSeedQrCodeActivity
import com.zhmiaobang.easydianapp.viewmodel.user.UserViewModel

/**
 * 主页Activity - 集成华为ScanKit扫码功能
 * 优化版本 by Claude 4.0 sonnet
 */
class HomeActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "HomeActivity"

        // 二维码格式常量
        private const val QR_PREFIX = "mb|login|"
        private const val QR_SUFFIX = "|end"
    }

    private val userViewModel: UserViewModel by viewModels()

    // 权限请求启动器
    private val requestPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            val isGranted = permissions.entries.all { it.value }
            if (isGranted) {
                onPermissionGranted()
            } else {
                onPermissionDenied()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_home)

        // 设置沉浸式状态栏和系统栏内边距
        setupSystemBars()

        // 设置 Toolbar
        setupToolbar()

        // 设置 Navigation
        setupNavigation()
    }

    private fun setupSystemBars() {
        // 设置沉浸式状态栏
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.home_main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())

            // 为 Toolbar 设置顶部内边距和调整高度
            val toolbar = findViewById<MaterialToolbar>(R.id.tbarHome)

            // 获取原始 actionBarSize
            val typedValue = android.util.TypedValue()
            theme.resolveAttribute(android.R.attr.actionBarSize, typedValue, true)
            val actionBarSize = resources.getDimensionPixelSize(typedValue.resourceId)

            // 设置 Toolbar 新高度 = actionBarSize + 状态栏高度
            val layoutParams = toolbar.layoutParams
            layoutParams.height = actionBarSize + systemBars.top
            toolbar.layoutParams = layoutParams

            // 设置内边距，让标题在正确位置显示
            toolbar.setPadding(
                toolbar.paddingLeft,
                systemBars.top,
                toolbar.paddingRight,
                toolbar.paddingBottom
            )

            // 为 BottomNavigationView 设置底部内边距（导航栏高度）
            val bottomNav = findViewById<BottomNavigationView>(R.id.home_bottom_navigation)
            bottomNav.setPadding(
                bottomNav.paddingLeft,
                bottomNav.paddingTop,
                bottomNav.paddingRight,
                systemBars.bottom
            )

            // 主容器不设置内边距，让子视图自己处理
            v.setPadding(0, 0, 0, 0)
            insets
        }
    }

    private fun setupToolbar() {
        // 获取 MaterialToolbar
        val toolbar = findViewById<MaterialToolbar>(R.id.tbarHome)

        // 设置为 ActionBar
        setSupportActionBar(toolbar)

        // 隐藏返回键（主页面不需要返回键）
        supportActionBar?.setDisplayHomeAsUpEnabled(false)
        supportActionBar?.setDisplayShowHomeEnabled(false)
    }

    private fun setupNavigation() {
        // 获取 NavHostFragment
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.home_nav_host_fragment) as NavHostFragment
        val navController = navHostFragment.navController

        // 获取 BottomNavigationView
        val bottomNavigationView = findViewById<BottomNavigationView>(R.id.home_bottom_navigation)

        // 连接 BottomNavigationView 和 NavController
        NavigationUI.setupWithNavController(bottomNavigationView, navController)

        // 监听导航变化，手动更新标题（居中显示）
        navController.addOnDestinationChangedListener { _, destination, _ ->
            supportActionBar?.title = destination.label
        }
    }

    // ==================== 菜单处理 ====================

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_home_toolbar, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_scan -> {
                startQRCodeScan()
                true
            }

            R.id.mnu_home_preseed->{
                val intent = Intent(this@HomeActivity, GetPreSeedQrCodeActivity::class.java)
                startActivity(intent)
                true
            }

            else -> super.onOptionsItemSelected(item)
        }
    }

    // ==================== 扫描功能 ====================

    /**
     * 开始二维码扫描
     */
    private fun startQRCodeScan() {
        Log.d(TAG, "开始二维码扫描")

        // 检查相机权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
            == PackageManager.PERMISSION_GRANTED
        ) {
            launchScanActivity()
        } else {
            // 请求相机权限
            requestPermissionLauncher.launch(MyApp.permissionScan)
        }
    }

    /**
     * 启动华为ScanKit扫码界面
     */
    private fun launchScanActivity() {
        try {
            Log.d(TAG, "启动华为ScanKit扫码界面")

            // 配置扫码选项 - 只扫描二维码
            val options = HmsScanAnalyzerOptions.Creator()
                .setHmsScanTypes(HmsScan.QRCODE_SCAN_TYPE)
                .setViewType(1) // 使用默认扫码界面
                .create()

            // 启动华为ScanKit扫码
            ScanUtil.startScan(this, MyApp.REQUEST_CODE_SCAN_ONE, options)

        } catch (e: Exception) {
            Log.e(TAG, "启动扫码失败: ${e.message}", e)
            showToast("启动扫码失败，请重试")
        }
    }

    /**
     * 处理Activity结果 - 华为ScanKit回调
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == MyApp.REQUEST_CODE_SCAN_ONE) {
            handleScanResult(resultCode, data)
        }
    }

    /**
     * 处理扫码结果
     */
    private fun handleScanResult(resultCode: Int, data: Intent?) {
        when (resultCode) {
            RESULT_OK -> {
                // 扫码成功
                val hmsScan = data?.getParcelableExtra<HmsScan>(ScanUtil.RESULT)
                if (hmsScan != null && !hmsScan.originalValue.isNullOrEmpty()) {
                    processScanSuccess(hmsScan.originalValue)
                } else {
                    showToast("扫码结果为空，请重试")
                }
            }

            RESULT_CANCELED -> {
                // 用户取消扫码
                showToast("已取消扫码")
                Log.d(TAG, "用户取消扫码")
            }

            else -> {
                // 扫码失败
                showToast("扫码失败，请重试")
                Log.w(TAG, "扫码失败，结果码: $resultCode")
            }
        }
    }

    /**
     * 处理扫码成功
     */
    private fun processScanSuccess(scanResult: String) {
        try {
            Log.d(TAG, "扫码成功: $scanResult")

            // 验证二维码格式
            if (validateQRCode(scanResult)) {
                // 直接显示扫描结果（不解密）
                showScanResult(scanResult)
            } else {
                showToast("无效的二维码格式，请重新扫描")
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理扫码结果失败: ${e.message}", e)
            showToast("处理扫码结果失败，请重试")
        }
    }

    /**
     * 验证二维码格式
     * 格式：mb|login|{加密内容}|end
     */
    private fun validateQRCode(qrContent: String): Boolean {
        try {
            Log.d(TAG, "开始验证二维码: $qrContent")

            // 验证前缀和后缀
            if (!qrContent.startsWith(QR_PREFIX) || !qrContent.endsWith(QR_SUFFIX)) {
                Log.w(TAG, "二维码格式错误：前缀或后缀不匹配")
                return false
            }

            // 提取加密内容
            val encryptedContent = qrContent.substring(
                QR_PREFIX.length,
                qrContent.length - QR_SUFFIX.length
            )

            if (encryptedContent.isBlank()) {
                Log.w(TAG, "二维码加密内容为空")
                return false
            }

            Log.d(TAG, "二维码格式验证通过")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "验证二维码格式失败: ${e.message}", e)
            return false
        }
    }

    /**
     * 显示扫描结果
     */
    private fun showScanResult(scanResult: String) {
        // 创建一个简单的对话框显示扫描结果
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("扫描结果")
            .setMessage("扫描到的二维码内容：\n\n$scanResult")
            .setPositiveButton("确定") { dialog, _ ->
                dialog.dismiss()
            }
            .setNeutralButton("复制") { _, _ ->
                // 复制到剪贴板
                val clipboard =
                    getSystemService(CLIPBOARD_SERVICE) as android.content.ClipboardManager
                val clip = android.content.ClipData.newPlainText("扫描结果", scanResult)
                clipboard.setPrimaryClip(clip)
                showToast("已复制到剪贴板")
            }
            .show()
    }

    // ==================== 权限处理回调 ====================

    /**
     * 权限授予回调
     */
    private fun onPermissionGranted() {
        Log.d(TAG, "相机权限已授予")
        launchScanActivity()
    }

    /**
     * 权限拒绝回调
     */
    private fun onPermissionDenied() {
        Log.w(TAG, "相机权限被拒绝")
        showToast("需要相机权限才能使用扫码功能")
    }

    // ==================== 工具方法 ====================

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
}