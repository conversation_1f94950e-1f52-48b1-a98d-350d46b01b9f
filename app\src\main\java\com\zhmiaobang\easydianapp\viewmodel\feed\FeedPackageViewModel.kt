package com.zhmiaobang.easydianapp.viewmodel.feed

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.zhmiaobang.easydianapp.json.CommonResponseJson
import com.zhmiaobang.easydianapp.json.CommonRestfulJson
import com.zhmiaobang.easydianapp.json.feed.FeedPackageJson
import com.zhmiaobang.easydianapp.libs.network.ExceptionUtil
import com.zhmiaobang.easydianapp.libs.network.RetrofitClient
import com.zhmiaobang.easydianapp.utils.LoadingState
import com.zhmiaobang.easydianapp.utils.PaginatedData
import com.zhmiaobang.easydianapp.utils.PaginationState
import com.zhmiaobang.easydianapp.viewmodel.BaseViewModel

/**
 * 饲料包相关的ViewModel
 *
 * 功能特性：
 * - 获取饲料包列表（支持分页）
 * - 编辑饲料包信息
 * - 数据状态管理
 * - 完整的错误处理和加载状态
 *
 * <AUTHOR> 4.0 sonnet
 */
class FeedPackageViewModel: BaseViewModel() {

    companion object {
        private const val TAG = "FeedPackageViewModel"
    }

    // 分页数据观察者
    val paginatedDataObserver: MutableLiveData<PaginatedData<FeedPackageJson>> by lazy {
        MutableLiveData<PaginatedData<FeedPackageJson>>()
    }

    // 饲料包编辑观察者
    val packageEditObserver: MutableLiveData<CommonResponseJson<FeedPackageJson>> by lazy {
        MutableLiveData<CommonResponseJson<FeedPackageJson>>()
    }

    // 分页状态
    private var paginationState = PaginationState()

    // 所有已加载的饲料包数据
    private val allFeedPackages = LinkedHashSet<FeedPackageJson>()

    /**
     * 加载第一页数据（正常加载，使用缓存）
     */
    fun loadFirstPage() = launch({
        if (paginationState.isLoading) {
            Log.d(TAG, "正在加载中，忽略重复请求")
            return@launch
        }

        Log.d(TAG, "开始加载第一页数据（使用缓存）...")
        paginationState = paginationState.startFirstLoading()
        allFeedPackages.clear()

        try {
            // 正常加载使用缓存
            RetrofitClient.setUseCache(true)

            val response = RetrofitClient.apiService.get_feed_packahe_list(page = 1)
            Log.d(TAG, "第一页加载成功: count=${response.count}, results数量=${response.results.size}")

            // 添加数据到集合（自动去重）
            allFeedPackages.addAll(response.results)

            // 更新分页状态
            val hasNext = !response.next.isNullOrEmpty()
            paginationState = paginationState.loadSuccess(hasNext, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allFeedPackages.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "第一页加载失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "加载失败")

            val paginatedData = PaginatedData(
                items = allFeedPackages.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        }
    })

    /**
     * 加载下一页数据（使用缓存）
     */
    fun loadNextPage() = launch({
        if (!paginationState.canLoadMore) {
            Log.d(TAG, "无法加载更多: isLoading=${paginationState.isLoading}, hasMore=${paginationState.hasMoreData}")
            return@launch
        }

        Log.d(TAG, "开始加载第${paginationState.currentPage}页数据（使用缓存）...")
        paginationState = paginationState.startLoadingMore()

        // 先发送加载状态
        val loadingData = PaginatedData(
            items = allFeedPackages.toList(),
            paginationState = paginationState
        )
        paginatedDataObserver.postValue(loadingData)

        try {
            // 分页加载使用缓存
            RetrofitClient.setUseCache(true)

            val response = RetrofitClient.apiService.get_feed_packahe_list(page = paginationState.currentPage)
            Log.d(TAG, "第${paginationState.currentPage}页加载成功: results数量=${response.results.size}")

            // 添加新数据到集合
            val newItemsCount = response.results.size
            allFeedPackages.addAll(response.results)
            val actualNewItemsCount = allFeedPackages.size - (allFeedPackages.size - newItemsCount)

            if (actualNewItemsCount < newItemsCount) {
                Log.w(TAG, "检测到重复数据，原始${newItemsCount}条，实际新增${actualNewItemsCount}条")
            }

            // 更新分页状态
            val hasNext = !response.next.isNullOrEmpty()
            paginationState = paginationState.loadSuccess(hasNext, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allFeedPackages.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "第${paginationState.currentPage}页加载失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "加载失败")

            val paginatedData = PaginatedData(
                items = allFeedPackages.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        }
    })

    /**
     * 重试加载（禁用缓存，获取最新数据）
     */
    fun retryLoad() {
        Log.d(TAG, "重试加载（禁用缓存）")

        try {
            // 重试时禁用缓存，确保获取最新数据
            RetrofitClient.setUseCache(false)

            if (allFeedPackages.isEmpty()) {
                // 如果没有数据，重新加载第一页
                loadFirstPage()
            } else {
                // 如果有数据，重试加载下一页
                loadNextPage()
            }
        } finally {
            // 恢复缓存设置
            RetrofitClient.setUseCache(true)
        }
    }

    /**
     * 刷新数据（禁用缓存，获取最新数据）
     */
    fun refresh() = launch({
        if (paginationState.isLoading) {
            Log.d(TAG, "正在加载中，忽略刷新请求")
            return@launch
        }

        Log.d(TAG, "刷新数据（禁用缓存）...")
        paginationState = paginationState.reset().startFirstLoading()
        allFeedPackages.clear()

        try {
            // 刷新时禁用缓存，确保获取最新数据
            RetrofitClient.setUseCache(false)

            val response = RetrofitClient.apiService.get_feed_packahe_list(page = 1)
            Log.d(TAG, "刷新成功: count=${response.count}, results数量=${response.results.size}")

            // 添加数据到集合（自动去重）
            allFeedPackages.addAll(response.results)

            // 更新分页状态
            val hasNext = !response.next.isNullOrEmpty()
            paginationState = paginationState.loadSuccess(hasNext, response.count)

            // 发送数据更新
            val paginatedData = PaginatedData(
                items = allFeedPackages.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)

        } catch (e: Exception) {
            Log.e(TAG, "刷新失败: ${e.message}", e)
            paginationState = paginationState.loadError(e.message ?: "刷新失败")

            val paginatedData = PaginatedData(
                items = allFeedPackages.toList(),
                paginationState = paginationState
            )
            paginatedDataObserver.postValue(paginatedData)
            throw e
        } finally {
            // 恢复缓存设置
            RetrofitClient.setUseCache(true)
        }
    })

    /**
     * 编辑饲料包信息
     */
    fun edit_feed_package(feedPackageJson: FeedPackageJson) = launch({
        Log.d(TAG, "开始编辑饲料包: ${feedPackageJson.name}")
        try {
            val response = RetrofitClient.apiService.edit_feed_packahe(feedPackageJson)
            Log.d(TAG, "饲料包编辑请求成功: code=${response.code}")
            packageEditObserver.postValue(response)
        } catch (e: Exception) {
            Log.e(TAG, "饲料包编辑请求失败: ${e.message}", e)
            throw e
        }
    })

    /**
     * 获取当前分页状态
     */
    fun getCurrentPaginationState(): PaginationState = paginationState
}